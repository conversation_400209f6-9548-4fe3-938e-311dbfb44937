fanme-shop-batch:
  quarkus_profile: "dev2"

env: dev2

cronjobs:
  - id : "0"
    name: monthly-seller-sales
    schedule: "0 15 * * *"
    main_class: MonthlySellerSalesBatchMain
  - id : "1"
    name: monthly-tenant-sales
    schedule: "0 15 * * *"
    main_class: MonthlyTenantSalesBatchMain
  - id : "2"
    name: monthly-seller-sales-merge
    schedule: "0 16 15 * *"
    main_class: MonthlySellerSalesMergeMain
  - id : "3"
    name: monthly-tenant-sales-merge
    schedule: "0 16 15 * *"
    main_class: MonthlyTenantSalesMergeMain
#  - id : "4"
#    name: seller-gmo-transfer-status-update
#    schedule: "0 3/6 * * *"
#    main_class: SellerGmoTransferStatusUpdateMain
  - id : "4"
    name: seller-sales-expire
    schedule: "0 17 * * *"
    main_class: SellerSalesExpireMain
  - id : "5"
    name: send-cvs-payment-reminder-email
    schedule: "55 8,14 * * *"
    main_class: SendCvsPaymentReminderEmailBatchMain
  - id : "6"
    name: transfer-deadline
    schedule: "0 3 * * *"
    main_class: TransferDeadLineContactBatchMain
  - id : "7"
    name: order-delivery-info-csv
    schedule: "0 20 * * *"
    main_class: OrderDeliveryInfoCsvBatchMain
  - id : "8"
    name: order-print-gacha-delivery-data
    schedule: "0 21 * * *"
    main_class: OrderPrintGachaDeliveryDataBatchMain
