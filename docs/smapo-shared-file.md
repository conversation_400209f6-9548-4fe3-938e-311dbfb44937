# Smapo データ共有仕様書

## 概要

本ドキュメントでは、fanme-shopからSmapoシステムへのデータ共有について記載します。
チェキ（CHEKI）とプリントガチャ（PRINT_GACHA）の2つの商品タイプについて、バッチ処理によりCSVファイルと画像ファイルを共有しています。

---

## 1. バッチ処理概要

### 1.1 実行頻度
- **手動実行**：運用者がコマンドラインから任意のタイミングで実行
- **実行単位**：日次（指定した日付の購入データを処理）

### 1.2 バッチ一覧

| バッチ名 | 処理対象 | 主要機能 |
|---|---|---|
| OrderDeliveryInfoCsvBatch | チェキ購入データ | CSV生成・アップロード |
| OrderPrintGachaDeliveryDataBatch | プリントガチャ購入データ | CSV生成・アップロード + 画像転送 |

---

## 2. チェキ（CHEKI）データ共有

### 2.1 処理フロー

1. **データ取得**
   - 指定日（JST 0:00〜23:59）の購入完了データを取得
   - 対象：`ItemType.CHEKI`
   - 条件：`PurchasedItemStatus.PAYSUCCESS`

2. **CSV生成**
   - 通常チェキCSV：【prefix】なしの商品
   - 物理アイテムCSV：【prefix】ありの商品（例：【ポスター】、【グッズ】等）

3. **ファイルアップロード**
   - S3アップロード：両方のCSV
   - GCPアップロード：通常チェキCSVのみ

### 2.2 ファイル配置

#### 通常チェキCSV
```
S3:  ${envKind}/2025/8/4/cheki_purchase_data20250804.csv
GCP: fanme/2025/8/4/cheki_purchase_data20250804.csv
```

#### 物理アイテムCSV
```
S3:  ${envKind}/2025/8/4/physical_item_purchase_data20250804.csv
GCP: アップロードなし
```

### 2.3 CSVカラム構造

**ファイル名**: `cheki_purchase_data{YYYYMMDD}.csv`

| カラム名 | 型 | 説明 | 例 |
|---|---|---|---|
| オーダーID | 数値 | 注文ID | 12345 |
| 商品ID | 数値 | 商品ID | 67890 |
| 商品タイプ | 文字列 | 固定値「チェキ」 | チェキ |
| 商品名 | 文字列 | 商品名 | 田中太郎のチェキ |
| 購入数 | 数値 | 購入数量 | 3 |
| 商品単価 | 数値 | 商品の単価（円） | 1000 |
| 合計 | 数値 | 単価×数量 | 3000 |
| 購入日時 | 文字列 | JST形式 | 2025-08-04 14:30:00 |
| 名前（購入者） | 文字列 | 姓名 | 山田 太郎 |
| 郵便番号（購入者） | 文字列 | 7桁 | 1500043 |
| 住所（購入者） | 文字列 | 都道府県+市区町村+町名+建物名 | 東京都渋谷区道玄坂スタブビル 101 |
| 電話番号（購入者） | 文字列 | 先頭に'を付与 | '09012345678 |

---

## 3. プリントガチャ（PRINT_GACHA）データ共有

### 3.1 Feature Toggle
**重要**: 本バッチはfeature toggleで制御されています。

```properties
# application.properties
config.shop.feature-toggle-print-gacha=false  # demo/local以外では無効
```

- **有効環境**: demo, local
- **無効時の動作**: バッチ開始直後にスキップしてログ出力

### 3.2 処理フロー

1. **Feature Toggle確認**
   - 無効の場合は処理をスキップ

2. **データ取得**
   - 指定日（JST 0:00〜23:59）の購入完了データを取得
   - 対象：`ItemType.PRINT_GACHA`
   - 条件：`PurchasedItemStatus.PAYSUCCESS`

3. **CSV生成**
   - ガチャで受け取ったファイルごとに行を生成
   - 同一ファイルの重複は数量としてカウント

4. **ファイルアップロード**
   - CSVをS3とGCPの両方にアップロード

5. **画像転送**
   - **初回のみ**: S3からGCPに画像ファイルを転送
   - 転送済み確認：GCP上のファイル存在チェック

### 3.3 ファイル配置

#### CSV
```
S3:  ${envKind}/2025/8/4/print_gacha_purchase_data20250804.csv
GCP: fanme/2025/8/4/print_gacha_purchase_data20250804.csv
```

#### 画像ファイル
```
GCP: fanme/images/items/{itemId}/{fileId}.png
```
例：
```
GCP: fanme/images/items/12345/67890.png
```

### 3.4 CSVカラム構造

**ファイル名**: `print_gacha_purchase_data{YYYYMMDD}.csv`

| カラム名 | 型 | 説明 | 例 |
|---|---|---|---|
| オーダーID | 数値 | 注文ID | 12345 |
| 購入日時 | 文字列 | JST形式 | 2025-08-04 14:30:00 |
| 商品名 | 文字列 | プリントガチャ商品名 | 田中太郎のプリントガチャ |
| 郵便番号（購入者） | 文字列 | 7桁 | 1500043 |
| 住所（購入者） | 文字列 | 都道府県+市区町村+町名+建物名 | 東京都渋谷区道玄坂スタブビル 101 |
| 名前（購入者） | 文字列 | 姓名 | 山田 太郎 |
| 画像ID | 文字列 | itemId_fileId形式 | 12345_67890 |
| 商品単価 | 数値 | 商品の単価（円） | 500 |
| 購入数 | 数値 | 同一ファイルの取得数 | 2 |
| 画像URL | 文字列 | GCP上の画像URL | {bucket}/fanme/images/items/12345/67890.png |

---

## 4. 共通仕様

### 4.1 環境別設定

| 環境 | envKind | S3バケット | GCPバケット |
|---|---|---|---|
| 開発 | dev | fanme-dev-shop | 設定により変動 |
| 本番 | prod | （本番設定） | （本番設定） |

### 4.2 エラーハンドリング

1. **データなしの場合**
   - ログ出力して正常終了
   - 空のCSVファイルは作成しない

2. **顧客情報なしの場合**
   - 該当データをスキップ
   - 警告ログを出力

3. **アップロード失敗**
   - エラーログを出力
   - 例外をスローして処理中断

### 4.3 ファイル命名規則

```
{type}_purchase_data{YYYYMMDD}.csv
```
- `type`: cheki | physical_item | print_gacha
- `YYYYMMDD`: 処理対象日（JST）

### 4.4 日付ディレクトリ構造

```
{year}/{month}/{day}/
```
例：2025年8月4日 → `2025/8/4/`

---

## 5. 運用注意事項

### 5.1 プリントガチャ画像転送

- **初回転送**: 商品の初回購入時のみ実行
- **重複回避**: GCP上のファイル存在確認で制御
- **転送失敗**: ログ出力のみで処理継続

### 5.2 Feature Toggle管理

プリントガチャバッチは本番環境では無効化されています。
有効化する場合は以下を実施：

1. `application.properties`でfeature toggle有効化
2. 動作確認後にデプロイ
3. 必要に応じて定期実行設定

### 5.3 データ整合性

- **タイムゾーン**: JSTベースで日付指定
- **購入状態**: `PAYSUCCESS`のみが対象
- **顧客情報**: Salesforce連携によるリアルタイム取得

---

## 6. トラブルシューティング

### 6.1 よくある問題

**Q: プリントガチャバッチがスキップされる**
A: feature toggleが無効になっています。`config.shop.feature-toggle-print-gacha`を確認してください。

**Q: CSVが空になる**
A: 指定日に購入データが存在しない、または決済完了していない可能性があります。

**Q: 画像転送されない**
A: 既にGCP上に同一ファイルが存在する場合は転送されません（正常動作）。

### 6.2 確認ポイント

1. バッチ実行時の引数確認
2. 処理対象データの存在確認
3. feature toggle設定確認（プリントガチャ）
4. ファイルアップロード成功確認
5. エラー発生時の詳細確認