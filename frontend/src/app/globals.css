@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --black: #000000;
  --white: #ffffff;
  --white-transparent: rgba(255, 255, 255, 0.4);
  --black-transparent: rgba(0, 0, 0, 0.4);
  --mask-transparent: rgba(34, 34, 34, 0.7);
  --white-mask: rgba(255, 255, 255, 0.8);
  --red: #ff5050;
  --purple: #8c1ee6;
  --shadow: rgba(0, 0, 0, 0.1);
  --header-shadow: rgba(0, 0, 0, 0.08);
  --toast-success-shadow: rgba(0, 230, 155, 0.5);
  --toast-error-shadow: rgba(255, 80, 80, 0.5);
  --toast-info-shadow: rgba(22, 126, 251, 0.5);
  --toast-warning-shadow: rgba(246, 135, 32, 0.5);
  --gray-50: #f8f8f8;
  --gray-100: #f2f2f2;
  --gray-150: #ebebeb;
  --gray-200: #dedede;
  --gray-300: #d9d9d9;
  --gray-400: #cecece;
  --gray-500: #999;
  --gray-550: #666;
  --gray-600: #484848;
  --gray-650: #3a3a3a;
  --gray-700: #333;
  --gray-800: #222;
  --navy-50: #eff3f5;
  --navy-100: #e5eaee;
  --navy-200: #d1dadf;
  --pink-50: #ff698d;
  --pink-100: #ff4874;
  --pink-200: #de3a61;
  --pink-300: #ff55af;
  --blue-50: #e4f6ff;
  --blue-100: #00b9e6;
  --blue-150: #10b2fc;
  --blue-160: #0aaafa;
  --blue-200: #167efb;
  --blue-300: #0050d2;
  --green-10: #f3f7ed;
  --green-50: #00e69b;
  --green-100: #34c759;
  --green-150: #6ccb85;
  --green-200: #00b74a;
  --green-250: #63AE41;
  --green-300: #21a142;
  --yellow-10: #fffaee;
  --yellow-50: #fff4d2;
  --yellow-100: #ffe070;
  --yellow-200: #ffd814;
  --yellow-300: #ffc814;
  --brown-50: #dfb620;
  --brown-100: #c19e22;
  --orange-50: #ffaf5b;
  --orange-100: #ff9a30;
  --orange-200: #f68720;
  --orange-300: #ffa800;

  --swiper-pagination-bottom: 0;
  --swiper-navigation-size: 20px !important;

  --ranking-first-border: #ac2020;
  --ranking-first-decoration: #8c1010;
  --ranking-first-avatar-border: #dabd25;
  --ranking-second-border: #2059ae;
  --ranking-second-decoration: #2059ae;
  --ranking-second-avatar-border: #93a3bc;
  --ranking-third-border: #5b9019;
  --ranking-third-decoration: #558518;
  --ranking-third-avatar-border: #c18955;
  --ranking-highlight-bg: #ffe600;
}
html {
  background-color: var(--white);
}

body {
  color: var(--gray-800);
  background: var(--white);
}
.text-shadow {
  color: var(--white);
  text-shadow: 1px 1px 3px var(--mask-transparent);
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--black-transparent); /* 50%透明の黒色 */
  z-index: -1; /* 背景として表示 */
}

.blurIconTitle::before {
  content: '';
  background-image: url('/shop/images/icons/Blur.svg');
  position: absolute;
  box-sizing: content-box;
  width: 24px;
  height: 24px;
  left: 4px;
}

.maskIconTitle::before {
  content: '';
  background-image: url('/shop/images/icons/setting/Mask.svg');
  position: absolute;
  box-sizing: content-box;
  width: 20px;
  height: 20px;
  left: 6px;
  top: 50%;
  transform: translateY(-50%);
}

.transparent-bg {
  background-image: url('/shop/images/empty.svg');
  background-repeat: repeat;
}

.ranking-checkered-bg {
  background-color: #f8f8f8;
  background-image:
    linear-gradient(45deg, #ebebeb 25%, transparent 25%), linear-gradient(-45deg, #ebebeb 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #ebebeb 75%), linear-gradient(-45deg, transparent 75%, #ebebeb 75%);
  background-size: 20px 20px;
  background-position:
    0 0,
    0 10px,
    10px -10px,
    -10px 0px;
}

.accordion {
  display: grid;
  grid-template-rows: 0fr;
  transition: grid-template-rows 0.3s ease-out;
}

.accordion.open {
  grid-template-rows: 1fr;
}

.triangle-right {
  width: 0px;
  height: 0px;
  border-style: solid;
  border-width: 10px 0 10px 17.3px;
  border-color: transparent transparent transparent var(--gray-800);
  transform: rotate(0deg);
}

.triangle-right-yellow {
  width: 0;
  height: 0;
  border-style: solid;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 9px solid var(--yellow-100);
  border-right: 0;
}

.triangle-right-white {
  width: 0;
  height: 0;
  border-style: solid;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-left: 5.33px solid #fffafa;
  border-right: 0;
}
.triangle-down {
  width: 0;
  height: 0;
  border-style: solid;
  border-right: 12px solid transparent;
  border-left: 12px solid transparent;
  border-top: 18px solid #d9d9d9;
  border-bottom: 0;
}

.gradient-to-top-purple {
  background: linear-gradient(to top, var(--purple), var(--pink-300), var(--yellow-300));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.gradient-complete-text {
  background: linear-gradient(
    83.38deg,
    var(--green-50) 5.2%,
    var(--blue-100) 23.12%,
    var(--blue-300) 41.04%,
    var(--purple) 58.96%,
    var(--pink-300) 76.88%,
    var(--yellow-300) 94.8%
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.08);
  letter-spacing: 0.08rem;
}

/* Progress bar gradients for each rank */
/* S rank gradients for different progress ranges */
.progress-bar-s-20 {
  background: linear-gradient(to right, var(--yellow-300), var(--pink-300));
}

.progress-bar-s-40 {
  background: linear-gradient(to right, var(--yellow-300), var(--pink-300), var(--purple));
}

.progress-bar-s-60 {
  background: linear-gradient(to right, var(--yellow-300), var(--pink-300), var(--purple), var(--blue-300));
}

.progress-bar-s-80 {
  background: linear-gradient(
    to right,
    var(--yellow-300),
    var(--pink-300),
    var(--purple),
    var(--blue-300),
    var(--blue-100)
  );
}

.progress-bar-s-100 {
  background: linear-gradient(
    to right,
    var(--yellow-300),
    var(--pink-300),
    var(--purple),
    var(--blue-300),
    var(--blue-100),
    var(--green-50)
  );
}

/* A rank gradients for different progress ranges */
.progress-bar-a-50 {
  background: linear-gradient(to right, #feec3f, #ffb91e);
}

.progress-bar-a-100 {
  background: linear-gradient(to right, #feec3f, #ff8c00);
}

/* B rank gradients for different progress ranges */
.progress-bar-b-50 {
  background: linear-gradient(to right, #c0daff, #8cbcff);
}

.progress-bar-b-100 {
  background: linear-gradient(to right, #c0daff, #5a9fff);
}

/* C rank gradients for different progress ranges */
.progress-bar-c-50 {
  background: linear-gradient(to right, #f1e1d8, #d2a287);
}

.progress-bar-c-100 {
  background: linear-gradient(to right, #f1e1d8, #b4653a);
}

.progress-bar-complete {
  background: linear-gradient(
    to right,
    var(--green-50),
    var(--blue-100),
    var(--blue-300),
    var(--purple),
    var(--pink-300),
    var(--yellow-300)
  );
}

.gradient-to-right-blue {
  background: linear-gradient(to right, var(--blue-300), var(--blue-100), var(--green-50));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

input[type='datetime-local'] {
  padding: 0 8px 0 12px;
  text-align: end;
}
input[type='datetime-local']::-webkit-calendar-picker-indicator {
  display: none;
}
input[type='datetime-local']:valid {
  border: none;
}
input[type='datetime-local'] + p {
  display: none;
}
input[type='datetime-local']:invalid {
  border: 1px solid var(--red);
}
input[type='datetime-local']:invalid + p {
  display: block;
}
input:focus {
  border: none;
  outline: none; /* フォーカス時の枠線を削除 */
}

input::placeholder {
  color: var(--gray-400);
  font-size: 13px;
  font-weight: 400;
  text-align: center;
}

/* width */
.w-full-with-padding {
  width: calc(100% - 32px);
}
.min-w-full-with-padding {
  min-width: calc(100% - 32px);
}

/* swiper */
.thumbnailSwiper {
  min-height: 488px;
}
@media (max-width: 430px) {
  .thumbnailSwiper {
    min-height: calc(100vw + 10px);
  }
}
@media (max-width: 374px) {
  .thumbnailSwiper {
    min-height: calc(100vw + 28px);
  }
}

.thumbnailSwiper .swiper-pagination-bullet,
.instructions-swiper .swiper-pagination-bullet {
  width: 8px;
  height: 8px;
  background-color: var(--gray-400);
}
.thumbnailSwiper .swiper-pagination-bullet-active,
.instructions-swiper .swiper-pagination-bullet-active {
  background-color: var(--gray-800);
}

.gacha-review-swiper .swiper-button-prev,
.gacha-review-swiper .swiper-button-next {
  width: 28px;
  height: 28px;
  background-color: var(--white-mask);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--gray-800);
}
.gacha-review-swiper .swiper-button-prev::after {
  content: '';
  width: 16px;
  height: 16px;
  background-image: url('/shop/images/icons/Arrow_Back.svg');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.gacha-review-swiper .swiper-button-next::after {
  content: '';
  width: 16px;
  height: 16px;
  background-image: url('/shop/images/icons/Arrow_Back.svg');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  transform: rotate(180deg);
}

.slide-in-animation {
  animation: slideInFromTop 0.3s forwards;
}

@keyframes slideInFromTop {
  0% {
    transform: translateX(-50%) translateY(-20px);
    opacity: 0;
  }
  100% {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

/* scroll ball animation */
@keyframes scrollBall {
  0% {
    top: 0;
    opacity: 0;
  }
  10% {
    top: 0;
    opacity: 1;
  }
  45% {
    top: 50%;
    opacity: 1;
  }
  55% {
    top: 50%;
    opacity: 0;
  }
  100% {
    top: 0;
    opacity: 0;
  }
}

.scroll-ball {
  animation: scrollBall 1.5s ease-in-out infinite;
}

/* CSS */
.ripple-button {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  outline: none;
}

.ripple-button .ripple {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.6);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
  pointer-events: none;
}

.gradient-purple-pink-yellow {
  background: linear-gradient(5deg, var(--purple), var(--pink-300), var(--yellow-300));
}

.gradient-blue-teal-green {
  background: linear-gradient(100deg, var(--blue-300), var(--blue-100), var(--green-50));
}

.swiper-pagination-bullet-white {
  background-color: var(--white-transparent);
}
.swiper-pagination-bullet-white-active {
  background-color: var(--white);
}
.fullscreenSwiper .swiper-button-prev,
.fullscreenSwiper .swiper-button-next {
  color: var(--white);
  width: 40px;
  height: 40px;
  background-color: var(--black-transparent);
}
.fullscreenSwiper .swiper-button-prev {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  left: 0;
}
.fullscreenSwiper .swiper-button-next {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  right: 0;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}
.ripple-button:disabled {
  cursor: not-allowed;
  background-color: var(--gray-200);
}
.section-double-border {
  border-bottom: 1px solid var(--white);
  position: relative;
}

.section-double-border::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: var(--navy-200);
}
@layer components {
  .custom-radio-label {
    @apply relative cursor-pointer pl-9;
  }

  .custom-radio-label::before,
  .custom-radio-label::after {
    content: '';
    @apply absolute top-1/2 block -translate-y-1/2 rounded-full;
  }

  .custom-radio-label::before {
    @apply left-[5px] h-7 w-7 rounded-full border-none bg-gray-200;
  }

  .custom-radio-label::after {
    @apply left-[11px] h-4 w-4 rounded-full bg-gray-200 opacity-100 transition-colors duration-300 ease-in-out;
  }

  .custom-radio:checked + .custom-radio-label::after {
    @apply left-[11px] h-4 w-4 rounded-full bg-white opacity-100 transition-colors duration-300 ease-in-out;
  }

  .custom-radio:disabled + .custom-radio-label::after {
    @apply left-[11px] h-4 w-4 rounded-full bg-gray-50 opacity-100 transition-colors duration-300 ease-in-out;
  }

  .fanme-checkbox + label::before {
    content: '';
    background-image: url('/shop/images/icons/CheckWhite.svg');
    position: absolute;
    box-sizing: content-box;
    width: 24px;
    height: 24px;
    top: -6px;
    left: 0;
    border-radius: 4px;
    background-color: var(--gray-200);
    border: 2px solid var(--gray-200);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .fanme-checkbox:checked + label::after {
    content: '';
    background-image: url('/shop/images/icons/CheckWhite.svg');
    position: absolute;
    box-sizing: content-box;
    width: 24px;
    height: 24px;
    left: 0;
    top: -6px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    background-color: var(--gray-700);
    border: 2px solid var(--gray-600);
    color: var(--white);
  }
  .fanme-checkbox:disabled + label::before {
    content: '';
    background-image: url('/shop/images/icons/CheckWhite.svg');
    position: absolute;
    box-sizing: content-box;
    width: 24px;
    height: 24px;
    left: 0;
    top: -6px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    background-color: var(--gray-50);
    border: 2px solid var(--gray-50);
    color: var(--white);
  }
  .label-disabled::after {
    background-color: var(--gray-200) !important;
    border: 2px solid var(--gray-200) !important;
  }
}

@layer utilities {
  .toast-success::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: 16px;
    height: 16px;
    background-image: url('/shop/images/icons/Check.svg');
    background-size: cover;
    background-position: center;
    z-index: 1;
  }

  .toast-error::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: 16px;
    height: 16px;
    background-image: url('/shop/images/icons/ErrorRed.svg');
    background-size: cover;
    background-position: center;
    z-index: 1;
  }

  .toast-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: 16px;
    height: 16px;
    background-image: url('/shop/images/icons/InfoBlue.svg');
    background-size: cover;
    background-position: center;
    z-index: 1;
  }

  .toast-warning::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: 16px;
    height: 16px;
    background-image: url('/shop/images/icons/ExclamationOrange.svg');
    background-size: cover;
    background-position: center;
    z-index: 1;
  }
}

.drag-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  touch-action: none;
}

.zoomed-image {
  transition: transform 0.3s ease-out;
  will-change: transform;
}
.viewer-images-modal .yarl__button.yarl__navigation_prev,
.viewer-images-modal .yarl__button.yarl__navigation_next {
  filter: unset;
  background-color: var(--mask-transparent) !important;
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100%;
}
.viewer-images-modal .yarl__button.yarl__navigation_next:has(img.pc),
.viewer-images-modal .yarl__button.yarl__navigation_prev:has(img.pc) {
  width: 48px;
  height: 48px;
}
.yarl__navigation_prev {
  left: 8px !important;
}
.yarl__navigation_next {
  right: 8px !important;
}
.viewer-images-modal {
  max-width: 480px;
  left: 0;
  right: 0;
  transform: none;
  top: 0;
  bottom: 0;
  margin: auto;
}
.viewer-images-modal.landscape {
  max-width: 100%;
}

.viewer-images-modal .yarl__container {
  background-color: var(--gray-700) !important;
}
.viewer-images-modal .yarl__toolbar {
  width: 100%;
  height: 56px;
  display: grid;
  grid-auto-flow: column;
  grid-template-columns: repeat(7, minmax(0, 1fr));
  align-items: center;
  justify-content: unset;
  gap: 1rem;
  padding: 0 10px;
}
.viewer-images-modal .yarl__toolbar > :last-child {
  grid-column-start: 7;
  justify-self: end;
}

@media screen and (orientation: landscape) {
  .viewer-images-modal.landscape .yarl__toolbar {
    grid-template-columns: repeat(18, minmax(0, 1fr));
  }
  .viewer-images-modal.landscape .yarl__toolbar > :last-child {
    grid-column-start: 18;
    justify-self: end;
  }
}

.viewer-images-modal .yarl__toolbar .yarl__button:disabled .zoom-btn {
  opacity: 0.5;
}

.viewer-images-modal.controls-hidden .yarl__toolbar,
.viewer-images-modal.controls-hidden .yarl__navigation_prev,
.viewer-images-modal.controls-hidden .yarl__navigation_next {
  opacity: 0;
  transition: opacity 300ms ease-in-out;
}

.viewer-images-modal.controls-visible .yarl__toolbar,
.viewer-images-modal.controls-visible .yarl__navigation_prev,
.viewer-images-modal.controls-visible .yarl__navigation_next {
  opacity: 1;
  transition: opacity 300ms ease-in-out;
}

@keyframes slideInFromTop {
  0% {
    transform: translateY(-50%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.slide-in-animation {
  animation: slideInFromTop 0.2s ease forwards;
}

.glow-white {
  color: var(--white);
  text-shadow:
    0 0 10px rgba(255, 255, 255, 0.8),
    0 0 20px rgba(255, 255, 255, 0.6),
    0 0 40px rgba(255, 255, 255, 0.4);
}

/* 白い縁取りテキスト用のクラス */
.text-white-outline {
  text-shadow:
    1px 1px 0 white,
    -1px -1px 0 white,
    1px -1px 0 white,
    -1px 1px 0 white,
    0 1px 0 white,
    1px 0 0 white,
    0 -1px 0 white,
    -1px 0 0 white;
}

/* より強い縁取り効果 */
.text-white-outline-bold {
  text-shadow:
    2px 2px 0 white,
    -2px -2px 0 white,
    2px -2px 0 white,
    -2px 2px 0 white,
    0 2px 0 white,
    2px 0 0 white,
    0 -2px 0 white,
    -2px 0 0 white,
    1px 1px 0 white,
    -1px -1px 0 white,
    1px -1px 0 white,
    -1px 1px 0 white,
    0 1px 0 white,
    1px 0 0 white,
    0 -1px 0 white,
    -1px 0 0 white;
}

/* クロップエリアの虚線内枠 */
.crop-area-with-dashed-inner::before {
  content: '';
  position: absolute;
  top: 11px;
  left: 11px;
  right: 11px;
  bottom: 11px;
  border: 2px dashed red;
  pointer-events: none;
  z-index: 1;
}

/* Quantity Slider */
.quantity-slider {
  background: linear-gradient(to right, var(--yellow-200) 0%, var(--yellow-200) var(--progress, 0%), var(--gray-150) var(--progress, 0%), var(--gray-150) 100%);
}

.quantity-slider::-webkit-slider-thumb {
  appearance: none;
  height: 1.5rem;
  width: 1.5rem;
  border-radius: 50%;
  background: var(--yellow-200);
  cursor: pointer;
  box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.15);
  transition: transform 0.2s ease;
}

.quantity-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
}

.quantity-slider::-moz-range-thumb {
  height: 1.5rem;
  width: 1.5rem;
  border-radius: 50%;
  background: var(--yellow-200);
  cursor: pointer;
  border: none;
  box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.15);
  transition: transform 0.2s ease;
}

.quantity-slider::-moz-range-thumb:hover {
  transform: scale(1.1);
}

.quantity-slider:disabled::-webkit-slider-thumb {
  background: var(--gray-500);
  cursor: not-allowed;
}

.quantity-slider:disabled::-moz-range-thumb {
  background: var(--gray-500);
  cursor: not-allowed;
}
