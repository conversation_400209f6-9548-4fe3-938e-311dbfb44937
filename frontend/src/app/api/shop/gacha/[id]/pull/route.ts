import { NextRequest, NextResponse } from 'next/server';
import type { FileForPullGachaItems, ItemForGetItem } from '@/lib/fanme-api/fanme-api.schemas';
import { createAxiosClient } from '@/app/api/_client';
import { apiAuth } from '@/app/api/utils/auth';
import { snakeToCamelObject } from '@/app/api/utils/formatConvert';
import { buildCurrentPulledFiles, buildGachaCollection } from '@/utils/gacha-helpers';

export async function POST(req: NextRequest, { params }: { params: { id: string } }) {
  const client = createAxiosClient(req);
  const isAuth = await apiAuth();
  if (!isAuth) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const id = params.id;
  const body = await req.json();
  const { identityId } = body;
  if (!identityId) {
    return NextResponse.json({ error: 'Missing identityId', statusText: 'error' }, { status: 400 });
  }

  try {
    const currentPulledResponseRaw = await client.post('/shops/gacha/pull', { itemId: Number(id) });
    const files = currentPulledResponseRaw.data?.data?.files ?? [];
    // TODO: createAxiosClientの中でキャメルケースへの変換を行い、呼び出し元での返還とアサーションを削除
    const currentPulledFilesFromApi = files.map((f: any) => snakeToCamelObject(f)) as FileForPullGachaItems[];

    const gachaCollectionResponseRaw = await client.get(`/shops/${identityId}/items/${id}`);
    // TODO: createAxiosClientの中でキャメルケースへの変換を行い、呼び出し元での返還とアサーションを削除
    const gachaCollectionItem = snakeToCamelObject(gachaCollectionResponseRaw.data?.data?.item) as ItemForGetItem;
    const gachaCollection = buildGachaCollection(gachaCollectionItem, id);
    const currentPulledFiles = buildCurrentPulledFiles(currentPulledFilesFromApi, gachaCollectionItem.files ?? []);

    const response = {
      currentPulledFiles,
      item: gachaCollection,
    };
    return NextResponse.json({
      data: response,
      statusText: 'success',
    });
  } catch (error: any) {
    console.error(JSON.stringify({ error }));
    return NextResponse.json({ error: error.message || 'Failed to pull gacha', statusText: 'error' }, { status: 500 });
  }
}
