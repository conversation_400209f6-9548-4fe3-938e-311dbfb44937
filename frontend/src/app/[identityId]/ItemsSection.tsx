'use client';
import React from 'react';
import NewItemSection from './NewItemSection';
import { getIsNowForSale } from '@/utils/item';
import { ShopItemType } from '@/types/shopItem';

interface ItemsSectionProps {
  identityId: string;
  items: ShopItemType[];
}

const ItemsSection = ({ identityId, items }: ItemsSectionProps) => {
  const forSaleItems = items.filter((item) => {
    if (!item.available) return false;
    if (!item.forSale) return true;
    return getIsNowForSale(item.forSale);
  });
  return (
    <div className="my-2 flex flex-col items-center justify-center gap-8 px-3">
      <NewItemSection items={forSaleItems} identityId={identityId} />
    </div>
  );
};

export default ItemsSection;
