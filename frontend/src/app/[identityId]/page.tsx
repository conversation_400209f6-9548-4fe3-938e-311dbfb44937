import { notFound, redirect } from 'next/navigation';
import { ParamsCleaner } from '@/components/common/CommonComponents/ParamsCleaner';
import { getShop } from '@/lib/server-api/shop-endpoint/shop-endpoint';
import ClientSideShopTop from './ClientSideShopTop';
import { getItems } from '@/app/actions/shopItems';
import { getCurrentUser } from '@/app/actions/user';
import { getUserIdentityId } from '@/utils/base';
import { getCookie } from '@/utils/cookie';
import { ShopItemType } from '@/types/shopItem';

const ShopPage = async ({ params }: { params: { identityId: string } }) => {
  const identityId = getUserIdentityId(params.identityId);
  const shopItemsJson = (await (async () => {
    try {
      const res = await getItems(identityId);
      const json = await res.json();
      return json.data ? (json.data as ShopItemType[]) : [];
    } catch (error) {
      console.error(JSON.stringify({ error }));
      return [];
    }
  })()) as any[];

  const shopResponseBody = await (async () => {
    try {
      return await getShop(identityId);
    } catch {
      const token = await getCookie('fanme_token-http-only');
      if (token) {
        const res = await getCurrentUser(token);
        const currentUser = await res.json();
        if (currentUser.data?.accountIdentity === identityId) {
          redirect(`/create`);
        }
      }
      return notFound();
    }
  })();

  if (!shopResponseBody.data) {
    return notFound();
  }

  return (
    <>
      <ParamsCleaner />
      <ClientSideShopTop shopData={shopResponseBody.data} shopItems={shopItemsJson} identityId={identityId} />
    </>
  );
};

export default ShopPage;
