import React from 'react';
import Accordion from '@/components/atoms/accordion';
import CreatorInfo from '@/components/containers/creator-info';
import GachaReceivedItemsSection from '@/components/containers/gacha-received-items-section';
import GachaCollectionSection from '@/components/containers/GachaCollectionSection';
import GachaCompleteFooterSection from '@/components/containers/GachaCompleteFooterSection';
import GachaCompleteSection from '@/components/containers/GachaCompleteSection';
import { PurchasedItemDetail, ShopForGetShop } from '@/lib/fanme-api/fanme-api.schemas';
import BenefitsSection from '@/app/[identityId]/purchased-item/[purchasedItemId]/benefit-section';
import ItemDetailSection from '@/app/[identityId]/purchased-item/[purchasedItemId]/item-detail-section';
import NotesSection from '@/app/[identityId]/purchased-item/[purchasedItemId]/notes-section';
import PaymentReceipt from '@/app/[identityId]/purchased-item/[purchasedItemId]/payment-receipt';
import PurchaseOptionSection from '@/app/[identityId]/purchased-item/[purchasedItemId]/purchase-option-section';
import PurchaseStatusSection from '@/app/[identityId]/purchased-item/[purchasedItemId]/purchase-status-section';
import { isPrintGacha } from '@/utils/itemTypes';
import { GachaItem } from '@/types/gacha';

type DigitalBundlePurchasedItemProps = {
  shop: ShopForGetShop;
  item: GachaItem['item'];
  purchasedItemDetail: PurchasedItemDetail;
  isPaySucceeded: boolean;
};

const GachaPurchasedItem = ({ shop, item, purchasedItemDetail, isPaySucceeded }: DigitalBundlePurchasedItemProps) => {
  if (!purchasedItemDetail || !purchasedItemDetail.itemId) throw new Error('purchasedItem not found');
  if (!item || !item.id) throw new Error('item not found');
  if (!shop) throw new Error('shop not found');

  return (
    <div className="flex flex-col items-center justify-center">
      <div className="flex w-full flex-col gap-3 px-4 py-3">
        <CreatorInfo
          creatorName={shop.creatorName}
          identityId={shop.creatorAccountIdentity}
          creatorIcon={shop.creatorIconUri}
        />
      </div>
      <div className="w-full px-4">
        {!isPaySucceeded && <PurchaseStatusSection purchasedItemDetail={purchasedItemDetail} />}
      </div>
      <div className="flex w-full flex-col">
        <GachaCompleteSection
          props={{
            isPreview: false,
            totalGachaCount: item.itemFiles.length,
            collectedUniqueItemsCount: item.collectedUniqueItemsCount,
            isCompleted: item.isCompleted,
            thumbnail: item.thumbnail,
            itemId: Number(item.id),
          }}
        />
        <GachaCollectionSection
          isPreview={false}
          itemFiles={item.itemFiles}
          itemId={Number(item.id)}
          isPrintGacha={isPrintGacha(item)}
        />
        <GachaCompleteFooterSection itemId={Number(item.id)} identityId={shop.creatorAccountIdentity} />
        <GachaReceivedItemsSection purchasedItemId={purchasedItemDetail.id!} />
        {purchasedItemDetail.purchaserComment && <PurchaseOptionSection purchasedItemDetail={purchasedItemDetail} />}
        <div className="flex w-full flex-col gap-3 py-5">
          <div className="flex w-full flex-col gap-3">
            <ItemDetailSection name={item.title} description={item.description} />
            {isPaySucceeded && item.benefits && item.benefits.length > 0 && (
              <BenefitsSection itemId={item.id?.toString()} benefits={item.benefits} />
            )}
          </div>
          <div className="w-full px-4">
            <Accordion icon={'/images/icons/CreditScore.svg'} title={'決済情報'} type="white" showDivider={false}>
              <PaymentReceipt purchasedItemDetail={purchasedItemDetail} />
            </Accordion>
          </div>
          <div className="w-full px-4">
            <Accordion
              icon={'/images/icons/ErrorOutline.svg'}
              title={'デジタル商品の注意事項'}
              type="white"
              showDivider={false}
            >
              <NotesSection />
            </Accordion>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GachaPurchasedItem;
