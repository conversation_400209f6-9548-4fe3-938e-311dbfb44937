'use client';
import { PurchasedItemDetail } from '@/lib/server-api/shop-api.schemas';
import { formatDateTime } from '@/utils/base';

interface PaymentReceiptProps {
  purchasedItemDetail: PurchasedItemDetail;
}

const PaymentReceipt = ({ purchasedItemDetail }: PaymentReceiptProps) => {
  const getPaymentTypeString = (paymentType: string) => {
    switch (paymentType) {
      case 'credit_card':
        return 'クレジットカード';
      case 'convenience':
        return 'コンビニ決済';
      case 'pay_pay':
        return 'PayPay';
      case 'apple_pay':
        return 'Apple Pay';
      case 'google_pay':
        return 'Google Pay';
      default:
        return '';
    }
  };

  const getPaymentStatusString = (status: string, paymentType?: string) => {
    switch (status) {
      case 'UNPROCESSED':
        return '未処理';
      case 'REQSUCCESS':
        return paymentType === 'convenience' ? 'コンビニ支払い申し込み中' : '支払い申込中';
      case 'PAYSUCCESS':
        return '購入済み';
      case 'EXPIRED':
        return '支払い期限切れ';
      case 'CANCEL':
        return 'キャンセル';
      case 'PAYFAILED':
        return '取引中止';
      default:
        return '';
    }
  };

  const ReceiptRow = ({ title, value }: { title: string; value: string }) => {
    return (
      <div className="flex h-6 w-full border-b-1.5 border-b-white">
        <div className="flex h-full w-20 items-center justify-center bg-gray-650 text-regular-10 text-white">
          {title}
        </div>
        <div className="flex h-full flex-1 items-center justify-start bg-gray-50 pl-2 text-regular-12">{value}</div>
      </div>
    );
  };

  const PriceRow = ({ label, price, quantity }: { label: string; price: number; quantity?: number }) => (
    <div className="flex items-start justify-between gap-2">
      <span className="flex-1 break-all text-left leading-tight">{label}</span>
      <div className="flex flex-shrink-0 items-center gap-1">
        {quantity && <span className="text-right">{quantity}点</span>}
        <span className="text-right">¥{price.toLocaleString()}</span>
      </div>
    </div>
  );

  return (
    <div className="flex w-full flex-col px-4 pb-4 pt-2">
      <div className="overflow-hidden rounded-base">
        <ReceiptRow title={'注文日'} value={formatDateTime(purchasedItemDetail.order.orderedAt, 'jp')} />
        <ReceiptRow title={'注文番号'} value={purchasedItemDetail.order.orderNumber || 'なし'} />
        <ReceiptRow
          title={'お支払い方法'}
          value={getPaymentTypeString(purchasedItemDetail.checkout?.paymentType || '')}
        />
        <ReceiptRow
          title={'お支払い状況'}
          value={getPaymentStatusString(
            purchasedItemDetail.checkout?.status || '',
            purchasedItemDetail.checkout?.paymentType || undefined,
          )}
        />
        <div className="flex min-h-6">
          <div className="flex w-20 items-center justify-center bg-gray-650 text-regular-10 text-white">
            お支払い金額
          </div>
          <div className="flex flex-1 items-center justify-center bg-gray-50 p-3 text-regular-10">
            <div className="flex w-full flex-col">
              <div className="flex flex-col gap-2 pb-1">
                {purchasedItemDetail.order.items.map((item) => (
                  <PriceRow key={item.id} label={item.name} quantity={item.quantity || 1} price={item.price || 0} />
                ))}
                {purchasedItemDetail.checkout?.tipAmount && (
                  <PriceRow label="チップ" price={purchasedItemDetail.checkout.tipAmount} quantity={1} />
                )}
                {purchasedItemDetail.checkout?.deliveryFee && (
                  <PriceRow label="配送料" price={purchasedItemDetail.checkout.deliveryFee} />
                )}
                {purchasedItemDetail.checkout?.fee && (
                  <PriceRow label="事務手数料" price={purchasedItemDetail.checkout.fee} />
                )}
              </div>
              <div className="w-full border-t border-gray-200" />
              <div className="pt-2">
                <div className="flex items-center justify-end gap-6">
                  <span>合計金額:</span>
                  <span className="text-regular-13">¥{purchasedItemDetail.checkout?.total?.toLocaleString()}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentReceipt;
