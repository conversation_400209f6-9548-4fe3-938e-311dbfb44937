import React from 'react';
import { notFound } from 'next/navigation';
import { CheckoutStatus } from '@/lib/client-api/client-shop-api.schemas';
import { getPurchasedItem } from '@/lib/server-api/purchased-item-endpoint/purchased-item-endpoint';
import { getShop } from '@/lib/server-api/shop-endpoint/shop-endpoint';
import ChekiPurchasedItem from '@/app/[identityId]/purchased-item/_components/cheki-purchased-item';
import DigitalBundlePurchasedItem from '@/app/[identityId]/purchased-item/_components/digital-bundle-purchased-item';
import GachaPurchasedItem from '@/app/[identityId]/purchased-item/_components/gacha-purchased-item';
import { getItem } from '@/app/actions/shopItem';
import { getUserIdentityId } from '@/utils/base';
import { isGacha, isExhibitCheki, isDigitalBundle } from '@/utils/itemTypes';
import { GachaItem } from '@/types/gacha';
import { ChekiItemDetail, ShopItemDetail } from '@/types/shopItem';

type ItemViewerProps = {
  identityId: string;
  purchasedItemId: string;
};

const ItemPurchasedDetail = async ({ params }: { params: ItemViewerProps }) => {
  const purchasedItemId = params.purchasedItemId;
  const identityId = getUserIdentityId(params.identityId);

  if (isNaN(Number(purchasedItemId))) {
    return notFound();
  }

  try {
    const purchasedItemResponse = await getPurchasedItem(parseInt(params.purchasedItemId));
    const purchasedItemDetail = purchasedItemResponse.data?.purchasedItem;
    if (!purchasedItemDetail || !purchasedItemDetail.itemId) throw new Error('purchasedItem not found');

    const itemResponse = await getItem(purchasedItemDetail.itemId.toString(), identityId, true);
    const itemData = await itemResponse.json();
    const item: ShopItemDetail['item'] | GachaItem['item'] | ChekiItemDetail['item'] = itemData.data?.item;
    if (!item || !item.id) throw new Error('item not found');

    // 商品は複数回購入可能なので購入済みかどうかをチェックアウトのステータスで判断する
    const isPaySucceeded = purchasedItemResponse.data?.purchasedItem.checkout?.status === CheckoutStatus.PAYSUCCESS;

    const shopResponse = await getShop(identityId);
    const shop = shopResponse.data?.shop;
    if (!shop) throw new Error('shop not found');

    return (
      <>
        {isDigitalBundle(item) && (
          <DigitalBundlePurchasedItem
            shop={shop}
            item={item as ShopItemDetail['item']}
            purchasedItemDetail={purchasedItemDetail}
            isPaySucceeded={isPaySucceeded}
          />
        )}
        {isGacha(item) && (
          <GachaPurchasedItem
            shop={shop}
            item={item as GachaItem['item']}
            purchasedItemDetail={purchasedItemDetail}
            isPaySucceeded={isPaySucceeded}
          />
        )}
        {isExhibitCheki(item) && (
          <ChekiPurchasedItem
            shop={shop}
            item={item as ChekiItemDetail['item']}
            purchasedItemDetail={purchasedItemDetail}
            isPaySucceeded={isPaySucceeded}
          />
        )}
      </>
    );
  } catch (e) {
    console.error(JSON.stringify({ e }));
    return notFound();
  }
};

export default ItemPurchasedDetail;
