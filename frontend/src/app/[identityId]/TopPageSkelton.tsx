'use client';

import React from 'react';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';

const ItemSkeleton = () => (
  <div className="my-2 flex flex-col items-center justify-center gap-8 px-3">
    <div className="flex w-full flex-col items-center justify-center gap-6">
      {[...Array(4)].map((_, index) => (
        <div key={index} className="w-full">
          <div className="flex w-full items-start gap-3">
            <div className="relative aspect-square w-30 overflow-hidden rounded-lg">
              <Skeleton height="100%" />
            </div>
            <div className="flex flex-1 flex-col gap-2">
              <Skeleton width="75%" height={16} />
              <Skeleton width="50%" height={16} />
              <div className="mt-auto flex items-center gap-2">
                <Skeleton width={80} height={24} borderRadius={9999} />
                <Skeleton width={64} height={24} borderRadius={9999} />
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

export default ItemSkeleton;
