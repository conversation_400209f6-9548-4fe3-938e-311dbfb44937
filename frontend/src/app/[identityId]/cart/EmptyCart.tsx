'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import Button from '@/components/atoms/button';

interface EmptyCartProps {
  identityId: string;
}

const EmptyCart = ({ identityId }: EmptyCartProps) => {
  const router = useRouter();
  return (
    <div className="flex flex-1 flex-col items-center justify-center gap-2">
      <div className="text-bold-18">カートは空です</div>
      <div className="mb-3 text-medium-14">商品をお探しですか？</div>
      <Button onClick={() => router.push(`/@${identityId}`)} buttonType="dark" buttonSize="lg">
        商品を見る
      </Button>
    </div>
  );
};

export default EmptyCart;
