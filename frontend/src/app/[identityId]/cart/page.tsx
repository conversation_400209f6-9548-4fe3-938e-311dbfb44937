'use client';

import React, { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import clsx from 'clsx';
import { useParams, useRouter } from 'next/navigation';
import Button from '@/components/atoms/button';
import { CartStatusError } from '@/components/cart/CartStatusError';
import CartItemList from '@/components/containers/CartItem/CartItemList';
import FixedBar from '@/components/layouts/FixedBar';
import { updateCartItem } from '@/lib/client-api/cart-item-endpoint/cart-item-endpoint';
import { useCurrentUser } from '@/store/useCurrentUser';
import CartSummaryBar from './CartSummaryBar';
import EmptyCart from './EmptyCart';
import FixedBarContent from './FixedBarContent';
import { useGetCart } from '@/hooks/swr/useGetCart';
import { useCheckCartItemsStatus } from '@/hooks/useCheckCartItemsStatus';
import { useIsFixedAtBottom } from '@/hooks/useIsFixedAtBottom';
import { googleAnalyticsForEc } from '@/services/googleAnalyticsForEc';
import { getUserIdentityId } from '@/utils/base';
import { calculateCartItemPartsTotalPrice, calculateCartItemsTotalPrice } from '@/utils/cart';
import { CartFormValues } from '@/types/cart';

const CartPage = () => {
  const router = useRouter();
  const params = useParams();
  const { currentUser } = useCurrentUser();
  const midDiv = useRef<HTMLDivElement>(null);
  const isFixed = useIsFixedAtBottom(midDiv);
  const [identityId, setIdentityId] = useState<string>('');

  useEffect(() => {
    const identityId = getUserIdentityId(params.identityId as string);
    setIdentityId(identityId);
  }, [params.identityId]);

  const { data, refetch } = useGetCart(identityId, currentUser?.accountIdentity as string);

  const cartItems = React.useMemo(() => data?.data?.items || [], [data]);
  const { control, watch, setValue } = useForm<CartFormValues>();

  const [totalQuantity, setTotalQuantity] = useState(0);
  const [cartItemsTotalPrice, setCartItemsTotalPrice] = useState(0);

  useEffect(() => {
    cartItems.forEach((cartItem) => {
      setValue(`cartFormValues.${cartItem.cartItemId}.cartItemId`, cartItem.cartItemId);
      setValue(`cartFormValues.${cartItem.cartItemId}.quantity`, cartItem.quantity);
      setValue(`cartFormValues.${cartItem.cartItemId}.purchaserComment`, cartItem.purchaserComment);
    });

    const totalPrice = calculateCartItemsTotalPrice(cartItems);
    setTotalQuantity(data?.data?.totalQuantity ?? 0);
    setCartItemsTotalPrice(totalPrice);
  }, [data, cartItems, setValue]);

  useEffect(() => {
    const subscription = watch((value, { name }) => {
      if (!!value && name?.endsWith('quantity')) {
        const cartItemMap = new Map(cartItems.map((cartItem) => [cartItem.cartItemId, cartItem]));

        setCartItemsTotalPrice(
          calculateCartItemPartsTotalPrice(
            (value.cartFormValues ?? []).map((cartItem) => {
              const currentPrice = cartItemMap.get(cartItem?.cartItemId ?? 0)?.currentPrice ?? 0;
              const quantity = cartItem?.quantity ?? 0;
              return { currentPrice, quantity };
            }),
          ),
        );
        setTotalQuantity(value.cartFormValues?.reduce((total, value) => total + (value?.quantity ?? 0), 0) ?? 0);
      }
    });
    return () => subscription.unsubscribe();
  }, [cartItems, watch]);

  const { checkCartItems } = useCheckCartItemsStatus({
    cartItems,
    identityId,
    onSuccess: () => {
      googleAnalyticsForEc.beginCheckout(cartItems);
      router.push(`/@${identityId}/order`);
    },
    renderErrorContent: (errors) => <CartStatusError errors={errors} cartItems={cartItems} />,
    onComplete: refetch,
  });

  const handleProceedToCheckout = async () => {
    await Promise.all(
      cartItems.map((cartItem) =>
        updateCartItem(identityId, cartItem.cartItemId, {
          quantity: watch(`cartFormValues.${cartItem.cartItemId}.quantity`),
          purchaserComment: watch(`cartFormValues.${cartItem.cartItemId}.purchaserComment`),
        }),
      ),
    );

    await checkCartItems();
  };

  if (cartItems.length === 0) {
    return <EmptyCart identityId={identityId as string} />;
  }

  return (
    <div className={clsx('relative mt-4 flex-1', { ['pb-25']: !isFixed })} ref={midDiv}>
      <div className="mb-4 mt-2 flex flex-col items-center justify-center gap-6 px-3">
        <CartSummaryBar subTotal={cartItemsTotalPrice} />
        <Button onClick={handleProceedToCheckout} buttonType="primary">
          レジに進む
          <span className="ml-2 text-regular-11">({totalQuantity.toLocaleString()}個の商品)</span>
        </Button>
        <CartItemList
          items={cartItems}
          showRemoveButton={true}
          showPurchaseInputs={true}
          onItemsChange={refetch}
          control={control}
          watch={watch}
        />
      </div>
      <FixedBar isFixed={isFixed} alignCenter={false}>
        <FixedBarContent subTotal={cartItemsTotalPrice} onClick={handleProceedToCheckout} />
      </FixedBar>
    </div>
  );
};

export default CartPage;
