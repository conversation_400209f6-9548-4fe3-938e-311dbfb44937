import React from 'react';

interface CartSummaryBarProps {
  subTotal: number;
}

const CartSummaryBar = ({ subTotal }: CartSummaryBarProps) => {
  return (
    <div className="w-full rounded-lg bg-white p-4">
      <div className="flex items-center justify-between">
        <span className="text-regular-13">小計</span>
        <div className="flex items-baseline">
          <span className="text-bold-28">¥{subTotal.toLocaleString()}</span>
          <span className="ml-1 text-regular-11">(税込)</span>
        </div>
      </div>
    </div>
  );
};

export default CartSummaryBar;
