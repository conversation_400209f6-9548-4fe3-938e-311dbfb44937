'use client';

import React from 'react';
import ShopItemList from '@/components/containers/ShopItem/ShopItemList';
import ShopPublicImage from '@/components/ShopImage';
import { ShopItemType } from '@/types/shopItem';

interface RecentlyViewedItemSectionProps {
  itemList: ShopItemType[];
  creatorId: string;
}

const RecentlyViewedItemSection = ({ itemList, creatorId }: RecentlyViewedItemSectionProps) => {
  return (
    <section className="mb-4 mt-5 w-full">
      <div className="mb-2 flex w-full justify-between">
        <h3 className="text-bold-20">最近チェックした商品</h3>
        <div className="flex items-center">
          <span className="mr-2 text-regular-11">See More</span>
          <ShopPublicImage src="/images/icons/ViewMore.svg" alt="View More" width={24} height={24} />
        </div>
      </div>
      <ShopItemList items={itemList} identityId={creatorId} />
    </section>
  );
};

export default RecentlyViewedItemSection;
