'use client';

import React from 'react';
import Button from '@/components/atoms/button';

interface FixedBarContentProps {
  subTotal: number;
  onClick: () => void;
}

const FixedBarContent = ({ subTotal, onClick }: FixedBarContentProps) => {
  return (
    <div className="flex items-center justify-between px-6">
      <div className="flex flex-col">
        <p className="text-medium-15">小計</p>
        <div className="flex items-center">
          <p className="mr-1 text-extra-bold-22 PRO:text-bold-28">{subTotal.toLocaleString()}</p>
          <p className="text-regular-11">(税込)</p>
        </div>
      </div>
      <Button onClick={onClick} buttonType="primary" buttonSize="free" buttonClassNames="w-42 h-14 text-bold-16">
        レジに進む
      </Button>
    </div>
  );
};

export default FixedBarContent;
