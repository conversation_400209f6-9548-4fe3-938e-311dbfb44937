'use client';

import React, { useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { FeatureFlags } from '@/lib/feature';
import CartOrderPage from './cart-order-page';
import GachaOrderPage from './gacha-order-page';
import { usePaymentScripts } from '@/hooks/use-load-payment-script';
import { getUserIdentityId } from '@/utils/base';
import { getFinalOrderType } from '@/utils/item';
import { ITEM_TYPE } from '@/types/item';

const OrderPage = ({ params }: { params: { identityId: string } }) => {
  usePaymentScripts();

  const router = useRouter();
  const identityId = getUserIdentityId(params.identityId);
  const searchParams = useSearchParams();
  const itemType = searchParams.get('itemType');
  const isGachaParam = searchParams.get('isGacha');
  const isGacha = isGachaParam === null ? null : isGachaParam === 'true';
  const {
    itemType: finalItemType,
    isGachaOrder: finalIsGachaOrder,
    shouldRedirect,
  } = getFinalOrderType(itemType, isGacha);

  useEffect(() => {
    if (shouldRedirect) {
      router.replace(`/@${identityId}`);
    }
  }, [shouldRedirect, router, identityId]);

  if (shouldRedirect) {
    return null;
  }

  if (finalItemType === ITEM_TYPE.PRINT_GACHA.str && !FeatureFlags.printGacha()) {
    return <CartOrderPage />;
  }

  return finalIsGachaOrder ? <GachaOrderPage /> : <CartOrderPage />;
};

export default OrderPage;
