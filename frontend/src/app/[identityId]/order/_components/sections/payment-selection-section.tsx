'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Button from '@/components/atoms/button';
import RadioGroup from '@/components/atoms/radioGroup';
import RadioGroupItem from '@/components/atoms/radioGroup/radio-group-item';
import ShopPublicImage from '@/components/ShopImage';
import { useOrderFormStore } from '@/store/useOrderFormStore';
import CardInformation from '@/app/payment/_components/CardInformation';
import { PAYMENT_METHODS } from '@/consts/order';
import { useGetCards } from '@/hooks/swr/useGetCards';
import { useConvenienceDialog } from '@/hooks/useConvenienceDialog';
import { useSelectCard } from '@/hooks/useSelectCard';

const FeeCampaignBadge = ({ children }: { children: React.ReactNode }) => (
  <p className="ml-11 inline-block rounded-s bg-green-250 px-2 py-0.5 text-regular-11 text-white">{children}</p>
);

type Props = {
  identityId: string;
};

const PaymentSelectionSection = ({ identityId }: Props) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { setPaymentMethod, selectedCard, setSelectedCard, paymentMethod } = useOrderFormStore();
  const [value, setValue] = useState(paymentMethod ? paymentMethod : 'card');
  const { data, isLoading } = useGetCards();
  const cards = data?.data;
  const { selectCard } = useSelectCard();
  const { openConvenienceDialog } = useConvenienceDialog();

  useEffect(() => {
    const selected = selectCard(cards);
    setSelectedCard(selected);
  }, [cards, setSelectedCard, selectCard]);

  const handleChange = (selectedValue: PAYMENT_METHODS) => {
    if (selectedValue === PAYMENT_METHODS.CONVENIENCE) {
      openConvenienceDialog(() => {
        setPaymentMethod(PAYMENT_METHODS.CONVENIENCE);
        setValue(PAYMENT_METHODS.CONVENIENCE);
      });
      return;
    }
    setPaymentMethod(selectedValue);
    setValue(selectedValue);
  };

  const handleGoToCardList = () => {
    const basePath = `/@${identityId}/order`;
    const query = searchParams.toString();
    const orderReturnToPath = query ? `${basePath}?${query}` : basePath;
    router.push(`/payment/credit-card/list?returnTo=${encodeURIComponent(orderReturnToPath)}`);
  };

  const handleGoToConvenience = () => {
    const basePath = `/@${identityId}/order`;
    const query = searchParams.toString();
    const orderReturnToPath = query ? `${basePath}?${query}` : basePath;
    router.push(`/payment/convenience-store/register?returnTo=${encodeURIComponent(orderReturnToPath)}`);
  };

  return (
    <div className="flex w-full items-center justify-between gap-12">
      <div className="grow gap-4">
        <RadioGroup name="payment-method" defaultValue="card" direction="column" className="!gap-0">
          <div className="flex h-8 items-center justify-between">
            <RadioGroupItem
              label={
                isLoading ? (
                  <div className="flex animate-pulse items-center gap-2">
                    <div className="h-4 w-8 rounded bg-gray-200"></div>
                    <div className="h-4 w-24 rounded bg-gray-200"></div>
                  </div>
                ) : selectedCard ? (
                  <CardInformation
                    brand={selectedCard.name}
                    number={selectedCard.number}
                    expiresAt={selectedCard.expiresAt}
                  />
                ) : (
                  'カード情報を登録'
                )
              }
              value={PAYMENT_METHODS.CARD}
              name="payment-method"
              selectedValue={value}
              onChange={() => handleChange(PAYMENT_METHODS.CARD)}
            />
            <Button onClick={handleGoToCardList} buttonType="light-small" buttonShape="circle" buttonSize="xxs">
              <ShopPublicImage
                src="/images/icons/Arrow_Back.svg"
                width={12}
                height={12}
                alt="help"
                className="-rotate-180"
              />
            </Button>
          </div>
          <div className="h-6">
            {value === PAYMENT_METHODS.CARD && (
              <FeeCampaignBadge>事務手数料9%の割引キャンペーンが適用されます</FeeCampaignBadge>
            )}
          </div>
          <div className="flex h-8 items-center justify-between">
            <RadioGroupItem
              label="コンビニ決済"
              value={PAYMENT_METHODS.CONVENIENCE}
              name="payment-method"
              selectedValue={value}
              onChange={() => handleChange(PAYMENT_METHODS.CONVENIENCE)}
            />
            <Button onClick={handleGoToConvenience} buttonType="light-small" buttonShape="circle" buttonSize="xxs">
              <ShopPublicImage
                src="/images/icons/Arrow_Back.svg"
                width={12}
                height={12}
                alt="help"
                className="-rotate-180"
              />
            </Button>
          </div>
          <div className="h-6">
            {value === PAYMENT_METHODS.CONVENIENCE && (
              <p className="ml-11 text-regular-11 text-orange-200">275円（税込み）の事務手数料がかかります</p>
            )}
          </div>
          <RadioGroupItem
            label={
              <div className="ml-3.5 flex h-8 items-center gap-1">
                <ShopPublicImage src="/images/icons/GooglePay.svg" alt="Google" width={30} height={20} />
                <span className="text-medium-14">GooglePay</span>
              </div>
            }
            value={PAYMENT_METHODS.GOOGLE_PAY}
            name="payment-method"
            selectedValue={value}
            onChange={() => handleChange(PAYMENT_METHODS.GOOGLE_PAY)}
          />
          <div className="h-6">
            {value === PAYMENT_METHODS.GOOGLE_PAY && (
              <FeeCampaignBadge>事務手数料9%の割引キャンペーンが適用されます</FeeCampaignBadge>
            )}
          </div>
          {/*SafariのみApplePay選択可能*/}
          {(window as any).ApplePaySession && (
            <>
              <RadioGroupItem
                label={
                  <div className="ml-3.5 flex h-8 items-center gap-1">
                    <ShopPublicImage src="/images/icons/ApplePay.svg" alt="ApplePay" width={30} height={20} />
                    <span className="text-medium-14">ApplePay</span>
                  </div>
                }
                value={PAYMENT_METHODS.APPLE_PAY}
                name="payment-method"
                selectedValue={value}
                onChange={() => handleChange(PAYMENT_METHODS.APPLE_PAY)}
              />
              <div className="h-6">
                {value === PAYMENT_METHODS.APPLE_PAY && (
                  <FeeCampaignBadge>事務手数料9%の割引キャンペーンが適用されます</FeeCampaignBadge>
                )}
              </div>
            </>
          )}
          <RadioGroupItem
            label={
              <div className="ml-3.5 flex h-8 items-center gap-1">
                <ShopPublicImage src="/images/icons/PayPay.svg" alt="PayPay" width={80} height={20} />
              </div>
            }
            value={PAYMENT_METHODS.PAY_PAY}
            name="payment-method"
            selectedValue={value}
            onChange={() => handleChange(PAYMENT_METHODS.PAY_PAY)}
          />
          <div className="h-6">
            {value === PAYMENT_METHODS.PAY_PAY && (
              <p className="ml-11 text-regular-11 text-orange-200">事務手数料 9% がかかります</p>
            )}
          </div>
        </RadioGroup>
      </div>
    </div>
  );
};

export default PaymentSelectionSection;
