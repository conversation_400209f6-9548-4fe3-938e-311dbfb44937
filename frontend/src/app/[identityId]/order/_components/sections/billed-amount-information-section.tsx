'use client';

import React from 'react';
import clsx from 'clsx';
import ShopPublicImage from '@/components/ShopImage';

interface BilledAmountInformationSectionProps {
  cartItemsTotalPrice: number;
  fee: number;
  tip: number;
  yellCount: number;
  deliveryFee?: number;
  boostRatio: number;
  hasShopCreatorActiveEvent: boolean;
}

const BilledAmountInformationSection = ({
  cartItemsTotalPrice,
  fee,
  tip,
  deliveryFee,
  yellCount,
  boostRatio,
  hasShopCreatorActiveEvent,
}: BilledAmountInformationSectionProps) => {
  const total = cartItemsTotalPrice + tip + fee + (deliveryFee || 0);
  const yellAmount = (yellCount + tip) * boostRatio;
  return (
    <div className="flex w-full flex-col gap-2">
      <div className="flex flex-col rounded-lg bg-white p-4">
        <div className="flex justify-between border-b border-dashed border-gray-300 py-2">
          <span>商品の小計</span>
          <span>¥{cartItemsTotalPrice.toLocaleString()}</span>
        </div>
        <div className="flex justify-between border-b border-dashed border-gray-300 py-2">
          <span>事務手数料</span>
          <span>¥{fee.toLocaleString()}</span>
        </div>
        {typeof deliveryFee === 'number' && (
          <div className="flex justify-between border-b border-dashed border-gray-300 py-2">
            <span>送料</span>
            <span>¥{deliveryFee.toLocaleString()}</span>
          </div>
        )}
        <div className="flex justify-between border-b border-dashed border-gray-300 py-2">
          <span>チップ</span>
          <span className={clsx(tip === 0 ? 'text-gray-200' : 'text-black')}>¥{tip.toLocaleString()}</span>
        </div>
        <div className="mt-2 flex items-center justify-between text-lg font-bold">
          <span className="text-bold-15">ご請求額</span>
          <div className="flex items-center gap-1">
            <div className="text-bold-28">¥{total.toLocaleString()}</div>
            <div className="text-regular-11">(税込)</div>
          </div>
        </div>
      </div>
      {hasShopCreatorActiveEvent && (
        <div className="flex items-center justify-between rounded-lg bg-white px-4 py-2">
          <div className="text-medium-13 text-orange-200">今回のエール贈与数</div>
          <div className="flex items-center gap-1">
            {boostRatio > 1 && (
              <ShopPublicImage
                src="/images/ranking/YellBoostPurchase.webp"
                alt="Yell Icon"
                width={80}
                height={23}
                className="mr-2 object-contain"
              />
            )}
            {boostRatio === 1 && (
              <ShopPublicImage src="/images/ranking/Yell.svg" alt="Yell Icon" width={20} height={20} />
            )}
            <div className="ml-1 text-medium-18 text-orange-200">{yellAmount.toLocaleString()}</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BilledAmountInformationSection;
