import { ThanksSectionContent } from './ThanksSectionContent';
import { getShopInfo } from '@/app/actions/shopInfo';
import type { Shop } from '@/types/shopinfo';

interface ThanksSectionProps {
  identityId: string;
}

const ThanksSection = async ({ identityId }: ThanksSectionProps) => {
  const shopInfo = await getShopInfo(identityId);
  const shopInfoJson: Shop = await shopInfo.json().then((res) => res.data.shop);

  if (!shopInfoJson) {
    return null;
  }

  return <ThanksSectionContent shopInfo={shopInfoJson} identityId={identityId} />;
};

export default ThanksSection;
