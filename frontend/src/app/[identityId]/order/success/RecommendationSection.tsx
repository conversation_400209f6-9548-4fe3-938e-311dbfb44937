import React from 'react';
import RecommendationSectionContent from './RecommendationSectionContent';
import { getDigitalFanLetterLink } from '@/app/actions/shopInfo';
import type { ApiResponse } from '@/types/api';
import type { DigitalFanLetterResponseBk } from '@/app/api/types/backend/shop/ShopInfo';

interface RecommendationSectionProps {
  identityId: string;
}

const createDigitalFanLetterUrl = (identityId: string, urlIdentifier: string): string => {
  return `${process.env.NEXT_PUBLIC_FANME_LINK_URL}/@${identityId}/apps/${urlIdentifier}`;
};

const RecommendationSection = async ({ identityId }: RecommendationSectionProps) => {
  try {
    const response = await getDigitalFanLetterLink(identityId);
    const responseData = (await response.json()) as ApiResponse<DigitalFanLetterResponseBk>;
    const urlIdentifier = responseData?.data?.digital_fan_letter_link;

    if (!urlIdentifier) {
      return null;
    }

    const digitalFanLetterUrl = createDigitalFanLetterUrl(identityId, urlIdentifier);
    return <RecommendationSectionContent url={digitalFanLetterUrl} />;
  } catch (error) {
    console.error('Failed to get digital fan letter link:', error);
    return null;
  }
};

export default RecommendationSection;
