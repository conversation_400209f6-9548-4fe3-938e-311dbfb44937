'use client';

import React from 'react';
import Button from '@/components/atoms/button';
import ShopPublicImage from '@/components/ShopImage';

interface RecommendationSectionContentProps {
  url: string;
}

const RecommendationSectionContent = ({ url }: RecommendationSectionContentProps) => {
  return (
    <section className="relative flex w-full flex-col items-center justify-center gap-2 bg-gray-100 px-4 pb-8">
      <h3 className="w-full text-bold-17">おすすめコンテンツ</h3>
      <div className="relative w-full max-w-lg">
        <ShopPublicImage
          src="/images/BlueRedBackground.webp"
          alt="CTA Background"
          layout="responsive"
          width={800}
          height={400}
          priority
        />
        <div className="absolute inset-0 flex flex-col items-center justify-center gap-2 p-6">
          <ShopPublicImage src="/images/DigitalFanLetterRecommendation.webp" alt="content" width={414} height={146} />
          <Button
            className="mt-4"
            buttonType="light"
            buttonSize="free"
            buttonClassNames="text-black w-70 h-14 text-bold-16"
            onClick={() => {
              window.location.href = url;
            }}
          >
            デジタルファンレターを送る
          </Button>
        </div>
      </div>
    </section>
  );
};

export default RecommendationSectionContent;
