import React, { Suspense } from 'react';
import RecommendationSection from './RecommendationSection';
import ThanksSection from './ThanksSection';
import { getUserIdentityId } from '@/utils/base';

const OrderSuccessPage = ({ params }: { params: { identityId: string } }) => {
  const identityId = getUserIdentityId(params.identityId);
  return (
    <div className="flex flex-col items-center gap-6">
      <Suspense fallback={<></>}>
        <ThanksSection identityId={identityId} />
      </Suspense>
      <Suspense fallback={<></>}>
        <RecommendationSection identityId={identityId} />
      </Suspense>
    </div>
  );
};

export default OrderSuccessPage;
