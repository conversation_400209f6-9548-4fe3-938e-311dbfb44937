'use client';

import clsx from 'clsx';
import { useRouter } from 'next/navigation';
import Avatar from '@/components/atoms/avatar';
import Button from '@/components/atoms/button';
import ShopPublicImage from '@/components/ShopImage';
import type { Shop } from '@/types/shopinfo';
interface ThanksSectionContentProps {
  shopInfo: Shop;
  identityId: string;
}

export const ThanksSectionContent = ({ shopInfo, identityId }: ThanksSectionContentProps) => {
  const router = useRouter();
  return (
    <div className="flex w-full flex-col items-center justify-between gap-6 bg-yellow-10 p-2 py-8">
      <ShopPublicImage src="/images/ThankYou.webp" alt="thankyou" width={414} height={86} />
      {shopInfo && (
        <div className="flex w-full flex-col items-center justify-center gap-4 rounded-lg bg-yellow-50 px-4 py-8">
          <div
            className={clsx('flex w-full items-center gap-2', {
              'justify-start': shopInfo.creatorIconUri,
              'justify-center': !shopInfo.creatorIconUri,
            })}
          >
            {shopInfo.creatorIconUri && <Avatar src={shopInfo.creatorIconUri} alt={shopInfo.name} size={40} />}
            <p className="text-bold-15 text-brown-100">{shopInfo.name}</p>
          </div>
          <p className="relative whitespace-pre-wrap rounded-lg bg-white p-4 text-medium-14 text-brown-100 before:absolute before:-top-2 before:left-1/2 before:size-0 before:-translate-x-1/2 before:border-x-8 before:border-b-8 before:border-x-transparent before:border-b-white before:content-['']">
            {shopInfo.message}
          </p>
          <Button buttonSize="lg" onClick={() => router.push(`${process.env.NEXT_PUBLIC_FANME_LINK_URL}/mylibrary`)}>
            購入したコンテンツ
          </Button>
        </div>
      )}
      <p className="text-regular-12 text-brown-100">
        *ご登録のメールアドレス宛にご注文内容の詳細を記載したメールをお送りしました。環境によっては受信できない、または迷惑フォルダに振り分けられる場合がございます。
      </p>
      <Button
        buttonType="light"
        buttonSize="free"
        buttonClassNames="text-black w-40 h-10 text-medium-14"
        onClick={() => {
          // 購入後、一覧に反映されないことがあるので、pushで遷移せずに、location.replaceでページごとリロードする形で遷移
          window.location.replace(`/shop/@${identityId}`);
        }}
      >
        ショップへ戻る
      </Button>
    </div>
  );
};
