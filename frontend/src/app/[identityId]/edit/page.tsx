import React from 'react';
import { notFound, redirect } from 'next/navigation';
import { ParamsCleaner } from '@/components/common/CommonComponents/ParamsCleaner';
import CreateShopComponent from '@/components/views/CreateShop';
import { getShopInfo } from '@/app/actions/shopInfo';
import { getUserIdentityId } from '@/utils/base';
import { Shop } from '@/types/shopinfo';

const EditShop = async ({ params }: { params: { identityId: string } }) => {
  const identityId = getUserIdentityId(params.identityId);
  const shopInfo = await getShopInfo(identityId, true);
  if (shopInfo.status !== 200) {
    if (shopInfo.status === 404) {
      redirect('/create');
    }
    if (shopInfo.status === 403) {
      notFound();
    }
  }
  const shopInfoJson: Shop = await shopInfo.json().then((res) => res.data.shop);
  if (!shopInfoJson) {
    redirect('/create');
  }
  return (
    <div className="pt-10">
      <CreateShopComponent
        coverImageProp={shopInfoJson.headerImageUri}
        shopInfoProp={shopInfoJson}
        messageProp={shopInfoJson.message}
        isEdit
      />
      <ParamsCleaner />
    </div>
  );
};

export default EditShop;
