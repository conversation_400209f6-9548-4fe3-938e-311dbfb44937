'use client';

import { useEffect, useMemo, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useShallow } from 'zustand/react/shallow';
import DetailItemsSection from '@/components/containers/DetailItemsSection';
import DetailMainInfo from '@/components/containers/DetailMainInfo';
import ThumbnailSwiper from '@/components/containers/ThumbnailSwiper';
import DetailPageInstruction from '@/components/views/DetailPageInstruction';
import { useExhibitsStore } from '@/store/useExhibit';
import { isExhibitCheki } from '@/utils/itemTypes';
import { ITEM_TYPE } from '@/types/shopItem';

const ChekiPreview = () => {
  const router = useRouter();
  const exhibits = useExhibitsStore(useShallow((state) => state.exhibits));
  const midDiv = useRef<HTMLDivElement>(null);
  const params = useParams();
  const identityId = params.identityId as string;
  const itemId = params.id as string;
  const itemType = ITEM_TYPE.cheki;
  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]);

  useEffect(() => {
    const isPreviewDataMissing = !exhibitItem || !exhibitItem?.title;
    if (isPreviewDataMissing) {
      router.back();
    }
  }, [exhibitItem, router]);
  const { title, description, limited, period, thumbnail, thumbnailRatio, priceSet, remainingAmount, discount } =
    exhibitItem ?? {};
  const limitedPerUser = !!exhibitItem && isExhibitCheki(exhibitItem) ? exhibitItem.limitedPerUser : undefined;

  const benefits = isExhibitCheki(exhibitItem) ? (exhibitItem.benefits ?? []) : [];

  const discountRate = discount?.percentage ? discount.percentage / 100 : 0;
  const currentPrice = Math.floor((priceSet ?? 0) - (priceSet ?? 0) * discountRate);

  const thumbnailProps = {
    shopName: '',
    creatorName: '',
    itemId,
    isPreview: true,
    itemType,
  };

  const detailMainInfoProps = {
    description,
    limited,
    limitedPerUser,
    purchasedCount: 0,
    leftStock: limited,
    period,
    itemId,
    isPreview: true,
  };

  const detailItemsSectionProps = {
    itemFiles: [],
    thumbnail: thumbnail!,
    thumbnailRatio: thumbnailRatio!,
    title: title!,
    price: priceSet!,
    discountRate,
    currentPrice,
    discount,
    benefits,
    isSoldOut: (remainingAmount ?? 1) <= 0,
    singleSale: false,
    period,
    isPreview: true,
    identityId,
    forceShowItemOption: true,
    itemType,
  };
  return (
    <div ref={midDiv}>
      <ThumbnailSwiper props={thumbnailProps} />
      <DetailMainInfo props={detailMainInfoProps} />
      <DetailItemsSection props={detailItemsSectionProps} />
      <DetailPageInstruction isDigital={false} />
    </div>
  );
};

export default ChekiPreview;
