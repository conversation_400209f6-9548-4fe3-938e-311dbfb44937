// TODO: ts-nocheck
//@ts-nocheck
'use client';

import { useEffect, useMemo } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useShallow } from 'zustand/react/shallow';
import GachaCollectionSection from '@/components/containers/GachaCollectionSection';
import GachaCompleteProgressBar from '@/components/containers/GachaCompleteProgressBar';
import GachaCompleteSection from '@/components/containers/GachaCompleteSection';
import GachaDetailItemsSection from '@/components/containers/GachaDetailItemSection';
import GachaDetailMainInfo from '@/components/containers/GachaDetailMainInfo';
import GachaPullSection from '@/components/containers/GachaPullSection';
import ThumbnailSwiper from '@/components/containers/ThumbnailSwiper';
import DetailPageInstruction from '@/components/views/DetailPageInstruction';
import { useExhibitsStore } from '@/store/useExhibit';
import { isDigitalGacha, isGacha, isPrintGacha } from '@/utils/itemTypes';
import { isNowBetween } from '@/utils/time';
import { ITEM_TYPE } from '@/types/item';

const GachaPreview = () => {
  const router = useRouter();
  const exhibits = useExhibitsStore(useShallow((state) => state.exhibits));
  const params = useParams();
  const identityId = params.identityId as string;
  const itemId = params.id as string;
  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]);
  const itemType = isDigitalGacha(exhibitItem) ? ITEM_TYPE.DIGITAL_GACHA.value : ITEM_TYPE.PRINT_GACHA.value;
  useEffect(() => {
    const isPreviewDataMissing = !exhibitItem || !exhibitItem?.title;
    if (isPreviewDataMissing) {
      router.back();
    }
  }, [exhibitItem, router]);
  const {
    title,
    description,
    limited,
    period,
    thumbnail,
    thumbnailRatio,
    samples,
    priceSet: price,
    discount,
    password,
    available,
  } = exhibitItem ?? {};
  const { itemFiles, totalCapacity, awardProbabilities, benefits, isDuplicated } = isGacha(exhibitItem)
    ? {
        itemFiles: exhibitItem.itemFiles,
        totalCapacity: exhibitItem.totalCapacity,
        awardProbabilities: exhibitItem.awardProbabilities,
        benefits: exhibitItem.benefits,
        isDuplicated: exhibitItem.isDuplicated,
      }
    : {};

  const isOnSale =
    discount && discount.percentage && discount.start && discount.end
      ? isNowBetween(discount.start, discount.end)
      : false;

  const currentPrice =
    isOnSale && discount?.percentage ? (price ? Math.floor(price * (1 - discount.percentage / 100)) : 0) : (price ?? 0);

  const thumbnailProps = {
    shopName: '',
    creatorName: '',
    itemId,
    itemType,
    itemFiles,
    samples,
    title,
    priceSet: price,
    currentPrice,
    thumbnailRatio,
    thumbnail,
    discount,
    isPreview: true,
    identityId,
  };

  const pullGachaProps = {
    description,
    price: price ?? 0,
    currentPrice,
    discount,
    period,
    available: !!available,
    isPreview: true,
    isDuplicated,
    remainingUniquePullCount: itemFiles?.length ?? 0,
  };
  const gachaMainInfoProps = {
    description,
    limited,
    period,
    itemId,
    isPreview: true,
  };
  const gachaItemsSectionProps = {
    itemFiles: itemFiles!,
    thumbnail: thumbnail!,
    title: title!,
    price: price ?? 0,
    currentPrice,
    thumbnailRatio: thumbnailRatio!,
    discount,
    benefits: benefits!,
    identityId,
    itemId: Number(itemId),
    period,
    hasPassword: !!password,
    forceShowItemOption: false,
    isSoldOut: false,
    totalCapacity: totalCapacity!,
    awardProbabilities: awardProbabilities!,
    isDuplicated,
    isPreview: true,
  };
  const gachaCompleteSectionProps = {
    isPreview: true,
    totalGachaCount: itemFiles?.length ?? 0,
    collectedUniqueItemsCount: itemFiles?.length ?? 0,
    isCompleted: true,
    thumbnail: thumbnail!,
    itemId: Number(itemId),
  };
  const progressbarProps = {
    itemFiles: itemFiles!,
    duplicatedCount: 0,
    isPreview: true,
  };
  return (
    <>
      <ThumbnailSwiper props={thumbnailProps} />
      <GachaPullSection props={pullGachaProps} />
      <GachaDetailMainInfo props={gachaMainInfoProps} />
      <GachaDetailItemsSection props={gachaItemsSectionProps} />
      <GachaCompleteSection props={gachaCompleteSectionProps} />
      <GachaCollectionSection isPreview={true} itemFiles={itemFiles!} isPrintGacha={isPrintGacha(exhibitItem)} />
      <GachaCompleteProgressBar props={progressbarProps} />
      <DetailPageInstruction />
    </>
  );
};

export default GachaPreview;
