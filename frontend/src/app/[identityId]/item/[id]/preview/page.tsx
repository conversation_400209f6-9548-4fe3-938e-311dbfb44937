'use client';

import { useSearchParams } from 'next/navigation';
import ChekiPreview from './_components/cheki-preview-page';
import DigitalBundlePreview from './_components/digital-bundle-preview-page';
import GachaPreview from './_components/gacha-preview-page';
import { ITEM_TYPE, GACHA_ITEM_TYPES_STR, type ItemTypeString } from '@/types/item';
const Preview = () => {
  const searchParams = useSearchParams();
  const itemType = (searchParams.get('item_type') ?? ITEM_TYPE.DIGITAL_BUNDLE.str) as ItemTypeString;
  const isDigitalBundle = itemType === ITEM_TYPE.DIGITAL_BUNDLE.str;
  const isGacha = GACHA_ITEM_TYPES_STR.includes(itemType);
  const isCheki = itemType === ITEM_TYPE.CHEKI.str;
  return (
    <div>
      {isDigitalBundle && <DigitalBundlePreview />}
      {isGacha && <GachaPreview />}
      {isCheki && <ChekiPreview />}
    </div>
  );
};

export default Preview;
