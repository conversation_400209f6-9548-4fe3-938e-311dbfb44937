'use client';

import { useEffect, useMemo } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import ExhibitItemForm from '@/components/containers/ExhibitItemForm';
import { useGetShop } from '@/lib/client-api/shop-endpoint/shop-endpoint';
import { useCurrentUser } from '@/store/useCurrentUser';
import { useExhibitsStore } from '@/store/useExhibit';
import { useFullModalStore } from '@/store/useFullModal';
import { getUserIdentityId } from '@/utils/base';
import { getItemTypeValueFromStr, GACHA_ITEM_TYPES_STR } from '@/types/item';
import { ExhibitType } from '@/types/shopItem';

const CreateItem = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const params = useParams();
  const itemId = params.id as string;
  const itemType = (searchParams.get('item_type') || 'digitalBundle') as ExhibitType;
  const identityId = getUserIdentityId(params.identityId as string);
  const { currentUser } = useCurrentUser();
  const { data: shopResponseBody } = useGetShop(identityId);
  const { onFullModalClose } = useFullModalStore();
  const shopLimitation = useMemo(() => {
    return (
      shopResponseBody?.data?.shop?.limitation ?? {
        fileCapacity: 0,
        fileQuantity: 0,
        isChekiExhibitable: false,
        createdAt: '',
        updatedAt: '',
      }
    );
  }, [shopResponseBody]);
  useEffect(() => {
    onFullModalClose();
  }, [onFullModalClose]);

  useEffect(() => {
    if (currentUser && identityId !== currentUser?.accountIdentity) {
      router.replace(`/@${identityId}`);
    }
  }, [identityId, currentUser, router]);

  useEffect(() => {
    if (itemType === 'cheki' && shopLimitation && !shopLimitation.isChekiExhibitable) {
      router.replace(`/@${identityId}`);
    }
  }, [identityId, itemType, router, shopLimitation]);

  const { addExhibit, setItemType } = useExhibitsStore();

  useEffect(() => {
    addExhibit(itemId, itemType);
    if (GACHA_ITEM_TYPES_STR.includes(itemType)) {
      setItemType(itemId, getItemTypeValueFromStr(itemType));
    }
  }, [addExhibit, itemId, itemType, setItemType]);

  return <ExhibitItemForm itemParams={{ itemId, itemType }} identityId={identityId} shopLimitation={shopLimitation} />;
};

export default CreateItem;
