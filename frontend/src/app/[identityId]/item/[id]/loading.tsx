'use client';

import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';

export default function Loading() {
  return (
    <div className="space-y-8">
      {/* ThumbnailSwiper skeleton */}
      <div className="relative aspect-video w-full">
        <Skeleton className="absolute inset-0" />
      </div>

      {/* DetailMainInfo skeleton */}
      <div className="space-y-4">
        <Skeleton height={32} /> {/* Title */}
        <Skeleton count={3} /> {/* Description */}
        <div className="flex gap-4">
          <Skeleton width={100} /> {/* Period */}
          <Skeleton width={80} /> {/* Stock */}
        </div>
      </div>

      {/* DetailItemsSection skeleton */}
      <div className="space-y-4">
        <Skeleton height={24} width={200} /> {/* Section title */}
        <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="space-y-2">
              <div className="aspect-square">
                <Skeleton className="h-full" /> {/* Item thumbnail */}
              </div>
              <Skeleton height={20} /> {/* Item title */}
              <Skeleton width={80} /> {/* Price */}
            </div>
          ))}
        </div>
      </div>

      {/* DetailPageInstruction skeleton */}
      <div className="space-y-2">
        <Skeleton height={24} width={150} /> {/* Instruction title */}
        <Skeleton count={2} /> {/* Instruction content */}
      </div>
    </div>
  );
}
