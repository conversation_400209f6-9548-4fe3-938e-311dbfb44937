import { notFound, redirect } from 'next/navigation';
import { getShop } from '@/lib/server-api/shop-endpoint/shop-endpoint';
import DigitalBundleDetail from './_components/digital-bundle-detail-page';
import GachaDetail from './_components/gacha-detail-page';
import ChekiDetail from '@/app/[identityId]/item/[id]/_components/cheki-detail-page';
import { getItem } from '@/app/actions/shopItem';
import { getUserIdentityId } from '@/utils/base';
import { handleHttpError } from '@/utils/errorHandler';
import { GachaItem } from '@/types/gacha';
import { ITEM_TYPE, GACHA_ITEM_TYPES } from '@/types/item';
import { ShopItemDetail } from '@/types/shopItem';

// Server Componentとしてデータフェッチを実装
async function Detail({ params }: { params: { id: string; identityId: string } }) {
  const itemId = params.id;
  const identityId = getUserIdentityId(params.identityId);

  try {
    // サービス層の関数を使用してデータフェッチ
    // Shop情報はorvalで生成されたAPIクライアントを使用
    // Item情報は取得後に成形等の複雑な処理をしているためApiRoute経由で取得
    const [shopInfoResponse, shopItem] = await Promise.all([getShop(identityId), getItem(itemId, identityId)]);
    const errors = shopInfoResponse.errors || [];
    if (errors.length > 0 || !shopInfoResponse.data) {
      return notFound();
    }
    const shop = shopInfoResponse.data.shop;
    const detailItem = await shopItem.json().then((res) => {
      if (res.error) {
        return notFound();
      }
      return res.data;
    });

    const { available, itemType } = detailItem.item;
    if (!available) {
      redirect(`/@${identityId}`);
    }
    return (
      <>
        {itemType === ITEM_TYPE.DIGITAL_BUNDLE.value && (
          <DigitalBundleDetail
            data={detailItem as ShopItemDetail}
            itemId={itemId}
            identityId={identityId}
            shopName={shop.name}
            creatorName={shop.creatorName}
          />
        )}
        {GACHA_ITEM_TYPES.includes(itemType) && (
          <GachaDetail
            data={detailItem as GachaItem}
            itemId={itemId}
            identityId={identityId}
            shopName={shop.name}
            creatorName={shop.creatorName}
          />
        )}
        {itemType === ITEM_TYPE.CHEKI.value && (
          <ChekiDetail
            data={detailItem as ShopItemDetail}
            itemId={itemId}
            identityId={identityId}
            shopName={shop.name}
            creatorName={shop.creatorName}
          />
        )}
      </>
    );
  } catch (error) {
    handleHttpError(error);
  }
}

export default Detail;
