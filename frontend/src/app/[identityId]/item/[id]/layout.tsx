import { ReactNode } from 'react';
import { Metadata, ResolvingMetadata } from 'next';
import { getItem } from '@/app/actions/shopItem';
import { getUserIdentityId } from '@/utils/base';
import { ShopItemDetail } from '@/types/shopItem';

type Props = {
  children: ReactNode;
  params: {
    identityId: string;
    id: string;
  };
};
export async function generateMetadata({ params }: Props, parent: ResolvingMetadata): Promise<Metadata> {
  const identityId = getUserIdentityId(params.identityId);
  const itemResponse = await getItem(params.id, identityId, false);
  const itemData: ShopItemDetail = await itemResponse.json().then(async (res) => res.data);
  if (!itemData) return {};
  const parentMetadata = await parent;

  const item = itemData.item;
  return {
    title: item.title,
    description: item.description,
    openGraph: {
      title: item.title,
      description: item.description,
      url: `${process.env.NEXT_PUBLIC_BASE_URL}/@${identityId}/item/${params.id}`,
      type: 'website',
      // 商品のサムネイルは縦長のものもあり見栄えが悪いこともあり、SHOPの画像をOGPとして使うことに決まった
      // これを商品の画像にするには、クリエイターがOGPサイズの商品画像を入稿させるなどの対応が必要という判断
      images: parentMetadata?.openGraph?.images || item.thumbnail,
    },
    // authors などのクリエイター情報は上位のlayout.tsxで設定想定
  };
}

export default function Layout({ children }: Props) {
  return <>{children}</>;
}
