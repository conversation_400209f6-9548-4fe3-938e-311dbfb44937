'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import PurchaseGuideButton from '@/components/atoms/button/purchase-guide-button';
import PulldownFilter, { type FilterOption } from '@/components/containers/PulldownFilter';
import ShopItemList from '@/components/containers/ShopItem/ShopItemList';
import { getFilterData, itemTypeFilterData, FILTER_TYPE, type FilterType } from '@/consts/filter-data';
import { ShopItemType } from '@/types/shopItem';

type NewItemSectionProps = {
  items: ShopItemType[];
  identityId: string;
};

const NewItemSection = ({ items, identityId }: NewItemSectionProps) => {
  const [filteredItems, setFilteredItems] = useState<ShopItemType[]>(items);
  const [currentFilterType, setCurrentFilterType] = useState<FilterType>(FILTER_TYPE.recommend);

  const filterData = getFilterData();

  const selectedFilterOption = filterData.find((filter) => filter.value === currentFilterType);

  const defaultFilter = filterData.find((filter) => filter.default);

  const sortedItemsByDate = useMemo(() => {
    return [...items].sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  }, [items]);

  const filteredItemsByType = useMemo(() => {
    const getFilteredItems = (filterKey: keyof typeof itemTypeFilterData): ShopItemType[] => {
      const allowedTypes = itemTypeFilterData[filterKey].map((type) => type.value);
      return items.filter((item) => allowedTypes.includes(item.itemType));
    };

    return {
      digitalItem: getFilteredItems('digitalItem'),
      digitalGacha: getFilteredItems('digitalGacha'),
      printGacha: getFilteredItems('printGacha'),
      realPhoto: getFilteredItems('realPhoto'),
      physicalItem: getFilteredItems('physicalItem'),
    };
  }, [items]);

  const applyFilter = useCallback(
    (filterType: FilterType): ShopItemType[] => {
      switch (filterType) {
        case FILTER_TYPE.recommend:
          return items;

        case FILTER_TYPE.new:
          return sortedItemsByDate;

        case FILTER_TYPE.digitalItem:
          return filteredItemsByType.digitalItem;

        case FILTER_TYPE.digitalGacha:
          return filteredItemsByType.digitalGacha;

        case FILTER_TYPE.printGacha:
          return filteredItemsByType.printGacha;

        case FILTER_TYPE.realPhoto:
          return filteredItemsByType.realPhoto;

        case FILTER_TYPE.physical:
          return filteredItemsByType.physicalItem;

        default:
          return items;
      }
    },
    [items, sortedItemsByDate, filteredItemsByType],
  );

  const handleFilterChange = (tabId: string, selectedOption: FilterOption) => {
    setCurrentFilterType(selectedOption.value as FilterType);
  };

  useEffect(() => {
    const filtered = applyFilter(currentFilterType);
    setFilteredItems(filtered);
  }, [currentFilterType, items, applyFilter]);

  const updatedTabs = [
    {
      id: 'filter',
      label: selectedFilterOption?.label || defaultFilter?.label || '',
      options: filterData,
    },
  ];

  return (
    <section className="w-full">
      {/*<div className="mb-2 flex w-full justify-between">*/}
      {/*   <h3 className="text-bold-20">新着商品</h3>*/}
      {/*  <div className="flex items-center">*/}
      {/*    <span className="mr-2 text-regular-11">See More</span>*/}
      {/*    <ShopPublicImage src="/images/icons/ViewMore.svg" alt="View More" width={24} height={24} />*/}
      {/*  </div> */}
      {/*</div>*/}
      {items.length > 0 && (
        <div className="mb-4 mt-2 flex items-center justify-between">
          <div className="flex w-full items-center justify-between">
            <PurchaseGuideButton />
            <PulldownFilter
              tabs={updatedTabs}
              onFilterChange={handleFilterChange}
              initialSelectedOption={selectedFilterOption}
            />
          </div>
        </div>
      )}
      <ShopItemList items={filteredItems} identityId={identityId} />
    </section>
  );
};

export default NewItemSection;
