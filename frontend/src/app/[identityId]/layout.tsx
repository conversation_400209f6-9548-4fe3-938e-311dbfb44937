import { ReactNode } from 'react';
import { Metada<PERSON> } from 'next';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '@/components/common/consent-checker';
import { getShopInfo } from '@/app/actions/shopInfo';
import { getUserIdentityId } from '@/utils/base';
import { Shop } from '@/types/shopinfo';

type Props = {
  children: ReactNode;
  params: {
    identityId: string;
  };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const identityId = getUserIdentityId(params.identityId);
  const shopResponse = await getShopInfo(identityId);
  const shopInfo: Shop = await shopResponse.json().then(async (res) => {
    if (res.error) {
      return {};
    }
    return res.data.shop;
  });
  if (!shopInfo) return {};

  return {
    title: shopInfo.name,
    description: shopInfo.description,
    keywords: `${shopInfo.creatorName},@${identityId},リンク,まとめ,推し活,推し,FANME,ファンミー,ファンミ,ふぁんみ,ふぁんみー,link in bio,links,link,portfolio,profile,プロフィール,クリエーター,タレント`,
    openGraph: {
      title: shopInfo.name,
      description: shopInfo.description,
      url: `${process.env.NEXT_PUBLIC_BASE_URL}/@${identityId}`,
      type: 'website',
      images: shopInfo.headerImageUri,
    },
    authors: [{ name: shopInfo.creatorName }],
    icons: {
      icon: shopInfo.creatorIconUri,
      apple: shopInfo.creatorIconUri,
    },
  };
}

export default function Layout({ children }: Props) {
  return (
    <>
      <ConsentChecker />
      {children}
    </>
  );
}
