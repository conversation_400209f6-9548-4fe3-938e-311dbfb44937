'use client';

import React from 'react';
import PulldownFilter, { FilterTab, FilterOption } from '@/components/containers/PulldownFilter/index';

// サンプルデータ - デフォルトオープンの並び替えフィルター
const sampleTabs: FilterTab[] = [
  {
    id: 'sort',
    label: '並び替え',
    options: [
      { id: 'recommend', label: 'オススメ', value: 'recommend' },
      { id: 'digital-item', label: 'デジタル商品', value: 'digital-item' },
      { id: 'digital-gacha', label: 'デジタルガチャ', value: 'digital-gacha' },
      { id: 'physical', label: '物販', value: 'physical' },
      { id: 'print-service', label: 'プリント便', value: 'print-service' },
      { id: 'print-gacha', label: 'プリントガチャ', value: 'print-gacha' },
      { id: 'new', label: '新着', value: 'new' },
      { id: 'popular', label: '人気順', value: 'popular' },
    ],
  },
];

const PulldownFilterSample = () => {
  const handleFilterChange = (tabId: string, selectedOption: FilterOption) => {
    console.log('フィルター変更:', { tabId, selectedOption });
  };

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <div className="mb-8">
        <h1 className="mb-4 text-2xl font-bold">PulldownFilter コンポーネント サンプル</h1>
        <p className="mb-6 text-gray-600">
          Figmaデザインに基づくプルダウンフィルターコンポーネントです。デフォルトでオープンしており、縦型のリスト表示になっています。
          オプション選択後、ヘッダーに選択されたオプション名が表示され、アイコンとテキストがオレンジ色に変わります。
        </p>

        {/* メインサンプル */}
        <div className="rounded-lg bg-white p-6 shadow-sm">
          <h2 className="mb-4 text-lg font-semibold">基本的な使用方法</h2>
          <PulldownFilter tabs={sampleTabs} onFilterChange={handleFilterChange} className="mb-4" />
        </div>

        {/* コード例 */}
        <div className="mt-8 rounded-lg bg-gray-800 p-6 text-white">
          <h3 className="mb-4 text-lg font-semibold">使用コード例</h3>
          <pre className="overflow-x-auto text-sm">
            {`import PulldownFilter from '@/components/containers/PulldownFilter';

// 縦型のデフォルトオープンフィルター
const tabs = [
  {
    id: 'sort',
    label: '並び替え',
    options: [
      { id: 'recommend', label: 'オススメ', value: 'recommend' },
      { id: 'digital-item', label: 'デジタル商品', value: 'digital-item' },
      { id: 'digital-gacha', label: 'デジタルガチャ', value: 'digital-gacha' },
      { id: 'physical', label: '物販', value: 'physical' },
      { id: 'new', label: '新着', value: 'new' },
      { id: 'popular', label: '人気順', value: 'popular' },
      // ... その他のオプション
    ],
  },
];

<PulldownFilter
  tabs={tabs}
  onFilterChange={(tabId, option) => {
    console.log('選択されたフィルター:', { tabId, option });
  }}
/>`}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default PulldownFilterSample;
