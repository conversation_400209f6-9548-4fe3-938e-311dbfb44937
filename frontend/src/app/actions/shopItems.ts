'use server';
import createError from 'http-errors';
import { NextResponse } from 'next/server';
import { ShopItemBk } from '@/app/api/types/backend/shop/ShopItem';
import { ServerFetchClient } from '@/services/server/fetch-client';
import { ApiResponse } from '@/types/api';
import { ShopItemType, ItemType } from '@/types/shopItem';

export const getItems = async (
  identityId: string,
): Promise<
  NextResponse<{
    data: ShopItemType[];
    statusText: string;
  }>
> => {
  const fetchClient = new ServerFetchClient();
  const shopItemsPath = `/shops/${identityId}/items`;

  try {
    const res: ApiResponse<{ items: ShopItemBk[] }> = await fetchClient.get(shopItemsPath, {
      next: { tags: [`shop-items-${identityId}`] },
    });
    if (!res.data) {
      throw createError(404, 'Item not found');
    }

    const items: ShopItemType[] = res.data.items.map((item: ShopItemBk) => {
      return {
        id: item.id,
        title: item.name,
        available: !!item.available,
        isNew: item.is_new,
        price: item.price,
        minPrice: item.min_price || 0,
        currentPrice: item.current_price,
        thumbnail: item.thumbnail_uri,
        description: item.description,
        fileType: item.file_type,
        itemType: item.item_type as ItemType,
        hasBenefit: item.has_benefit,
        hasPassword: item.item_option.password ? true : false,
        isCompleted: item.is_completed,
        collectedUniqueItemsCount: item.collected_unique_items_count,
        isPurchased: item.is_purchased,
        isCheckout: item.is_checkout,
        createdAt: item.created_at,
        onSale: {
          startAt: item.item_option.on_sale ? item.item_option.on_sale.start_at : undefined,
          endAt: item.item_option.on_sale ? item.item_option.on_sale.end_at : undefined,
          discountRate: item.item_option.on_sale ? item.item_option.on_sale.discount_rate * 100 : undefined,
        },
        forSale: {
          startAt: item.item_option.for_sale ? item.item_option.for_sale.start_at : undefined,
          endAt: item.item_option.for_sale ? item.item_option.for_sale.end_at : undefined,
        },
        isSingleSales: item.item_option.is_single_sales,
        stockCount: item.item_option.remaining_amount ?? undefined,
        fileQuantities:
          item.file_quantities?.map((fileQuantity) => ({
            fileType: fileQuantity.file_type,
            quantity: fileQuantity.quantity,
          })) || [],
      };
    });

    return NextResponse.json({ data: items, statusText: 'success' });
  } catch (error: any) {
    console.error(JSON.stringify({ error }));
    return new NextResponse(error, { status: 500 });
  }
};
