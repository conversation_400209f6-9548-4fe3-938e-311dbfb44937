'use client';

import React, { useState, useRef, Suspense } from 'react';
import ReactMarkdown from 'react-markdown';
import { useRouter, useSearchParams } from 'next/navigation';
import Button from '@/components/atoms/button';
import { useCreateUserConsent, useGetConsents } from '@/lib/fanme-api/fanme/fanme';
import { Consent1 } from '@/lib/fanme-api/fanme-api.schemas';

const TermsConsentContent = () => {
  const [loading, setLoading] = useState(false);
  const [hasScrolledToBottom, setHasScrolledToBottom] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { trigger: createUserConsent } = useCreateUserConsent();
  const { mutate } = useGetConsents();
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const termsPageUrl = `${window.location.origin}/terms`;

  const handleIframeScroll = () => {
    const iframe = iframeRef.current;
    if (!iframe || !iframe.contentWindow) return;

    const iframeDocument = iframe.contentWindow.document;
    const scrollTop = iframeDocument.documentElement.scrollTop || iframeDocument.body.scrollTop;
    const scrollHeight = iframeDocument.documentElement.scrollHeight || iframeDocument.body.scrollHeight;
    const clientHeight = iframeDocument.documentElement.clientHeight || iframeDocument.body.clientHeight;

    if (scrollTop + clientHeight >= scrollHeight - 10) {
      setHasScrolledToBottom(true);
    }
  };

  const handleConsent = async () => {
    setLoading(true);
    const consentName = Consent1.TERMS_20250801.valueOf();

    await createUserConsent({ consentName });
    await mutate();

    const returnUrl = searchParams.get('return-url');
    if (returnUrl) {
      router.push(returnUrl);
    } else {
      router.push('/');
    }
  };

  const TERMS_UPDATE_BODY = `
いつもFANMEをご利用いただきありがとうございます。  
このたび、2025年8月1日より「利用規約」の内容を一部改定いたします。  
下記に主な変更点を記載いたしますので、ご確認のほどお願い申し上げます。

## ■ 主な変更点について

### ◆ 第1章 総則

第2条（定義）第16・17・18・21号にて、一部用語の定義を以下の通り修しました。

- **第2条（定義）第16号**  
  「商品・サービス代金」とは、当社がユーザーに対して商品・サービスを提供する際の金額を指します。  

- **第2条（定義）第17号**  
  「コンテンツ料金」とは、当社が本規約等に基づきユーザーから受領した商品・サービス代金相当額から、販売手数料（商品の原価及びプラットフォーム手数料）を控除したもので、コンテンツ提供クリエイターに支払われる金銭を指します。  

- **第2条（定義）第18号**  
  「プラットフォーム手数料」とは、当社が本サービス上でコンテンツ提供クリエイターの商品・サービスの売買を行う際に発生する手数料であり、商品・サービス代金に別途当社の定める割合を乗じた額を指します。  

- **第2条（定義）第21号**  
  「決済代金額」とは、ユーザーが当社に対して商品・サービス販売契約に基づき支払う商品・サービス代金および事務手数料（送料・システム利用料・サービス利用料）の合計金額を指します。

### ◆ 第5章 禁止事項

以下を新たに禁止事項として追加しました。

- **第17条（商業行為の禁止）第5号**  
  クリエイターおよびユーザーが、他人の商品・サービスについて本人の同意を得ることなく、商業目的で利用（使用・再生・複製・複写・販売・再販売等、形態は問いません）する行為  

- **第21条（その他禁止行為）第8号**  
  クリエイターおよびユーザーが、他人の商品・サービスにかかるデータを本人の同意を得ることなく、AI の開発・学習に利用する行為

### ◆ 第6章 商品・サービス提供等

商品の送料に関する支払方法を以下のように明記しました。

- **第23条（購入）7号**  
  ユーザーは商品・サービス販売契約が成立した場合、当社の定める方法により決済代金額を支払うものとします。なお、商品・サービスの送料が発生する場合、送料はユーザーが負担し、当該送料は決済代金額に含まれるものとします。

### ◆ 第7章 決済等

決済手段ごとの手数料についての記載を追加し、「アプリケーション手数料」を「プラットフォーム手数料」に統一しました。

- **第24条（決済）第1号**  
  当社は、ユーザーとの間で商品・サービス販売契約が成立し、ユーザーが当社所定の方法で商品・サービス代金の全部または一部にクレジットカード決済等を利用する旨を表示し、当社がこれを承認した場合、本規約等に従って適正に各種決済等を受け入れるものとします。なお、ユーザーは選択した決済手段に従い決済手数料等を負担するものとします。  

- **第25条（コンテンツ料金の計上）**  
  当社は、本規約に基づき取引完了の通知を受けた場合、商品・サービス代金から本規約に定めるプラットフォーム手数料を控除した売買金額の合計額を、当該取引に関するコンテンツ提供クリエイターのアカウント上でコンテンツ料金として計上します。

## ■ 利用規約の全文について

改定後の利用規約は、以下よりご確認いただけます。  
全文をご確認いただいた後に **「同意する」** ボタンが押せるようになります。`;

  return (
    <div className="flex flex-col items-center justify-center gap-4 p-4">
      <article className="prose prose-sm">
        <ReactMarkdown>{TERMS_UPDATE_BODY}</ReactMarkdown>
      </article>
      <iframe
        ref={iframeRef}
        src={termsPageUrl}
        className="h-96 w-full border-0"
        title="利用規約"
        onLoad={() => {
          const iframe = iframeRef.current;
          if (iframe && iframe.contentWindow) {
            iframe.contentWindow.addEventListener('scroll', handleIframeScroll);
          }
        }}
      />
      <Button
        buttonType={hasScrolledToBottom ? 'dark' : 'disabled'}
        onClick={handleConsent}
        disabled={loading || !hasScrolledToBottom}
      >
        同意する
      </Button>
    </div>
  );
};

function TermsConsentPage() {
  return (
    <Suspense fallback={<></>}>
      <TermsConsentContent />
    </Suspense>
  );
}

export default TermsConsentPage;
