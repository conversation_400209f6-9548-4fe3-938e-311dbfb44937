import React from 'react';
import { redirect } from 'next/navigation';
import CreateShopComponent from '@/components/views/CreateShop';
import { getShopInfo } from '@/app/actions/shopInfo';
import { getCurrentUser } from '@/app/actions/user';
import { getCookie } from '@/utils/cookie';

const CreateShop = async () => {
  // ミドルウェアで認証済み
  const token = (await getCookie('fanme_token-http-only')) || '';
  const currentUserRes = await getCurrentUser(token);
  const currentUser = await currentUserRes.json();
  // shop開設済みの場合はshopページにリダイレクト
  const shopInfoRes = await getShopInfo(currentUser.data.accountIdentity, true);
  const shopInfo = await shopInfoRes.json();
  const defaultMessage = `購入ありがとうございます。\n大切に保存していただけると嬉しいです！`.trim();
  if (!shopInfo.error && shopInfo.data.shop) {
    redirect(`/@${currentUser.data.accountIdentity}?editMode=true`);
  }

  return (
    <div className="pt-10">
      <p className="mb-8 text-center text-bold-17 text-orange-200">まずはショップを開設しましょう!</p>
      <CreateShopComponent messageProp={defaultMessage} />
    </div>
  );
};

export default CreateShop;
