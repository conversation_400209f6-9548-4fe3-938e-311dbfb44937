import { FeatureFlags } from '@/lib/feature';
import { ITEM_TYPE } from '@/types/shopItem';

const FILTER_TYPE = {
  recommend: 'recommend',
  new: 'new',
  digitalItem: 'digitalItem',
  digitalGacha: 'digitalGacha',
  realPhoto: 'realPhoto',
  printGacha: 'printGacha',
  physical: 'physical',
} as const;

type FilterType = (typeof FILTER_TYPE)[keyof typeof FILTER_TYPE];

const getFilterData = () => {
  const filters: Array<{
    id: string;
    label: string;
    value: FilterType;
    default?: boolean;
  }> = [
    { id: FILTER_TYPE.recommend, label: 'オススメ', value: FILTER_TYPE.recommend, default: true },
    { id: FILTER_TYPE.new, label: '新着', value: FILTER_TYPE.new },
    { id: FILTER_TYPE.digitalItem, label: 'デジタル商品', value: FILTER_TYPE.digitalItem },
    { id: FILTER_TYPE.digitalGacha, label: 'デジタルガチャ', value: FILTER_TYPE.digitalGacha },
  ];

  // realPhotoのfeature flagがオンの場合のみ追加
  if (FeatureFlags.realPhoto()) {
    filters.push({
      id: FILTER_TYPE.realPhoto,
      label: 'プリント便',
      value: FILTER_TYPE.realPhoto,
    });
  }

  // printGachaのfeature flagがオンの場合のみ追加
  if (FeatureFlags.printGacha()) {
    filters.push({
      id: FILTER_TYPE.printGacha,
      label: 'プリントガチャ',
      value: FILTER_TYPE.printGacha,
    });
  }

  // 最後に物販を追加
  filters.push({ id: FILTER_TYPE.physical, label: '物販', value: FILTER_TYPE.physical }); // チェキ、アクリル商品

  // 保留、今回対応しない
  //   { id: 'popular', label: '人気順', value: 'popular' },

  return filters;
};

const filterData = getFilterData();

const itemTypeFilterData = {
  digitalItem: [
    { id: ITEM_TYPE.digitalBundle, label: 'デジタルバンドル', value: ITEM_TYPE.digitalBundle },
    { id: ITEM_TYPE.digitalGacha, label: 'デジタルガチャ', value: ITEM_TYPE.digitalGacha },
  ],
  physicalItem: [{ id: ITEM_TYPE.cheki, label: 'チェキ', value: ITEM_TYPE.cheki }],
  digitalGacha: [{ id: ITEM_TYPE.digitalGacha, label: 'デジタルガチャ', value: ITEM_TYPE.digitalGacha }],
  printGacha: [{ id: ITEM_TYPE.printGacha, label: 'プリントガチャ', value: ITEM_TYPE.printGacha }],
  realPhoto: [{ id: ITEM_TYPE.realPhoto, label: 'プリント便', value: ITEM_TYPE.realPhoto }],
  tradingCardGacha: [
    { id: ITEM_TYPE.tradingCardGacha, label: 'トレーディングカードガチャ', value: ITEM_TYPE.tradingCardGacha },
  ],
};

export { filterData, getFilterData, itemTypeFilterData, FILTER_TYPE };
export type { FilterType };
