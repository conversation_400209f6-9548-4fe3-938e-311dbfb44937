// 画像圧縮しないサイズ　500KB
const IMAGE_COMPRESS_SIZE = 500 * 1024;
// 画像圧縮しないサイズ　5MB
const IMAGE_COMPRESS_SIZE_5MB = 5 * 1024 * 1024;

// 画像圧縮するサイズ　10MB
const IMAGE_COMPRESS_SIZE_10MB = 10 * 1024 * 1024;

const MAX_BLOB_SIZE = 500 * 1024; // 500KB

// 商品個数
const MAX_IMAGE_ITEM_COUNT = 30;
const MAX_AUDIO_ITEM_COUNT = 10;
const MAX_VIDEO_ITEM_COUNT = 10;
const MAX_ITEM_COUNT = 50;
const MAX_GACHA_ITEM_COUNT = 30;

// IMAGE
const MAX_SINGLE_IMAGE_SIZE_MB = 10;
const MAX_SINGLE_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB
const MAX_ALL_IMAGE_SIZE = MAX_IMAGE_ITEM_COUNT * MAX_SINGLE_IMAGE_SIZE; // 300MB

// AUDIO
const MAX_SINGLE_AUDIO_SIZE_MB = 100;
const MAX_SINGLE_AUDIO_SIZE = 100 * 1024 * 1024; // 100MB
const MAX_ALL_AUDIO_SIZE = MAX_AUDIO_ITEM_COUNT * MAX_SINGLE_AUDIO_SIZE; // 1000MB

// VIDEO
const MAX_SINGLE_VIDEO_SIZE_MB = 300;
const MAX_SINGLE_VIDEO_SIZE = 300 * 1024 * 1024; // 300MB
const MAX_ALL_VIDEO_SIZE = MAX_VIDEO_ITEM_COUNT * MAX_SINGLE_VIDEO_SIZE; // 3000MB

// 商品サイズ
const ITEMS_FILES_SIZE = {
  IMAGE: {
    MAX_SINGLE_IMAGE_SIZE,
    MAX_ALL_IMAGE_SIZE,
  },
  AUDIO: {
    MAX_SINGLE_AUDIO_SIZE,
    MAX_ALL_AUDIO_SIZE,
  },
  VIDEO: {
    MAX_SINGLE_VIDEO_SIZE,
    MAX_ALL_VIDEO_SIZE,
  },
};

const NEARLY_ENDED_THRESHOLD = 1000 * 60 * 60 * 72; // 72 hours

const FIXED_BAR_HEIGHT = 'h-25';

const LEAST_ITEM_COUNT = {
  1: 3,
  2: 4,
  3: 5,
  4: 6,
};

export type LeastItemCount = keyof typeof LEAST_ITEM_COUNT;

const PRINT_SIZE = {
  LL: {
    width: 89,
    height: 127,
  },
};

const TRADING_CARD_SIZE = {
  width: 740,
  height: 1039,
};

export {
  IMAGE_COMPRESS_SIZE,
  IMAGE_COMPRESS_SIZE_5MB,
  IMAGE_COMPRESS_SIZE_10MB,
  MAX_BLOB_SIZE,
  MAX_SINGLE_IMAGE_SIZE,
  MAX_ALL_IMAGE_SIZE,
  MAX_SINGLE_AUDIO_SIZE,
  MAX_ALL_AUDIO_SIZE,
  MAX_SINGLE_VIDEO_SIZE,
  MAX_ALL_VIDEO_SIZE,
  ITEMS_FILES_SIZE,
  MAX_IMAGE_ITEM_COUNT,
  MAX_AUDIO_ITEM_COUNT,
  MAX_VIDEO_ITEM_COUNT,
  MAX_ITEM_COUNT,
  MAX_GACHA_ITEM_COUNT,
  FIXED_BAR_HEIGHT,
  MAX_SINGLE_IMAGE_SIZE_MB,
  MAX_SINGLE_AUDIO_SIZE_MB,
  MAX_SINGLE_VIDEO_SIZE_MB,
  NEARLY_ENDED_THRESHOLD,
  LEAST_ITEM_COUNT,
  PRINT_SIZE,
  TRADING_CARD_SIZE,
};
