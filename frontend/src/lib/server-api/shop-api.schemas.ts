/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
export type ActionType = (typeof ActionType)[keyof typeof ActionType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const ActionType = {
  NONE: 'NONE',
  MARGIN_TYPE1: 'MARGIN_TYPE1',
} as const;

export type AgencyId = number | null;

export type AgencyCreatedAt = Instant | null;

export type AgencyUpdatedAt = Instant | null;

export type AgencyDeletedAt = Instant | null;

export interface Agency {
  id?: AgencyId;
  createdAt: AgencyCreatedAt;
  updatedAt: AgencyUpdatedAt;
  name: string;
  deletedAt?: AgencyDeletedAt;
}

export type AgencyMonthlySalesMonthOverMonthGrowthRate = number | null;

export interface AgencyMonthlySales {
  yearMonth: string;
  totalSalesAmount?: number;
  totalPurchaserCount?: number;
  totalPurchaseCount?: number;
  monthOverMonthGrowthRate?: AgencyMonthlySalesMonthOverMonthGrowthRate;
}

export interface AgencySales {
  totalSales: TotalSales;
  usersSales: UserSales[];
}

export interface AllShopItemsResponse {
  shop: ShopForGetShop;
  items: Item[];
}

export interface ApplePayParam {
  token: string;
}

export type AssetType = (typeof AssetType)[keyof typeof AssetType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const AssetType = {
  IMAGE: 'IMAGE',
  VOICE: 'VOICE',
  MOVIE: 'MOVIE',
  ANY: 'ANY',
} as const;

export type AuditGroupId = number | null;

export type AuditGroupCreatedAt = Instant | null;

export type AuditGroupUpdatedAt = Instant | null;

export type AuditGroupUser = User | null;

export type AuditGroupItemId = number | null;

export type AuditGroupMetadataProperty = string | null;

export type AuditGroupComment = string | null;

export type AuditGroupAuditedAt = LocalDateTime | null;

/**
 * @maxLength 50
 */
export type AuditGroupAuditedUserUid = string | null;

export type AuditGroupMetadataObject = AuditGroupMetadata | null;

export interface AuditGroup {
  id?: AuditGroupId;
  createdAt: AuditGroupCreatedAt;
  updatedAt: AuditGroupUpdatedAt;
  /** @maxLength 50 */
  userUid: string;
  user?: AuditGroupUser;
  auditType: AuditType;
  operationType: OperationType;
  itemId?: AuditGroupItemId;
  metadata?: AuditGroupMetadataProperty;
  status: AuditStatus;
  comment?: AuditGroupComment;
  auditedAt?: AuditGroupAuditedAt;
  /** @maxLength 50 */
  auditedUserUid?: AuditGroupAuditedUserUid;
  metadataObject?: AuditGroupMetadataObject;
}

/**
 * ショップID
 */
export type AuditGroupMetadataShopId = number | null;

/**
 * 商品ID
 */
export type AuditGroupMetadataItemId = string | null;

/**
 * タイトル
 */
export type AuditGroupMetadataTitle = string | null;

/**
 * 説明
 */
export type AuditGroupMetadataDescription = string | null;

export interface AuditGroupMetadata {
  /** ショップID */
  shopId?: AuditGroupMetadataShopId;
  /** 商品ID */
  itemId?: AuditGroupMetadataItemId;
  /** タイトル */
  title?: AuditGroupMetadataTitle;
  /** 説明 */
  description?: AuditGroupMetadataDescription;
}

export type AuditGroupsDataTotalCount = number | null;

export interface AuditGroupsData {
  auditGroups: AuditGroup[];
  totalCount?: AuditGroupsDataTotalCount;
}

export type AuditGroupsResponseData = AuditGroupsData | null;

export type AuditGroupsResponseErrors = ErrorObject[] | null;

export type AuditGroupsResponseMetadataAnyOf = { [key: string]: unknown };

export type AuditGroupsResponseMetadata = AuditGroupsResponseMetadataAnyOf | null;

export interface AuditGroupsResponse {
  data?: AuditGroupsResponseData;
  errors?: AuditGroupsResponseErrors;
  metadata?: AuditGroupsResponseMetadata;
}

export type AuditObjectId = number | null;

export type AuditObjectCreatedAt = Instant | null;

export type AuditObjectUpdatedAt = Instant | null;

export type AuditObjectAuditGroup = AuditGroup | null;

export interface AuditObject {
  id?: AuditObjectId;
  createdAt: AuditObjectCreatedAt;
  updatedAt: AuditObjectUpdatedAt;
  auditGroup?: AuditObjectAuditGroup;
  auditGroupId: number;
  /** @maxLength 50 */
  bucket: string;
  /** @maxLength 255 */
  filePath: string;
  assetType: AssetType;
  fileUrl: string;
}

export interface AuditObjectsData {
  auditObjects: AuditObject[];
}

export type AuditObjectsResponseData = AuditObjectsData | null;

export type AuditObjectsResponseErrors = ErrorObject[] | null;

export type AuditObjectsResponseMetadataAnyOf = { [key: string]: unknown };

export type AuditObjectsResponseMetadata = AuditObjectsResponseMetadataAnyOf | null;

export interface AuditObjectsResponse {
  data?: AuditObjectsResponseData;
  errors?: AuditObjectsResponseErrors;
  metadata?: AuditObjectsResponseMetadata;
}

export type AuditStatus = (typeof AuditStatus)[keyof typeof AuditStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const AuditStatus = {
  UNAUDITED: 'UNAUDITED',
  REJECTED: 'REJECTED',
  PENDING: 'PENDING',
  RESEND: 'RESEND',
  APPROVED: 'APPROVED',
} as const;

export interface AuditStatusData {
  result?: boolean;
}

export type AuditStatusResponseBodyMetadataAnyOf = { [key: string]: unknown };

export type AuditStatusResponseBodyMetadata = AuditStatusResponseBodyMetadataAnyOf | null;

export type AuditStatusResponseBodyData = AuditStatusData | null;

export type AuditStatusResponseBodyErrors = ErrorObject[] | null;

export interface AuditStatusResponseBody {
  metadata?: AuditStatusResponseBodyMetadata;
  data: AuditStatusResponseBodyData;
  errors?: AuditStatusResponseBodyErrors;
}

export type AuditType = (typeof AuditType)[keyof typeof AuditType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const AuditType = {
  SHOP: 'SHOP',
  SHOP_ITEM: 'SHOP_ITEM',
  FANME_PROFILE: 'FANME_PROFILE',
  FANME_CONTENT: 'FANME_CONTENT',
} as const;

export interface AwardProbability {
  awardType?: number;
  probability?: number;
}

export interface AwardProbability1 {
  awardType?: number;
  probability?: number;
}

export interface BadgeRankingData {
  ranking: GetGachaCompleteBadgeRankingResponse[];
}

export type BadgeRankingResponseBodyMetadataAnyOf = { [key: string]: unknown };

export type BadgeRankingResponseBodyMetadata = BadgeRankingResponseBodyMetadataAnyOf | null;

export type BadgeRankingResponseBodyData = BadgeRankingData | null;

export type BadgeRankingResponseBodyErrors = ErrorObject[] | null;

export interface BadgeRankingResponseBody {
  metadata?: BadgeRankingResponseBodyMetadata;
  data: BadgeRankingResponseBodyData;
  errors?: BadgeRankingResponseBodyErrors;
}

export type BaseResponseBodyContentBlockDetailResponseData = ContentBlockDetailResponse | null;

export type BaseResponseBodyContentBlockDetailResponseErrors = ErrorObject[] | null;

export type BaseResponseBodyContentBlockDetailResponseMetadataAnyOf = { [key: string]: unknown };

export type BaseResponseBodyContentBlockDetailResponseMetadata =
  BaseResponseBodyContentBlockDetailResponseMetadataAnyOf | null;

export interface BaseResponseBodyContentBlockDetailResponse {
  data?: BaseResponseBodyContentBlockDetailResponseData;
  errors?: BaseResponseBodyContentBlockDetailResponseErrors;
  metadata?: BaseResponseBodyContentBlockDetailResponseMetadata;
}

export type BaseResponseBodyContentBlockResponseData = ContentBlockResponse | null;

export type BaseResponseBodyContentBlockResponseErrors = ErrorObject[] | null;

export type BaseResponseBodyContentBlockResponseMetadataAnyOf = { [key: string]: unknown };

export type BaseResponseBodyContentBlockResponseMetadata = BaseResponseBodyContentBlockResponseMetadataAnyOf | null;

export interface BaseResponseBodyContentBlockResponse {
  data?: BaseResponseBodyContentBlockResponseData;
  errors?: BaseResponseBodyContentBlockResponseErrors;
  metadata?: BaseResponseBodyContentBlockResponseMetadata;
}

export type BaseResponseBodyContentBlocksData = ContentBlocks | null;

export type BaseResponseBodyContentBlocksErrors = ErrorObject[] | null;

export type BaseResponseBodyContentBlocksMetadataAnyOf = { [key: string]: unknown };

export type BaseResponseBodyContentBlocksMetadata = BaseResponseBodyContentBlocksMetadataAnyOf | null;

export interface BaseResponseBodyContentBlocks {
  data?: BaseResponseBodyContentBlocksData;
  errors?: BaseResponseBodyContentBlocksErrors;
  metadata?: BaseResponseBodyContentBlocksMetadata;
}

export type BaseResponseBodyCreateUserConsentResponseData = CreateUserConsentResponse | null;

export type BaseResponseBodyCreateUserConsentResponseErrors = ErrorObject[] | null;

export type BaseResponseBodyCreateUserConsentResponseMetadataAnyOf = { [key: string]: unknown };

export type BaseResponseBodyCreateUserConsentResponseMetadata =
  BaseResponseBodyCreateUserConsentResponseMetadataAnyOf | null;

export interface BaseResponseBodyCreateUserConsentResponse {
  data?: BaseResponseBodyCreateUserConsentResponseData;
  errors?: BaseResponseBodyCreateUserConsentResponseErrors;
  metadata?: BaseResponseBodyCreateUserConsentResponseMetadata;
}

export type BaseResponseBodyGetConsentsResponseData = GetConsentsResponse | null;

export type BaseResponseBodyGetConsentsResponseErrors = ErrorObject[] | null;

export type BaseResponseBodyGetConsentsResponseMetadataAnyOf = { [key: string]: unknown };

export type BaseResponseBodyGetConsentsResponseMetadata = BaseResponseBodyGetConsentsResponseMetadataAnyOf | null;

export interface BaseResponseBodyGetConsentsResponse {
  data?: BaseResponseBodyGetConsentsResponseData;
  errors?: BaseResponseBodyGetConsentsResponseErrors;
  metadata?: BaseResponseBodyGetConsentsResponseMetadata;
}

export type BaseResponseBodyItemData = Item | null;

export type BaseResponseBodyItemErrors = ErrorObject[] | null;

export type BaseResponseBodyItemMetadataAnyOf = { [key: string]: unknown };

export type BaseResponseBodyItemMetadata = BaseResponseBodyItemMetadataAnyOf | null;

export interface BaseResponseBodyItem {
  data?: BaseResponseBodyItemData;
  errors?: BaseResponseBodyItemErrors;
  metadata?: BaseResponseBodyItemMetadata;
}

export type BaseResponseBodyListItemMarginRateResultData = ItemMarginRateResult[] | null;

export type BaseResponseBodyListItemMarginRateResultErrors = ErrorObject[] | null;

export type BaseResponseBodyListItemMarginRateResultMetadataAnyOf = { [key: string]: unknown };

export type BaseResponseBodyListItemMarginRateResultMetadata =
  BaseResponseBodyListItemMarginRateResultMetadataAnyOf | null;

export interface BaseResponseBodyListItemMarginRateResult {
  data?: BaseResponseBodyListItemMarginRateResultData;
  errors?: BaseResponseBodyListItemMarginRateResultErrors;
  metadata?: BaseResponseBodyListItemMarginRateResultMetadata;
}

export type BaseResponseBodyListShopItemTypeMarginRateResultData = ShopItemTypeMarginRateResult[] | null;

export type BaseResponseBodyListShopItemTypeMarginRateResultErrors = ErrorObject[] | null;

export type BaseResponseBodyListShopItemTypeMarginRateResultMetadataAnyOf = { [key: string]: unknown };

export type BaseResponseBodyListShopItemTypeMarginRateResultMetadata =
  BaseResponseBodyListShopItemTypeMarginRateResultMetadataAnyOf | null;

export interface BaseResponseBodyListShopItemTypeMarginRateResult {
  data?: BaseResponseBodyListShopItemTypeMarginRateResultData;
  errors?: BaseResponseBodyListShopItemTypeMarginRateResultErrors;
  metadata?: BaseResponseBodyListShopItemTypeMarginRateResultMetadata;
}

export type BaseResponseBodyUserData = User | null;

export type BaseResponseBodyUserErrors = ErrorObject[] | null;

export type BaseResponseBodyUserMetadataAnyOf = { [key: string]: unknown };

export type BaseResponseBodyUserMetadata = BaseResponseBodyUserMetadataAnyOf | null;

export interface BaseResponseBodyUser {
  data?: BaseResponseBodyUserData;
  errors?: BaseResponseBodyUserErrors;
  metadata?: BaseResponseBodyUserMetadata;
}

export type BaseResponseBodyUserProfileResponseData = UserProfileResponse | null;

export type BaseResponseBodyUserProfileResponseErrors = ErrorObject[] | null;

export type BaseResponseBodyUserProfileResponseMetadataAnyOf = { [key: string]: unknown };

export type BaseResponseBodyUserProfileResponseMetadata = BaseResponseBodyUserProfileResponseMetadataAnyOf | null;

export interface BaseResponseBodyUserProfileResponse {
  data?: BaseResponseBodyUserProfileResponseData;
  errors?: BaseResponseBodyUserProfileResponseErrors;
  metadata?: BaseResponseBodyUserProfileResponseMetadata;
}

export type BaseResponseBodyUserResponseData = UserResponse | null;

export type BaseResponseBodyUserResponseErrors = ErrorObject[] | null;

export type BaseResponseBodyUserResponseMetadataAnyOf = { [key: string]: unknown };

export type BaseResponseBodyUserResponseMetadata = BaseResponseBodyUserResponseMetadataAnyOf | null;

export interface BaseResponseBodyUserResponse {
  data?: BaseResponseBodyUserResponseData;
  errors?: BaseResponseBodyUserResponseErrors;
  metadata?: BaseResponseBodyUserResponseMetadata;
}

export type BaseResponseBodyUsersByPartialAccountIdentityResponseData = UsersByPartialAccountIdentityResponse | null;

export type BaseResponseBodyUsersByPartialAccountIdentityResponseErrors = ErrorObject[] | null;

export type BaseResponseBodyUsersByPartialAccountIdentityResponseMetadataAnyOf = { [key: string]: unknown };

export type BaseResponseBodyUsersByPartialAccountIdentityResponseMetadata =
  BaseResponseBodyUsersByPartialAccountIdentityResponseMetadataAnyOf | null;

export interface BaseResponseBodyUsersByPartialAccountIdentityResponse {
  data?: BaseResponseBodyUsersByPartialAccountIdentityResponseData;
  errors?: BaseResponseBodyUsersByPartialAccountIdentityResponseErrors;
  metadata?: BaseResponseBodyUsersByPartialAccountIdentityResponseMetadata;
}

export type BenefitFileId = number | null;

export type BenefitFileObjectUri = string | null;

export type BenefitFileThumbnailUri = string | null;

export type BenefitFileDuration = number | null;

export type BenefitFileItemThumbnailSelected = boolean | null;

export type BenefitFileSortOrder = number | null;

export interface BenefitFile {
  id?: BenefitFileId;
  name: string;
  objectUri?: BenefitFileObjectUri;
  thumbnailUri?: BenefitFileThumbnailUri;
  fileType: string;
  size?: number;
  duration?: BenefitFileDuration;
  itemThumbnailSelected?: BenefitFileItemThumbnailSelected;
  sortOrder?: BenefitFileSortOrder;
}

export type BenefitParamDescription = string | null;

export type BenefitParamFiles = BenefitFile[] | null;

export interface BenefitParam {
  id?: number;
  description?: BenefitParamDescription;
  conditionType?: number;
  files?: BenefitParamFiles;
}

export type CampaignId = number | null;

export type CampaignCreatedAt = Instant | null;

export type CampaignUpdatedAt = Instant | null;

export type CampaignUser = User | null;

export interface Campaign {
  id?: CampaignId;
  createdAt: CampaignCreatedAt;
  updatedAt: CampaignUpdatedAt;
  campaignIdentity: string;
  title: string;
  user?: CampaignUser;
  entryType: EntryType;
  actionType: ActionType;
  actionDurationDays?: number;
  startAt: Instant;
  endAt: Instant;
}

export interface CardParam {
  cardSequence?: number;
}

export type CartItemFileId = number | null;

export type CartItemFileType = string | null;

export type CartItemFileQuantities = FileQuantity[] | null;

export type CartItemPurchasableQuantity = number | null;

export type CartItemPurchaserComment = string | null;

export interface CartItem {
  cartItemId?: number;
  cartId?: number;
  itemType?: number;
  itemId?: number;
  fileId?: CartItemFileId;
  quantity?: number;
  name: string;
  thumbnailUri: string;
  price?: number;
  marginRate?: number;
  currentPrice?: number;
  discountRate?: number;
  fileType?: CartItemFileType;
  fileQuantities?: CartItemFileQuantities;
  forSale?: boolean;
  soldOut?: boolean;
  purchasableQuantity?: CartItemPurchasableQuantity;
  purchaserComment?: CartItemPurchaserComment;
}

export type CartItemDataDeliveryFee = number | null;

export interface CartItemData {
  cartItems: CartItem[];
  deliveryFee?: CartItemDataDeliveryFee;
  isLocked?: boolean;
}

export type CartItemsResponseBodyErrors = ErrorObject[] | null;

export interface CartItemsResponseBody {
  cart: CartItemData;
  errors?: CartItemsResponseBodyErrors;
}

export interface CheckCartItemPriceRequest {
  itemPrices: ItemPriceSet[];
}

export type CheckoutStatus = (typeof CheckoutStatus)[keyof typeof CheckoutStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CheckoutStatus = {
  UNPROCESSED: 'UNPROCESSED',
  REQSUCCESS: 'REQSUCCESS',
  PAYSUCCESS: 'PAYSUCCESS',
  EXPIRED: 'EXPIRED',
  CANCEL: 'CANCEL',
  PAYFAILED: 'PAYFAILED',
} as const;

export interface CompleteBadgeData {
  badge: Output1;
}

export type CompleteBadgeResponseBodyMetadataAnyOf = { [key: string]: unknown };

export type CompleteBadgeResponseBodyMetadata = CompleteBadgeResponseBodyMetadataAnyOf | null;

export type CompleteBadgeResponseBodyData = CompleteBadgeData | null;

export type CompleteBadgeResponseBodyErrors = ErrorObject[] | null;

export interface CompleteBadgeResponseBody {
  metadata?: CompleteBadgeResponseBodyMetadata;
  data: CompleteBadgeResponseBodyData;
  errors?: CompleteBadgeResponseBodyErrors;
}

export interface Consent {
  name: string;
  url: string;
  required?: boolean;
}

export type Consent1 = (typeof Consent1)[keyof typeof Consent1];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const Consent1 = {
  TERMS_20250801: 'TERMS_20250801',
} as const;

export type ConsoleGetUserResponseAgencyId = number | null;

export interface ConsoleGetUserResponse {
  user: User;
  role: string;
  agencyId?: ConsoleGetUserResponseAgencyId;
}

export type ConsoleUserId = number | null;

export type ConsoleUserCreatedAt = Instant | null;

export type ConsoleUserUpdatedAt = Instant | null;

export type ConsoleUserAgencyId = number | null;

export type ConsoleUserAgency = Agency | null;

export type ConsoleUserDeletedAt = Instant | null;

export interface ConsoleUser {
  id?: ConsoleUserId;
  createdAt: ConsoleUserCreatedAt;
  updatedAt: ConsoleUserUpdatedAt;
  user: User;
  agencyId?: ConsoleUserAgencyId;
  agency?: ConsoleUserAgency;
  role: string;
  deletedAt?: ConsoleUserDeletedAt;
}

export interface ConsoleUsersData {
  consoleUsers: ConsoleUser[];
}

export type ConsoleUsersResponseBodyMetadataAnyOf = { [key: string]: unknown };

export type ConsoleUsersResponseBodyMetadata = ConsoleUsersResponseBodyMetadataAnyOf | null;

export type ConsoleUsersResponseBodyData = ConsoleUsersData | null;

export type ConsoleUsersResponseBodyErrors = ErrorObject[] | null;

export interface ConsoleUsersResponseBody {
  metadata?: ConsoleUsersResponseBodyMetadata;
  data: ConsoleUsersResponseBodyData;
  errors?: ConsoleUsersResponseBodyErrors;
}

export type ContentBlockId = number | null;

export type ContentBlockCreatedAt = Instant | null;

export type ContentBlockUpdatedAt = Instant | null;

export type ContentBlockUser = User | null;

export type ContentBlockContentBlockTypeId = number | null;

export type ContentBlockDisplayOrderNumber = number | null;

export type ContentBlockDisplayable = boolean | null;

export interface ContentBlock {
  id?: ContentBlockId;
  createdAt: ContentBlockCreatedAt;
  updatedAt: ContentBlockUpdatedAt;
  user: ContentBlockUser;
  contentBlockTypeId: ContentBlockContentBlockTypeId;
  displayOrderNumber: ContentBlockDisplayOrderNumber;
  displayable: ContentBlockDisplayable;
  contentBlockGroups: ContentBlockGroup[];
}

export type ContentBlockDetailId = number | null;

export type ContentBlockDetailCreatedAt = Instant | null;

export type ContentBlockDetailUpdatedAt = Instant | null;

/**
 * @maxLength 255
 */
export type ContentBlockDetailIcon = string | null;

/**
 * @maxLength 255
 */
export type ContentBlockDetailTitle = string | null;

export type ContentBlockDetailDescription = string | null;

export type ContentBlockDetailAppDescription = string | null;

export type ContentBlockDetailUrl = string | null;

export type ContentBlockDetailStyleAnyOf = { [key: string]: unknown };

export type ContentBlockDetailStyle = ContentBlockDetailStyleAnyOf | null;

export type ContentBlockDetailContentBlockGroup = ContentBlockGroup | null;

export type ContentBlockDetailIconUrl = string | null;

export interface ContentBlockDetail {
  id?: ContentBlockDetailId;
  createdAt: ContentBlockDetailCreatedAt;
  updatedAt: ContentBlockDetailUpdatedAt;
  /** @maxLength 255 */
  icon?: ContentBlockDetailIcon;
  /** @maxLength 255 */
  title: ContentBlockDetailTitle;
  description?: ContentBlockDetailDescription;
  appDescription?: ContentBlockDetailAppDescription;
  url?: ContentBlockDetailUrl;
  style?: ContentBlockDetailStyle;
  contentBlockGroup?: ContentBlockDetailContentBlockGroup;
  iconUrl?: ContentBlockDetailIconUrl;
}

export interface ContentBlockDetailResponse {
  contentBlockDetail: ContentBlockDetail;
}

export type ContentBlockGroupId = number | null;

export type ContentBlockGroupCreatedAt = Instant | null;

export type ContentBlockGroupUpdatedAt = Instant | null;

export type ContentBlockGroupContentBlock = ContentBlock | null;

export type ContentBlockGroupContentBlockDetail = ContentBlockDetail | null;

export type ContentBlockGroupContentGroupNumber = number | null;

export interface ContentBlockGroup {
  id?: ContentBlockGroupId;
  createdAt: ContentBlockGroupCreatedAt;
  updatedAt: ContentBlockGroupUpdatedAt;
  contentBlock: ContentBlockGroupContentBlock;
  contentBlockDetail: ContentBlockGroupContentBlockDetail;
  contentGroupNumber: ContentBlockGroupContentGroupNumber;
}

export interface ContentBlockResponse {
  contentBlock: ContentBlock;
}

export interface ContentBlocks {
  contentBlocks: GetContentBlockContentBlock[];
}

export interface ConvenienceParam {
  convenience: string;
  customerName: string;
  customerKana: string;
  telNo: string;
}

export type CreateCartItemRequestSingleFile = number | null;

export interface CreateCartItemRequest {
  itemId?: number;
  singleFile?: CreateCartItemRequestSingleFile;
  quantity?: number;
}

export interface CreateContentBlockData {
  contentBlock: ContentBlock;
}

export interface CreateContentBlockRequest {
  contentBlockType: string;
}

export type CreateContentBlockResponseMetadataAnyOf = { [key: string]: unknown };

export type CreateContentBlockResponseMetadata = CreateContentBlockResponseMetadataAnyOf | null;

export type CreateContentBlockResponseData = CreateContentBlockData | null;

export type CreateContentBlockResponseErrors = ErrorObject[] | null;

export interface CreateContentBlockResponse {
  metadata?: CreateContentBlockResponseMetadata;
  data: CreateContentBlockResponseData;
  errors: CreateContentBlockResponseErrors;
}

/**
 * @maxLength 50
 */
export type CreateContentWithDetailRequestTitle = string | null;

/**
 * @maxLength 500
 */
export type CreateContentWithDetailRequestDescription = string | null;

/**
 * @maxLength 500
 */
export type CreateContentWithDetailRequestAppDescription = string | null;

export type CreateContentWithDetailRequestUrl = string | null;

export type CreateContentWithDetailRequestIconUrl = string | null;

export interface CreateContentWithDetailRequest {
  contentBlockType: number;
  /** @maxLength 50 */
  title?: CreateContentWithDetailRequestTitle;
  /** @maxLength 500 */
  description?: CreateContentWithDetailRequestDescription;
  /** @maxLength 500 */
  appDescription?: CreateContentWithDetailRequestAppDescription;
  url?: CreateContentWithDetailRequestUrl;
  iconUrl?: CreateContentWithDetailRequestIconUrl;
}

export type CreateContentWithDetailRequest1Description = string | null;

export type CreateContentWithDetailRequest1AppDescription = string | null;

export type CreateContentWithDetailRequest1IconUrl = string | null;

export interface CreateContentWithDetailRequest1 {
  contentBlockType?: number;
  title: string;
  description?: CreateContentWithDetailRequest1Description;
  appDescription?: CreateContentWithDetailRequest1AppDescription;
  url: string;
  iconUrl?: CreateContentWithDetailRequest1IconUrl;
}

export type CreateGachaItemRequestDescription = string | null;

export type CreateGachaItemRequestSamples = GachaSampleFile[] | null;

export type CreateGachaItemRequestBenefits = GachaBenefit[] | null;

export type CreateGachaItemRequestTags = string[] | null;

export interface CreateGachaItemRequest {
  name: string;
  description?: CreateGachaItemRequestDescription;
  thumbnailUri: string;
  thumbnailFrom?: number;
  thumbnailBlurLevel?: number;
  thumbnailWatermarkLevel?: number;
  price?: number;
  available?: boolean;
  itemFiles: GachaFile[];
  samples?: CreateGachaItemRequestSamples;
  benefits?: CreateGachaItemRequestBenefits;
  tags?: CreateGachaItemRequestTags;
  itemOption: ItemOption;
  isDuplicated?: boolean;
  awardProbabilities: AwardProbability1[];
  itemType?: number;
}

export interface CreateItemPasswordUnlockCacheRequest {
  userInputPassword: string;
}

export type CreateOrUpdateItemRequestDescription = string | null;

export type CreateOrUpdateItemRequestSamples = DigitalBundleSampleFile[] | null;

export type CreateOrUpdateItemRequestBenefits = DigitalBundleBenefit[] | null;

export type CreateOrUpdateItemRequestTags = string[] | null;

export type CreateOrUpdateItemRequestItemType = number | null;

export interface CreateOrUpdateItemRequest {
  name: string;
  description?: CreateOrUpdateItemRequestDescription;
  thumbnailUri: string;
  thumbnailFrom?: number;
  thumbnailBlurLevel?: number;
  thumbnailWatermarkLevel?: number;
  price?: number;
  available?: boolean;
  itemFiles: DigitalBundleFile[];
  samples?: CreateOrUpdateItemRequestSamples;
  benefits?: CreateOrUpdateItemRequestBenefits;
  tags?: CreateOrUpdateItemRequestTags;
  itemOption: ItemOption;
  itemType?: CreateOrUpdateItemRequestItemType;
}

export type CreateOrderRequestCardParam = CardParam | null;

export type CreateOrderRequestConvenienceParam = ConvenienceParam | null;

export type CreateOrderRequestGooglePayParam = GooglePayParam | null;

export type CreateOrderRequestApplePayParam = ApplePayParam | null;

export interface CreateOrderRequest {
  cartId?: number;
  cartItemIds: number[];
  tip?: number;
  paymentMethod: string;
  cardParam?: CreateOrderRequestCardParam;
  convenienceParam?: CreateOrderRequestConvenienceParam;
  googlePayParam?: CreateOrderRequestGooglePayParam;
  applePayParam?: CreateOrderRequestApplePayParam;
}

export type CreateShopRequestDescription = string | null;

export type CreateShopRequestHeaderImageUri = string | null;

export type CreateShopRequestMessage = string | null;

export type CreateShopRequestCampaignIdentity = string | null;

export interface CreateShopRequest {
  name: string;
  description?: CreateShopRequestDescription;
  headerImageUri?: CreateShopRequestHeaderImageUri;
  message?: CreateShopRequestMessage;
  campaignIdentity?: CreateShopRequestCampaignIdentity;
}

export type CreateSingleOrderRequestCardParam = CardParam | null;

export type CreateSingleOrderRequestConvenienceParam = ConvenienceParam | null;

export type CreateSingleOrderRequestGooglePayParam = GooglePayParam | null;

export type CreateSingleOrderRequestApplePayParam = ApplePayParam | null;

export interface CreateSingleOrderRequest {
  itemId?: number;
  quantity?: number;
  tip?: number;
  paymentMethod: string;
  cardParam?: CreateSingleOrderRequestCardParam;
  convenienceParam?: CreateSingleOrderRequestConvenienceParam;
  googlePayParam?: CreateSingleOrderRequestGooglePayParam;
  applePayParam?: CreateSingleOrderRequestApplePayParam;
}

export interface CreateSnsLinksResponse {
  snsLinks: SnsLink[];
}

export interface CreateUserConsentRequest {
  consentName: string;
}

export type CreateUserConsentResponseUserConsent = UserConsent | null;

export interface CreateUserConsentResponse {
  userConsent?: CreateUserConsentResponseUserConsent;
}

export interface DeleteContentBlockData {
  success?: boolean;
}

export type DeleteContentBlockResponseMetadataAnyOf = { [key: string]: unknown };

export type DeleteContentBlockResponseMetadata = DeleteContentBlockResponseMetadataAnyOf | null;

export type DeleteContentBlockResponseData = DeleteContentBlockData | null;

export type DeleteContentBlockResponseErrors = ErrorObject[] | null;

export interface DeleteContentBlockResponse {
  metadata?: DeleteContentBlockResponseMetadata;
  data: DeleteContentBlockResponseData;
  errors: DeleteContentBlockResponseErrors;
}

export interface DeliveryFeeData {
  deliveryFee?: number;
}

export type DeliveryFeeResponseBodyMetadataAnyOf = { [key: string]: unknown };

export type DeliveryFeeResponseBodyMetadata = DeliveryFeeResponseBodyMetadataAnyOf | null;

export type DeliveryFeeResponseBodyData = DeliveryFeeData | null;

export type DeliveryFeeResponseBodyErrors = ErrorObject[] | null;

export interface DeliveryFeeResponseBody {
  metadata?: DeliveryFeeResponseBodyMetadata;
  data: DeliveryFeeResponseBodyData;
  errors?: DeliveryFeeResponseBodyErrors;
}

export type DigitalBundleBenefitDescription = string | null;

export interface DigitalBundleBenefit {
  id?: number;
  description?: DigitalBundleBenefitDescription;
  conditionType?: number;
  files: DigitalBundleBenefitFile[];
}

export type DigitalBundleBenefitFileId = number | null;

export type DigitalBundleBenefitFileThumbnailUri = string | null;

export type DigitalBundleBenefitFileItemThumbnailSelected = boolean | null;

export type DigitalBundleBenefitFileSortOrder = number | null;

export interface DigitalBundleBenefitFile {
  id?: DigitalBundleBenefitFileId;
  name: string;
  objectUri: string;
  thumbnailUri?: DigitalBundleBenefitFileThumbnailUri;
  fileType: string;
  size?: number;
  duration?: number;
  itemThumbnailSelected?: DigitalBundleBenefitFileItemThumbnailSelected;
  sortOrder?: DigitalBundleBenefitFileSortOrder;
  conditionType?: number;
}

export type DigitalBundleFileId = number | null;

export type DigitalBundleFilePrice = number | null;

export type DigitalBundleFileItemThumbnailSelected = boolean | null;

export interface DigitalBundleFile {
  id?: DigitalBundleFileId;
  name: string;
  objectUri: string;
  thumbnailUri: string;
  price?: DigitalBundleFilePrice;
  fileType: string;
  size?: number;
  duration?: number;
  itemThumbnailSelected?: DigitalBundleFileItemThumbnailSelected;
  sortOrder?: number;
}

export type DigitalBundleSampleFileThumbnailUri = string | null;

export interface DigitalBundleSampleFile {
  name: string;
  objectUri: string;
  thumbnailUri?: DigitalBundleSampleFileThumbnailUri;
  fileType: string;
  size?: number;
  duration?: number;
}

export type DisplayableCoverImageId = number | null;

export type DisplayableCoverImageCreatedAt = Instant | null;

export type DisplayableCoverImageUpdatedAt = Instant | null;

export type DisplayableCoverImageProfileCover = ProfileCover | null;

export type DisplayableCoverImageProfileCoverImage = ProfileCoverImage | null;

export interface DisplayableCoverImage {
  id?: DisplayableCoverImageId;
  createdAt: DisplayableCoverImageCreatedAt;
  updatedAt: DisplayableCoverImageUpdatedAt;
  profileCover: DisplayableCoverImageProfileCover;
  profileCoverImage: DisplayableCoverImageProfileCoverImage;
}

export interface DownloadUrl {
  key: string;
  url: string;
}

export interface DownloadUrlData {
  downloadUrls: DownloadUrl[];
}

export type DownloadUrlResponseBodyMetadataAnyOf = { [key: string]: unknown };

export type DownloadUrlResponseBodyMetadata = DownloadUrlResponseBodyMetadataAnyOf | null;

export type DownloadUrlResponseBodyData = DownloadUrlData | null;

export type DownloadUrlResponseBodyErrors = ErrorObject[] | null;

export interface DownloadUrlResponseBody {
  metadata?: DownloadUrlResponseBodyMetadata;
  data: DownloadUrlResponseBodyData;
  errors?: DownloadUrlResponseBodyErrors;
}

export type EntryType = (typeof EntryType)[keyof typeof EntryType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const EntryType = {
  NONE: 'NONE',
  LOGIN: 'LOGIN',
  SIGNUP: 'SIGNUP',
  SHOP_CREATION: 'SHOP_CREATION',
  SHOP_CREATION_ALL: 'SHOP_CREATION_ALL',
} as const;

export interface ErrorObject {
  code: number;
  message: string;
}

export interface EventData {
  event: RankingEventWithBoost;
}

export interface FanmeCustomerData {
  fanmeCustomer: FanmeCustomerEntity;
}

export type FanmeCustomerEntityCreatorUid = string | null;

export type FanmeCustomerEntityBuilding = string | null;

export interface FanmeCustomerEntity {
  creatorUid?: FanmeCustomerEntityCreatorUid;
  firstName: string;
  lastName: string;
  firstNameKana: string;
  lastNameKana: string;
  postalCode: string;
  prefecture: string;
  city: string;
  street: string;
  building?: FanmeCustomerEntityBuilding;
  phoneNumber: string;
}

export type FanmeCustomerResponseBodyMetadataAnyOf = { [key: string]: unknown };

export type FanmeCustomerResponseBodyMetadata = FanmeCustomerResponseBodyMetadataAnyOf | null;

export type FanmeCustomerResponseBodyData = FanmeCustomerData | null;

export type FanmeCustomerResponseBodyErrors = ErrorObject[] | null;

export interface FanmeCustomerResponseBody {
  metadata?: FanmeCustomerResponseBodyMetadata;
  data: FanmeCustomerResponseBodyData;
  errors?: FanmeCustomerResponseBodyErrors;
}

export type FileId = number | null;

export type FileObjectUri = string | null;

export type FileThumbnailUri = string | null;

export type FileMaskedThumbnailUri = string | null;

export type FileWatermarkThumbnailUri = string | null;

export type FilePrice = number | null;

export type FileCurrentPrice = number | null;

export type FileDuration = number | null;

export type FileAwardType = number | null;

export type FileReceivedFileCount = number | null;

export interface File {
  id?: FileId;
  name: string;
  objectUri?: FileObjectUri;
  thumbnailUri?: FileThumbnailUri;
  maskedThumbnailUri?: FileMaskedThumbnailUri;
  watermarkThumbnailUri?: FileWatermarkThumbnailUri;
  price?: FilePrice;
  currentPrice?: FileCurrentPrice;
  fileType: string;
  size?: number;
  duration?: FileDuration;
  isPurchased?: boolean;
  isCheckout?: boolean;
  itemThumbnailSelected?: boolean;
  awardType?: FileAwardType;
  isSecret?: boolean;
  conditionType?: number;
  receivedFileCount?: FileReceivedFileCount;
}

export type FileForPullGachaItemsObjectUri = string | null;

export type FileForPullGachaItemsThumbnailUri = string | null;

export type FileForPullGachaItemsDuration = number | null;

export type FileForPullGachaItemsIsSecret = boolean | null;

export type FileForPullGachaItemsWatermarkThumbnailUri = string | null;

export interface FileForPullGachaItems {
  id?: number;
  name: string;
  objectUri?: FileForPullGachaItemsObjectUri;
  thumbnailUri?: FileForPullGachaItemsThumbnailUri;
  fileType: string;
  size?: number;
  duration?: FileForPullGachaItemsDuration;
  awardType?: number;
  isSecret?: FileForPullGachaItemsIsSecret;
  watermarkThumbnailUri?: FileForPullGachaItemsWatermarkThumbnailUri;
}

export interface FileQuantity {
  fileType: string;
  quantity?: number;
}

export interface FileUpload {
  [key: string]: unknown;
}

export interface FinalizeCreditCard3DSecureRequest {
  transactionId?: number;
  checkoutId?: number;
}

export type ForSaleStartAt = string | null;

export type ForSaleEndAt = string | null;

export interface ForSale {
  startAt?: ForSaleStartAt;
  endAt?: ForSaleEndAt;
}

export type ForSaleDataStartAt = string | null;

export type ForSaleDataEndAt = string | null;

export interface ForSaleData {
  startAt?: ForSaleDataStartAt;
  endAt?: ForSaleDataEndAt;
}

export type GachaBenefitDescription = string | null;

export interface GachaBenefit {
  id?: number;
  description?: GachaBenefitDescription;
  conditionType?: number;
  files: GachaBenefitFile[];
}

export type GachaBenefitFileId = number | null;

export type GachaBenefitFileThumbnailUri = string | null;

export type GachaBenefitFileItemThumbnailSelected = boolean | null;

export type GachaBenefitFileSortOrder = number | null;

export interface GachaBenefitFile {
  id?: GachaBenefitFileId;
  name: string;
  objectUri: string;
  thumbnailUri?: GachaBenefitFileThumbnailUri;
  fileType: string;
  size?: number;
  duration?: number;
  itemThumbnailSelected?: GachaBenefitFileItemThumbnailSelected;
  sortOrder?: GachaBenefitFileSortOrder;
  conditionType?: number;
}

export type GachaBenefitFileForUpdateThumbnailUri = string | null;

export type GachaBenefitFileForUpdateItemThumbnailSelected = boolean | null;

export type GachaBenefitFileForUpdateSortOrder = number | null;

export interface GachaBenefitFileForUpdate {
  id?: number;
  name: string;
  objectUri: string;
  thumbnailUri?: GachaBenefitFileForUpdateThumbnailUri;
  fileType: string;
  size?: number;
  duration?: number;
  itemThumbnailSelected?: GachaBenefitFileForUpdateItemThumbnailSelected;
  sortOrder?: GachaBenefitFileForUpdateSortOrder;
}

export type GachaBenefitForUpdateId = number | null;

export type GachaBenefitForUpdateDescription = string | null;

export interface GachaBenefitForUpdate {
  id?: GachaBenefitForUpdateId;
  description?: GachaBenefitForUpdateDescription;
  files: GachaBenefitFileForUpdate[];
}

export type GachaFileId = number | null;

export type GachaFileThumbnailUri = string | null;

export type GachaFileMaskedThumbnailUri = string | null;

export type GachaFileWatermarkThumbnailUri = string | null;

export type GachaFilePrice = number | null;

export type GachaFileItemThumbnailSelected = boolean | null;

export type GachaFileSortOrder = number | null;

export type GachaFileIsSecret = boolean | null;

export interface GachaFile {
  id?: GachaFileId;
  name: string;
  objectUri: string;
  thumbnailUri?: GachaFileThumbnailUri;
  maskedThumbnailUri?: GachaFileMaskedThumbnailUri;
  watermarkThumbnailUri?: GachaFileWatermarkThumbnailUri;
  price?: GachaFilePrice;
  fileType: string;
  size?: number;
  duration?: number;
  itemThumbnailSelected?: GachaFileItemThumbnailSelected;
  sortOrder?: GachaFileSortOrder;
  awardType?: number;
  isSecret?: GachaFileIsSecret;
}

export interface GachaFileForUpdate {
  id?: number;
  name: string;
  isSecret?: boolean;
}

export interface GachaPullData {
  files: FileForPullGachaItems[];
}

export type GachaPullResponseBodyMetadataAnyOf = { [key: string]: unknown };

export type GachaPullResponseBodyMetadata = GachaPullResponseBodyMetadataAnyOf | null;

export type GachaPullResponseBodyData = GachaPullData | null;

export type GachaPullResponseBodyErrors = ErrorObject[] | null;

export interface GachaPullResponseBody {
  metadata?: GachaPullResponseBodyMetadata;
  data: GachaPullResponseBodyData;
  errors?: GachaPullResponseBodyErrors;
}

export interface GachaPullableCountData {
  item: Output;
}

export type GachaPullableCountResponseBodyMetadataAnyOf = { [key: string]: unknown };

export type GachaPullableCountResponseBodyMetadata = GachaPullableCountResponseBodyMetadataAnyOf | null;

export type GachaPullableCountResponseBodyData = GachaPullableCountData | null;

export type GachaPullableCountResponseBodyErrors = ErrorObject[] | null;

export interface GachaPullableCountResponseBody {
  metadata?: GachaPullableCountResponseBodyMetadata;
  data: GachaPullableCountResponseBodyData;
  errors?: GachaPullableCountResponseBodyErrors;
}

export type GachaSampleFileThumbnailUri = string | null;

export interface GachaSampleFile {
  name: string;
  objectUri: string;
  thumbnailUri?: GachaSampleFileThumbnailUri;
  fileType: string;
  size?: number;
  duration?: number;
}

export type GachaSampleFileForUpdateId = number | null;

export type GachaSampleFileForUpdateThumbnailUri = string | null;

export interface GachaSampleFileForUpdate {
  id?: GachaSampleFileForUpdateId;
  name: string;
  objectUri: string;
  thumbnailUri?: GachaSampleFileForUpdateThumbnailUri;
  fileType: string;
  size?: number;
  duration?: number;
}

export interface GetAgenciesData {
  agencies: Agency[];
}

export type GetAgenciesResponseData = GetAgenciesData | null;

export type GetAgenciesResponseErrors = ErrorObject[] | null;

export type GetAgenciesResponseMetadataAnyOf = { [key: string]: unknown };

export type GetAgenciesResponseMetadata = GetAgenciesResponseMetadataAnyOf | null;

export interface GetAgenciesResponse {
  data?: GetAgenciesResponseData;
  errors?: GetAgenciesResponseErrors;
  metadata?: GetAgenciesResponseMetadata;
}

export interface GetAgencyMonthlySalesData {
  agencyMonthlySalesList: AgencyMonthlySales[];
}

export type GetAgencyMonthlySalesResponseData = GetAgencyMonthlySalesData | null;

export type GetAgencyMonthlySalesResponseErrors = ErrorObject[] | null;

export type GetAgencyMonthlySalesResponseMetadataAnyOf = { [key: string]: unknown };

export type GetAgencyMonthlySalesResponseMetadata = GetAgencyMonthlySalesResponseMetadataAnyOf | null;

export interface GetAgencyMonthlySalesResponse {
  data?: GetAgencyMonthlySalesResponseData;
  errors?: GetAgencyMonthlySalesResponseErrors;
  metadata?: GetAgencyMonthlySalesResponseMetadata;
}

export interface GetAgencySalesData {
  agencySales: AgencySales;
}

export type GetAgencySalesResponseData = GetAgencySalesData | null;

export type GetAgencySalesResponseErrors = ErrorObject[] | null;

export type GetAgencySalesResponseMetadataAnyOf = { [key: string]: unknown };

export type GetAgencySalesResponseMetadata = GetAgencySalesResponseMetadataAnyOf | null;

export interface GetAgencySalesResponse {
  data?: GetAgencySalesResponseData;
  errors?: GetAgencySalesResponseErrors;
  metadata?: GetAgencySalesResponseMetadata;
}

export interface GetAgencyUsersData {
  users: User[];
}

export type GetAgencyUsersResponseData = GetAgencyUsersData | null;

export type GetAgencyUsersResponseErrors = ErrorObject[] | null;

export type GetAgencyUsersResponseMetadataAnyOf = { [key: string]: unknown };

export type GetAgencyUsersResponseMetadata = GetAgencyUsersResponseMetadataAnyOf | null;

export interface GetAgencyUsersResponse {
  data?: GetAgencyUsersResponseData;
  errors?: GetAgencyUsersResponseErrors;
  metadata?: GetAgencyUsersResponseMetadata;
}

export interface GetAllShopItemsData {
  creatorShopItems: AllShopItemsResponse;
}

export type GetAllShopItemsResponseMetadataAnyOf = { [key: string]: unknown };

export type GetAllShopItemsResponseMetadata = GetAllShopItemsResponseMetadataAnyOf | null;

export type GetAllShopItemsResponseData = GetAllShopItemsData | null;

export type GetAllShopItemsResponseErrors = ErrorObject[] | null;

export interface GetAllShopItemsResponse {
  metadata?: GetAllShopItemsResponseMetadata;
  data: GetAllShopItemsResponseData;
  errors: GetAllShopItemsResponseErrors;
}

export interface GetConsentsResponse {
  consents: Consent[];
}

export interface GetContentBlockData {
  contentBlocks: GetContentBlockContentBlock[];
}

export type GetContentBlockResponseMetadataAnyOf = { [key: string]: unknown };

export type GetContentBlockResponseMetadata = GetContentBlockResponseMetadataAnyOf | null;

export type GetContentBlockResponseData = GetContentBlockData | null;

export type GetContentBlockResponseErrors = ErrorObject[] | null;

export interface GetContentBlockResponse {
  metadata?: GetContentBlockResponseMetadata;
  data: GetContentBlockResponseData;
  errors: GetContentBlockResponseErrors;
}

export interface GetContentBlockContentBlock {
  id?: number;
  contentBlockDetails: GetContentBlockContentBlockDetail[];
  contentBlockType?: number;
  displayOrderNumber?: number;
  displayable?: boolean;
}

export type GetContentBlockContentBlockDetailUrl = string | null;

export type GetContentBlockContentBlockDetailDescription = string | null;

export type GetContentBlockContentBlockDetailAppDescription = string | null;

export type GetContentBlockContentBlockDetailIcon = string | null;

export type GetContentBlockContentBlockDetailStyleAnyOf = { [key: string]: unknown };

export type GetContentBlockContentBlockDetailStyle = GetContentBlockContentBlockDetailStyleAnyOf | null;

export interface GetContentBlockContentBlockDetail {
  id?: number;
  contentGroupNumber?: number;
  isSetIcon?: boolean;
  title: string;
  url?: GetContentBlockContentBlockDetailUrl;
  description?: GetContentBlockContentBlockDetailDescription;
  appDescription?: GetContentBlockContentBlockDetailAppDescription;
  icon?: GetContentBlockContentBlockDetailIcon;
  style?: GetContentBlockContentBlockDetailStyle;
}

export interface GetDownloadUrlRequest {
  metadataList: MetadataForGetDownloadUrlRequest[];
  itemId?: number;
}

export interface GetGachaCompleteBadgeRankingResponse {
  userUid: string;
  userAccountIdentity: string;
  userName: string;
  userIcon: string;
  getBadgeAt: string;
}

export interface GetPreSignedUrlRequest {
  metadataList: MetadataForGetPreSignedUrlRequest[];
  creatorAccountIdentity: string;
}

export interface GetProfileData {
  profile: GetProfileProfile;
}

export type GetProfileResponseMetadataAnyOf = { [key: string]: unknown };

export type GetProfileResponseMetadata = GetProfileResponseMetadataAnyOf | null;

export type GetProfileResponseData = GetProfileData | null;

export type GetProfileResponseErrors = ErrorObject[] | null;

export interface GetProfileResponse {
  metadata?: GetProfileResponseMetadata;
  data: GetProfileResponseData;
  errors: GetProfileResponseErrors;
}

export interface GetProfileCoverImage {
  resourceType: string;
  resource: string;
  coverVisibility?: boolean;
  brightness: string;
}

export type GetProfileProfileBio = string | null;

export type GetProfileProfileCoverImage = GetProfileCoverImage | null;

export type GetProfileProfileHeaderImage = string | null;

export type GetProfileProfileSnsLinkColor = string | null;

export type GetProfileProfileThemeColor = string | null;

export interface GetProfileProfile {
  id?: number;
  userId?: number;
  bio?: GetProfileProfileBio;
  coverImage?: GetProfileProfileCoverImage;
  headerImage?: GetProfileProfileHeaderImage;
  snsLinkColor?: GetProfileProfileSnsLinkColor;
  snsLinks: GetProfileSnsLink[];
  themeColor?: GetProfileProfileThemeColor;
  officialFlg?: boolean;
}

export interface GetProfileSnsLink {
  id?: number;
  type: string;
  snsAccountId: string;
  displayOrderNumber?: number;
  displayable?: boolean;
}

export interface GetUploadUrlRequest {
  metadataList: MetadataForGetUploadUrlRequest[];
}

export interface GetUserMonthlySalesData {
  userMonthlySalesList: UserMonthlySales[];
}

export type GetUserMonthlySalesResponseData = GetUserMonthlySalesData | null;

export type GetUserMonthlySalesResponseErrors = ErrorObject[] | null;

export type GetUserMonthlySalesResponseMetadataAnyOf = { [key: string]: unknown };

export type GetUserMonthlySalesResponseMetadata = GetUserMonthlySalesResponseMetadataAnyOf | null;

export interface GetUserMonthlySalesResponse {
  data?: GetUserMonthlySalesResponseData;
  errors?: GetUserMonthlySalesResponseErrors;
  metadata?: GetUserMonthlySalesResponseMetadata;
}

export interface GetUserTransactionsData {
  purchaseHistories: PurchaseHistory[];
}

export type GetUserTransactionsResponseData = GetUserTransactionsData | null;

export type GetUserTransactionsResponseErrors = ErrorObject[] | null;

export type GetUserTransactionsResponseMetadataAnyOf = { [key: string]: unknown };

export type GetUserTransactionsResponseMetadata = GetUserTransactionsResponseMetadataAnyOf | null;

export interface GetUserTransactionsResponse {
  data?: GetUserTransactionsResponseData;
  errors?: GetUserTransactionsResponseErrors;
  metadata?: GetUserTransactionsResponseMetadata;
}

export interface GetUserTutorialUserTutorial {
  userUuid: string;
  name: string;
  displayFlg?: boolean;
}

export interface GooglePayParam {
  token: string;
}

export type Instant = string;

export type ItemId = number | null;

export type ItemCreatedAt = Instant | null;

export type ItemUpdatedAt = Instant | null;

/**
 * @maxLength 800
 */
export type ItemDescription = string | null;

export interface Item {
  id?: ItemId;
  createdAt: ItemCreatedAt;
  updatedAt: ItemUpdatedAt;
  shop: Shop;
  /**
   * @minLength 1
   * @maxLength 100
   * @pattern \S
   */
  name: string;
  /** @maxLength 800 */
  description?: ItemDescription;
  /** @pattern \S */
  thumbnailUri: string;
  /**
   * @minimum 0
   * @maximum 1
   */
  thumbnailFrom: number;
  /**
   * @minimum 0
   * @maximum 2
   */
  thumbnailBlurLevel: number;
  /**
   * @minimum 0
   * @maximum 2
   */
  thumbnailWatermarkLevel: number;
  /**
   * @minimum 100
   * @maximum 1000000
   */
  price: number;
  fileType: number;
  available: boolean;
  marginRate: number;
  sortOrder: number;
  itemType: ItemType;
  digital?: boolean;
}

export interface ItemCostData {
  cost?: number;
}

export type ItemCostResponseBodyMetadataAnyOf = { [key: string]: unknown };

export type ItemCostResponseBodyMetadata = ItemCostResponseBodyMetadataAnyOf | null;

export type ItemCostResponseBodyData = ItemCostData | null;

export type ItemCostResponseBodyErrors = ErrorObject[] | null;

export interface ItemCostResponseBody {
  metadata?: ItemCostResponseBodyMetadata;
  data: ItemCostResponseBodyData;
  errors?: ItemCostResponseBodyErrors;
}

export interface ItemData {
  item: ItemForGetItem;
}

export type ItemForGetItemDescription = string | null;

export type ItemForGetItemAwardProbabilities = AwardProbability[] | null;

export type ItemForGetItemIsDuplicatedDigitalGachaItems = boolean | null;

export type ItemForGetItemFiles = File[] | null;

export type ItemForGetItemSamples = File[] | null;

export type ItemForGetItemBenefits = BenefitParam[] | null;

export type ItemForGetItemTags = string[] | null;

export type ItemForGetItemRemainingUniquePullCount = number | null;

export interface ItemForGetItem {
  id?: number;
  creatorAccountIdentity: string;
  name: string;
  description?: ItemForGetItemDescription;
  thumbnailUri: string;
  thumbnailFrom?: number;
  thumbnailBlurLevel?: number;
  thumbnailWatermarkLevel?: number;
  price?: number;
  currentPrice?: number;
  fileType: string;
  available?: boolean;
  awardProbabilities?: ItemForGetItemAwardProbabilities;
  isDuplicatedDigitalGachaItems?: ItemForGetItemIsDuplicatedDigitalGachaItems;
  itemType?: number;
  files?: ItemForGetItemFiles;
  samples?: ItemForGetItemSamples;
  benefits?: ItemForGetItemBenefits;
  tags?: ItemForGetItemTags;
  itemOption: OptionData;
  isPurchased?: boolean;
  isCheckout?: boolean;
  purchasedCount?: number;
  collectedUniqueItemsCount?: number;
  isCompleted?: boolean;
  remainingUniquePullCount?: ItemForGetItemRemainingUniquePullCount;
  isPublishLocked?: boolean;
}

export interface ItemMarginRateItem {
  itemId?: number;
  marginRate?: number;
}

export interface ItemMarginRateResult {
  shopId?: number;
  itemId?: number;
  marginRate?: number;
}

export type ItemOptionQtyTotal = number | null;

export type ItemOptionQtyPerUser = number | null;

export type ItemOptionForSale = ForSale | null;

export type ItemOptionPassword = string | null;

export type ItemOptionOnSale = OnSale | null;

export interface ItemOption {
  isSingleSales?: boolean;
  qtyTotal?: ItemOptionQtyTotal;
  qtyPerUser?: ItemOptionQtyPerUser;
  forSale?: ItemOptionForSale;
  password?: ItemOptionPassword;
  onSale?: ItemOptionOnSale;
}

export type ItemOption1Password = string | null;

export type ItemOption1OnSale = OnSale | null;

export type ItemOption1ForSale = ForSale | null;

export interface ItemOption1 {
  password?: ItemOption1Password;
  onSale?: ItemOption1OnSale;
  forSale?: ItemOption1ForSale;
}

export interface ItemPriceSet {
  cartItemId?: number;
  displayedPrice?: number;
}

export type ItemResponseBodyMetadataAnyOf = { [key: string]: unknown };

export type ItemResponseBodyMetadata = ItemResponseBodyMetadataAnyOf | null;

export type ItemResponseBodyData = ItemData | null;

export type ItemResponseBodyErrors = ErrorObject[] | null;

export interface ItemResponseBody {
  metadata?: ItemResponseBodyMetadata;
  data: ItemResponseBodyData;
  errors?: ItemResponseBodyErrors;
}

export type ItemType = (typeof ItemType)[keyof typeof ItemType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const ItemType = {
  DIGITAL_BUNDLE: 'DIGITAL_BUNDLE',
  DIGITAL_GACHA: 'DIGITAL_GACHA',
  CHEKI: 'CHEKI',
  REAL_PHOTO: 'REAL_PHOTO',
  PRINT_GACHA: 'PRINT_GACHA',
} as const;

export type LocalDateTime = string;

export type MetadataForGetDownloadUrlRequestName = string | null;

export interface MetadataForGetDownloadUrlRequest {
  key: string;
  name?: MetadataForGetDownloadUrlRequestName;
}

export interface MetadataForGetPreSignedUrlRequest {
  id: string;
  key: string;
}

export type MetadataForGetUploadUrlRequestName = string | null;

export interface MetadataForGetUploadUrlRequest {
  id: string;
  name?: MetadataForGetUploadUrlRequestName;
}

export interface MonthlySales {
  yearMonth: string;
  sellerSalesAmount?: number;
  purchaserCount?: number;
  purchaseCount?: number;
}

export interface MoveContentBlockData {
  contentBlocks: MoveContentBlockContentBlocks;
}

export interface MoveContentBlockDetailData {
  contentBlock: MoveContentBlockDetailContentBlocks;
}

export type MoveContentBlockDetailRequestFromContentBlockDetailId = number | null;

export type MoveContentBlockDetailRequestToContentBlockDetailId = number | null;

export type MoveContentBlockDetailRequestContentBlockDetailIds = number[] | null;

export interface MoveContentBlockDetailRequest {
  fromContentBlockDetailId?: MoveContentBlockDetailRequestFromContentBlockDetailId;
  toContentBlockDetailId?: MoveContentBlockDetailRequestToContentBlockDetailId;
  contentBlockDetailIds?: MoveContentBlockDetailRequestContentBlockDetailIds;
}

export type MoveContentBlockDetailResponseMetadataAnyOf = { [key: string]: unknown };

export type MoveContentBlockDetailResponseMetadata = MoveContentBlockDetailResponseMetadataAnyOf | null;

export type MoveContentBlockDetailResponseData = MoveContentBlockDetailData | null;

export type MoveContentBlockDetailResponseErrors = ErrorObject[] | null;

export interface MoveContentBlockDetailResponse {
  metadata?: MoveContentBlockDetailResponseMetadata;
  data: MoveContentBlockDetailResponseData;
  errors: MoveContentBlockDetailResponseErrors;
}

export interface MoveContentBlockDetailContentBlock {
  id?: number;
  contentBlockDetails: MoveContentBlockDetailContentBlockDetail[];
}

export interface MoveContentBlockDetailContentBlockDetail {
  id?: number;
  contentGroupNumber?: number;
}

export interface MoveContentBlockDetailContentBlocks {
  contentBlock: MoveContentBlockDetailContentBlock;
}

export type MoveContentBlockRequestDisplayOrderNum = number | null;

export type MoveContentBlockRequestUpDown = string | null;

export type MoveContentBlockRequestContentBlockIds = number[] | null;

export interface MoveContentBlockRequest {
  displayOrderNum?: MoveContentBlockRequestDisplayOrderNum;
  upDown?: MoveContentBlockRequestUpDown;
  contentBlockIds?: MoveContentBlockRequestContentBlockIds;
}

export type MoveContentBlockResponseMetadataAnyOf = { [key: string]: unknown };

export type MoveContentBlockResponseMetadata = MoveContentBlockResponseMetadataAnyOf | null;

export type MoveContentBlockResponseData = MoveContentBlockData | null;

export type MoveContentBlockResponseErrors = ErrorObject[] | null;

export interface MoveContentBlockResponse {
  metadata?: MoveContentBlockResponseMetadata;
  data: MoveContentBlockResponseData;
  errors: MoveContentBlockResponseErrors;
}

export interface MoveContentBlockContentBlock {
  id?: number;
  displayOrderNumber?: number;
}

export interface MoveContentBlockContentBlocks {
  contentBlocks: MoveContentBlockContentBlock[];
}

export interface MoveSnsLinksResponse {
  success?: boolean;
}

export type OnSaleStartAt = string | null;

export type OnSaleEndAt = string | null;

export interface OnSale {
  discountRate?: number;
  startAt?: OnSaleStartAt;
  endAt?: OnSaleEndAt;
}

export type OnSaleDataStartAt = string | null;

export type OnSaleDataEndAt = string | null;

export interface OnSaleData {
  discountRate?: number;
  startAt?: OnSaleDataStartAt;
  endAt?: OnSaleDataEndAt;
}

export type OperationType = (typeof OperationType)[keyof typeof OperationType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const OperationType = {
  INSERT: 'INSERT',
  UPDATE: 'UPDATE',
} as const;

export type OptionDataQtyTotal = number | null;

export type OptionDataQtyPerUser = number | null;

export type OptionDataRemainingAmount = number | null;

export type OptionDataForSale = ForSaleData | null;

export type OptionDataPassword = string | null;

export type OptionDataOnSale = OnSaleData | null;

export interface OptionData {
  isSingleSales?: boolean;
  qtyTotal?: OptionDataQtyTotal;
  qtyPerUser?: OptionDataQtyPerUser;
  remainingAmount?: OptionDataRemainingAmount;
  forSale?: OptionDataForSale;
  password?: OptionDataPassword;
  onSale?: OptionDataOnSale;
}

export type OrderId = number | null;

export type OrderCreatedAt = Instant | null;

export type OrderUpdatedAt = Instant | null;

export type OrderTransactionId = number | null;

export type OrderCheckoutId = number | null;

export interface Order {
  id?: OrderId;
  createdAt: OrderCreatedAt;
  updatedAt: OrderUpdatedAt;
  /** @pattern \S */
  purchaserUid: string;
  shop: Shop;
  transactionId?: OrderTransactionId;
  checkoutId?: OrderCheckoutId;
}

export type OrderResultPurchasedItems = PurchasedItem[] | null;

export type OrderResultConvenienceCheckout = Output2 | null;

export type OrderResultRedirectUrl = string | null;

export interface OrderResult {
  order: Order;
  purchasedItems?: OrderResultPurchasedItems;
  convenienceCheckout?: OrderResultConvenienceCheckout;
  redirectUrl?: OrderResultRedirectUrl;
}

export interface OrderedItem {
  id?: number;
  name: string;
  itemType?: number;
  price?: number;
  marginRate?: number;
  quantity?: number;
}

export interface Output {
  itemId?: number;
  remainingPullCount?: number;
}

export type Output1Rank = number | null;

export interface Output1 {
  itemId?: number;
  isAcquired?: boolean;
  rank?: Output1Rank;
}

export type Output2ReceiptUrl = string | null;

export interface Output2 {
  checkoutId?: number;
  convenience: string;
  confNo: string;
  receiptNo: string;
  paymentTerm: string;
  receiptUrl?: Output2ReceiptUrl;
  status: CheckoutStatus;
}

export type ProfileId = number | null;

export type ProfileCreatedAt = Instant | null;

export type ProfileUpdatedAt = Instant | null;

export type ProfileUser = User | null;

export type ProfileBio = string | null;

/**
 * @maxLength 255
 */
export type ProfileHeaderImage = string | null;

/**
 * @maxLength 255
 */
export type ProfileSnsLinkColor = string | null;

export type ProfileOfficialFlg = boolean | null;

export type ProfileCoverProperty = ProfileCover | null;

export type ProfileThemeColorProperty = ProfileThemeColor | null;

export interface Profile {
  id?: ProfileId;
  createdAt: ProfileCreatedAt;
  updatedAt: ProfileUpdatedAt;
  user?: ProfileUser;
  bio: ProfileBio;
  /** @maxLength 255 */
  headerImage?: ProfileHeaderImage;
  /** @maxLength 255 */
  snsLinkColor: ProfileSnsLinkColor;
  officialFlg: ProfileOfficialFlg;
  cover?: ProfileCoverProperty;
  themeColor?: ProfileThemeColorProperty;
  snsLinks: SnsLink[];
  snsLinkDisplays: SnsLinkDisplay[];
}

export type ProfileCoverId = number | null;

export type ProfileCoverCreatedAt = Instant | null;

export type ProfileCoverUpdatedAt = Instant | null;

export type ProfileCoverProfile = Profile | null;

/**
 * @maxLength 255
 */
export type ProfileCoverBrightness = string | null;

export type ProfileCoverCoverVisibility = boolean | null;

export type ProfileCoverDisplayableCoverImage = DisplayableCoverImage | null;

export interface ProfileCover {
  id?: ProfileCoverId;
  createdAt: ProfileCoverCreatedAt;
  updatedAt: ProfileCoverUpdatedAt;
  profile?: ProfileCoverProfile;
  /** @maxLength 255 */
  brightness: ProfileCoverBrightness;
  coverVisibility: ProfileCoverCoverVisibility;
  coverImage: ProfileCoverImage[];
  displayableCoverImage?: ProfileCoverDisplayableCoverImage;
}

export type ProfileCoverImageId = number | null;

export type ProfileCoverImageCreatedAt = Instant | null;

export type ProfileCoverImageUpdatedAt = Instant | null;

export type ProfileCoverImageProfileCover = ProfileCover | null;

/**
 * @maxLength 255
 */
export type ProfileCoverImageResource = string | null;

/**
 * @maxLength 255
 */
export type ProfileCoverImageResourceType = string | null;

export type ProfileCoverImageDisplayableCoverImage = DisplayableCoverImage | null;

export interface ProfileCoverImage {
  id?: ProfileCoverImageId;
  createdAt: ProfileCoverImageCreatedAt;
  updatedAt: ProfileCoverImageUpdatedAt;
  profileCover: ProfileCoverImageProfileCover;
  /** @maxLength 255 */
  resource: ProfileCoverImageResource;
  /** @maxLength 255 */
  resourceType: ProfileCoverImageResourceType;
  displayableCoverImage?: ProfileCoverImageDisplayableCoverImage;
}

export type ProfileThemeColorId = number | null;

export type ProfileThemeColorCreatedAt = Instant | null;

export type ProfileThemeColorUpdatedAt = Instant | null;

export type ProfileThemeColorProfile = Profile | null;

/**
 * @maxLength 255
 */
export type ProfileThemeColorCustomColor = string | null;

export interface ProfileThemeColor {
  id?: ProfileThemeColorId;
  createdAt: ProfileThemeColorCreatedAt;
  updatedAt: ProfileThemeColorUpdatedAt;
  profile?: ProfileThemeColorProfile;
  themeColorId: number;
  /** @maxLength 255 */
  customColor?: ProfileThemeColorCustomColor;
}

export interface ProxyAccessTokenData {
  proxyAccessToken: string;
}

export type ProxyAccessTokenResponseData = ProxyAccessTokenData | null;

export type ProxyAccessTokenResponseErrors = ErrorObject[] | null;

export type ProxyAccessTokenResponseMetadataAnyOf = { [key: string]: unknown };

export type ProxyAccessTokenResponseMetadata = ProxyAccessTokenResponseMetadataAnyOf | null;

export interface ProxyAccessTokenResponse {
  data?: ProxyAccessTokenResponseData;
  errors?: ProxyAccessTokenResponseErrors;
  metadata?: ProxyAccessTokenResponseMetadata;
}

export interface PullGachaRequest {
  itemId?: number;
}

export type PurchaseHistoryPaymentType = string | null;

export interface PurchaseHistory {
  purchaseDate: string;
  purchaserName: string;
  itemNames: string[];
  sellerSalesAmount?: number;
  quantity?: number;
  paymentType?: PurchaseHistoryPaymentType;
}

export interface PurchaseItemData {
  purchasedItem: PurchasedItemDetail;
}

export type PurchaseItemResponseBodyMetadataAnyOf = { [key: string]: unknown };

export type PurchaseItemResponseBodyMetadata = PurchaseItemResponseBodyMetadataAnyOf | null;

export type PurchaseItemResponseBodyData = PurchaseItemData | null;

export type PurchaseItemResponseBodyErrors = ErrorObject[] | null;

export interface PurchaseItemResponseBody {
  metadata?: PurchaseItemResponseBodyMetadata;
  data: PurchaseItemResponseBodyData;
  errors?: PurchaseItemResponseBodyErrors;
}

export type PurchasedItemId = number | null;

export type PurchasedItemCreatedAt = Instant | null;

export type PurchasedItemUpdatedAt = Instant | null;

export type PurchasedItemItemId = number | null;

export type PurchasedItemItemCreatedAt = Instant | null;

export type PurchasedItemItemUpdatedAt = Instant | null;

/**
 * @maxLength 800
 */
export type PurchasedItemItemDescription = string | null;

export type PurchasedItemItem = {
  id?: PurchasedItemItemId;
  createdAt: PurchasedItemItemCreatedAt;
  updatedAt: PurchasedItemItemUpdatedAt;
  /**
   * @minLength 1
   * @maxLength 100
   * @pattern \S
   */
  name: string;
  /** @maxLength 800 */
  description?: PurchasedItemItemDescription;
  /** @pattern \S */
  thumbnailUri: string;
  /**
   * @minimum 0
   * @maximum 1
   */
  thumbnailFrom: number;
  /**
   * @minimum 0
   * @maximum 2
   */
  thumbnailBlurLevel: number;
  /**
   * @minimum 0
   * @maximum 2
   */
  thumbnailWatermarkLevel: number;
  /**
   * @minimum 100
   * @maximum 1000000
   */
  price: number;
  fileType: number;
  available: boolean;
  marginRate: number;
  sortOrder: number;
  itemType: ItemType;
  digital?: boolean;
};

export type PurchasedItemItemFileAnyOfId = number | null;

export type PurchasedItemItemFileAnyOfCreatedAt = Instant | null;

export type PurchasedItemItemFileAnyOfUpdatedAt = Instant | null;

/**
 * @pattern \S
 */
export type PurchasedItemItemFileAnyOfObjectUri = string | null;

export type PurchasedItemItemFileAnyOfThumbnailUri = string | null;

export type PurchasedItemItemFileAnyOfMaskedThumbnailUri = string | null;

export type PurchasedItemItemFileAnyOfWatermarkThumbnailUri = string | null;

/**
 * @minimum 0
 * @maximum 1000000
 */
export type PurchasedItemItemFileAnyOfPrice = number | null;

export type PurchasedItemItemFileAnyOf = {
  id?: PurchasedItemItemFileAnyOfId;
  createdAt: PurchasedItemItemFileAnyOfCreatedAt;
  updatedAt: PurchasedItemItemFileAnyOfUpdatedAt;
  /**
   * @maxLength 30
   * @pattern \S
   */
  name: string;
  /** @pattern \S */
  objectUri: PurchasedItemItemFileAnyOfObjectUri;
  thumbnailUri?: PurchasedItemItemFileAnyOfThumbnailUri;
  maskedThumbnailUri?: PurchasedItemItemFileAnyOfMaskedThumbnailUri;
  watermarkThumbnailUri?: PurchasedItemItemFileAnyOfWatermarkThumbnailUri;
  /**
   * @minimum 0
   * @maximum 1000000
   */
  price?: PurchasedItemItemFileAnyOfPrice;
  /** @pattern \S */
  fileType: string;
  size?: number;
  /** @minimum 0 */
  duration?: number;
  itemThumbnailSelected: boolean;
  sortOrder: number;
};

export type PurchasedItemItemFile = PurchasedItemItemFileAnyOf | null;

export type PurchasedItemPurchaserComment = string | null;

export type PurchasedItemPurchasedAt = Instant | null;

export interface PurchasedItem {
  id?: PurchasedItemId;
  createdAt: PurchasedItemCreatedAt;
  updatedAt: PurchasedItemUpdatedAt;
  order: Order;
  /** @pattern \S */
  purchaserUid: string;
  item: PurchasedItemItem;
  itemFile?: PurchasedItemItemFile;
  /**
   * @minimum 100
   * @maximum 1000000
   */
  price?: number;
  /** @minimum 1 */
  quantity?: number;
  purchaserComment?: PurchasedItemPurchaserComment;
  /** @pattern \S */
  status: string;
  purchasedAt?: PurchasedItemPurchasedAt;
}

export type PurchasedItemCheckoutPaymentType = string | null;

export type PurchasedItemCheckoutConvenience = string | null;

export type PurchasedItemCheckoutConfNo = string | null;

export type PurchasedItemCheckoutReceiptNo = string | null;

export type PurchasedItemCheckoutPaymentTerm = string | null;

export type PurchasedItemCheckoutReceiptUrl = string | null;

export type PurchasedItemCheckoutStatus = string | null;

export type PurchasedItemCheckoutTotal = number | null;

export type PurchasedItemCheckoutTipAmount = number | null;

export type PurchasedItemCheckoutFee = number | null;

export type PurchasedItemCheckoutDeliveryFee = number | null;

export interface PurchasedItemCheckout {
  paymentType?: PurchasedItemCheckoutPaymentType;
  convenience?: PurchasedItemCheckoutConvenience;
  confNo?: PurchasedItemCheckoutConfNo;
  receiptNo?: PurchasedItemCheckoutReceiptNo;
  paymentTerm?: PurchasedItemCheckoutPaymentTerm;
  receiptUrl?: PurchasedItemCheckoutReceiptUrl;
  status?: PurchasedItemCheckoutStatus;
  total?: PurchasedItemCheckoutTotal;
  tipAmount?: PurchasedItemCheckoutTipAmount;
  fee?: PurchasedItemCheckoutFee;
  deliveryFee?: PurchasedItemCheckoutDeliveryFee;
}

export type PurchasedItemDetailCheckout = PurchasedItemCheckout | null;

export type PurchasedItemDetailPurchaserComment = string | null;

export interface PurchasedItemDetail {
  id?: number;
  itemId?: number;
  purchasedAt: string;
  order: PurchasedItemOrder;
  checkout?: PurchasedItemDetailCheckout;
  purchaserComment?: PurchasedItemDetailPurchaserComment;
}

export interface PurchasedItemOrder {
  id?: number;
  orderNumber: string;
  items: OrderedItem[];
  orderedAt: string;
}

export type PurchasedItemReceivedFileThumbnail = string | null;

export interface PurchasedItemReceivedFile {
  id?: number;
  title: string;
  thumbnail?: PurchasedItemReceivedFileThumbnail;
  awardType?: number;
  count?: number;
}

export interface PurchasedItemReceivedFilesData {
  files: PurchasedItemReceivedFile[];
}

export type PurchasedItemReceivedFilesResponseBodyMetadataAnyOf = { [key: string]: unknown };

export type PurchasedItemReceivedFilesResponseBodyMetadata = PurchasedItemReceivedFilesResponseBodyMetadataAnyOf | null;

export type PurchasedItemReceivedFilesResponseBodyData = PurchasedItemReceivedFilesData | null;

export type PurchasedItemReceivedFilesResponseBodyErrors = ErrorObject[] | null;

export interface PurchasedItemReceivedFilesResponseBody {
  metadata?: PurchasedItemReceivedFilesResponseBodyMetadata;
  data: PurchasedItemReceivedFilesResponseBodyData;
  errors?: PurchasedItemReceivedFilesResponseBodyErrors;
}

export interface PurposeAndUserAttributesRequestBody {
  userAttributes: UserAttributes;
}

export type RankingEventId = number | null;

export type RankingEventCreatedAt = Instant | null;

export type RankingEventUpdatedAt = Instant | null;

/**
 * @maxLength 255
 */
export type RankingEventEventIdentity = string | null;

/**
 * @maxLength 255
 */
export type RankingEventName = string | null;

export type RankingEventDescription = string | null;

/**
 * @maxLength 255
 */
export type RankingEventImageUrl = string | null;

/**
 * @maxLength 255
 */
export type RankingEventBaseColor = string | null;

/**
 * @maxLength 255
 */
export type RankingEventAddInfos = string | null;

/**
 * @maxLength 255
 */
export type RankingEventJudgeX = string | null;

/**
 * @maxLength 255
 */
export type RankingEventJudgeInstagram = string | null;

/**
 * @maxLength 255
 */
export type RankingEventShareHashTags = string | null;

/**
 * @maxLength 255
 */
export type RankingEventResults = string | null;

export type RankingEventApplyStartAt = Instant | null;

export type RankingEventApplyEndAt = Instant | null;

export type RankingEventStartAt = Instant | null;

export type RankingEventEndAt = Instant | null;

export type RankingEventCalculatedAt = Instant | null;

export type RankingEventArchivedAt = Instant | null;

export interface RankingEvent {
  id?: RankingEventId;
  createdAt: RankingEventCreatedAt;
  updatedAt: RankingEventUpdatedAt;
  /** @maxLength 255 */
  eventIdentity: RankingEventEventIdentity;
  /** @maxLength 255 */
  name: RankingEventName;
  description: RankingEventDescription;
  /** @maxLength 255 */
  imageUrl: RankingEventImageUrl;
  /** @maxLength 255 */
  baseColor?: RankingEventBaseColor;
  /** @maxLength 255 */
  addInfos?: RankingEventAddInfos;
  /** @maxLength 255 */
  judgeX?: RankingEventJudgeX;
  /** @maxLength 255 */
  judgeInstagram?: RankingEventJudgeInstagram;
  /** @maxLength 255 */
  shareHashTags?: RankingEventShareHashTags;
  /** @maxLength 255 */
  results?: RankingEventResults;
  applyStartAt: RankingEventApplyStartAt;
  applyEndAt: RankingEventApplyEndAt;
  startAt: RankingEventStartAt;
  endAt: RankingEventEndAt;
  calculatedAt: RankingEventCalculatedAt;
  archivedAt: RankingEventArchivedAt;
}

export type RankingEventInfoResponseBodyMetadataAnyOf = { [key: string]: unknown };

export type RankingEventInfoResponseBodyMetadata = RankingEventInfoResponseBodyMetadataAnyOf | null;

export type RankingEventInfoResponseBodyData = EventData | null;

export type RankingEventInfoResponseBodyErrors = ErrorObject[] | null;

export interface RankingEventInfoResponseBody {
  metadata?: RankingEventInfoResponseBodyMetadata;
  data: RankingEventInfoResponseBodyData;
  errors?: RankingEventInfoResponseBodyErrors;
}

export type RankingEventWithBoostRankingEvent = RankingEvent | null;

export type RankingEventWithBoostBoost = RankingYellBoost | null;

export interface RankingEventWithBoost {
  rankingEvent?: RankingEventWithBoostRankingEvent;
  boost?: RankingEventWithBoostBoost;
}

export type RankingYellBoostId = number | null;

export type RankingYellBoostCreatedAt = Instant | null;

export type RankingYellBoostUpdatedAt = Instant | null;

export type RankingYellBoostStartAt = Instant | null;

export type RankingYellBoostEndAt = Instant | null;

export type RankingYellBoostBoostRatio = number | null;

export interface RankingYellBoost {
  id?: RankingYellBoostId;
  createdAt: RankingYellBoostCreatedAt;
  updatedAt: RankingYellBoostUpdatedAt;
  startAt?: RankingYellBoostStartAt;
  endAt?: RankingYellBoostEndAt;
  boostRatio?: RankingYellBoostBoostRatio;
}

export interface RegisterCardRequest {
  cardName: string;
  token: string;
}

export type SaveFanmeCustomerRequestBuilding = string | null;

export interface SaveFanmeCustomerRequest {
  firstName: string;
  lastName: string;
  firstNameKana: string;
  lastNameKana: string;
  postalCode: string;
  prefecture: string;
  city: string;
  street: string;
  building?: SaveFanmeCustomerRequestBuilding;
  phoneNumber: string;
}

export interface SendEmailRequest {
  transactionId?: number;
}

export type ShopId = number | null;

export type ShopCreatedAt = Instant | null;

export type ShopUpdatedAt = Instant | null;

/**
 * @maxLength 50
 */
export type ShopCreatorUid = string | null;

/**
 * @maxLength 500
 */
export type ShopDescription = string | null;

export type ShopHeaderImageUri = string | null;

export interface Shop {
  id?: ShopId;
  createdAt: ShopCreatedAt;
  updatedAt: ShopUpdatedAt;
  /** @maxLength 50 */
  name: string;
  tenant: string;
  /** @maxLength 50 */
  creatorUid: ShopCreatorUid;
  /** @maxLength 500 */
  description?: ShopDescription;
  headerImageUri?: ShopHeaderImageUri;
  /**
   * @maxLength 100
   * @pattern \S
   */
  message: string;
  /** @minimum 0 */
  marginRate: number;
  /** @minimum 0 */
  tipMarginRate: number;
  isOpen: boolean;
  open?: boolean;
}

export interface ShopData {
  shop: ShopForGetShop;
}

export type ShopForGetShopDescription = string | null;

export type ShopForGetShopHeaderImageUri = string | null;

export type ShopForGetShopMessage = string | null;

export type ShopForGetShopItemTypeMarginRate = { [key: string]: number };

export interface ShopForGetShop {
  id?: number;
  tenant: string;
  creatorName: string;
  creatorIconUri: string;
  creatorUid: string;
  creatorAccountIdentity: string;
  name: string;
  description?: ShopForGetShopDescription;
  headerImageUri?: ShopForGetShopHeaderImageUri;
  message?: ShopForGetShopMessage;
  itemTypeMarginRate: ShopForGetShopItemTypeMarginRate;
  isOpen?: boolean;
  limitation: ShopLimitation;
  open?: boolean;
}

export interface ShopItemTypeMarginRateParam {
  itemType: string;
  /**
   * @minimum 0
   * @maximum 1
   */
  marginRate: number;
}

export interface ShopItemTypeMarginRateResult {
  shopId?: number;
  itemType: string;
  marginRate?: number;
}

export type ShopLimitationId = number | null;

export type ShopLimitationCreatedAt = Instant | null;

export type ShopLimitationUpdatedAt = Instant | null;

export interface ShopLimitation {
  id?: ShopLimitationId;
  createdAt: ShopLimitationCreatedAt;
  updatedAt: ShopLimitationUpdatedAt;
  fileCapacity: number;
  fileQuantity: number;
  isChekiExhibitable: boolean;
  chekiExhibitable?: boolean;
}

export type ShopResponseBodyMetadataAnyOf = { [key: string]: unknown };

export type ShopResponseBodyMetadata = ShopResponseBodyMetadataAnyOf | null;

export type ShopResponseBodyData = ShopData | null;

export type ShopResponseBodyErrors = ErrorObject[] | null;

export interface ShopResponseBody {
  metadata?: ShopResponseBodyMetadata;
  data: ShopResponseBodyData;
  errors?: ShopResponseBodyErrors;
}

export interface SingleOrderData {
  order: OrderResult;
}

export type SingleOrderResponseBodyMetadataAnyOf = { [key: string]: unknown };

export type SingleOrderResponseBodyMetadata = SingleOrderResponseBodyMetadataAnyOf | null;

export type SingleOrderResponseBodyData = SingleOrderData | null;

export type SingleOrderResponseBodyErrors = ErrorObject[] | null;

export interface SingleOrderResponseBody {
  metadata?: SingleOrderResponseBodyMetadata;
  data: SingleOrderResponseBodyData;
  errors?: SingleOrderResponseBodyErrors;
}

export type SnsLinkId = number | null;

export type SnsLinkCreatedAt = Instant | null;

export type SnsLinkUpdatedAt = Instant | null;

export type SnsLinkProfile = Profile | null;

/**
 * @maxLength 255
 */
export type SnsLinkType = string | null;

/**
 * @maxLength 255
 */
export type SnsLinkSnsAccountId = string | null;

export type SnsLinkSnsLinkDisplay = SnsLinkDisplay | null;

export interface SnsLink {
  id?: SnsLinkId;
  createdAt: SnsLinkCreatedAt;
  updatedAt: SnsLinkUpdatedAt;
  profile: SnsLinkProfile;
  /** @maxLength 255 */
  type: SnsLinkType;
  /** @maxLength 255 */
  snsAccountId: SnsLinkSnsAccountId;
  snsLinkDisplay?: SnsLinkSnsLinkDisplay;
}

export type SnsLinkDisplayId = number | null;

export type SnsLinkDisplayCreatedAt = Instant | null;

export type SnsLinkDisplayUpdatedAt = Instant | null;

export type SnsLinkDisplayProfile = Profile | null;

export type SnsLinkDisplaySnsLink = SnsLink | null;

export type SnsLinkDisplayDisplayOrderNumber = number | null;

export type SnsLinkDisplayDisplayable = boolean | null;

export interface SnsLinkDisplay {
  id?: SnsLinkDisplayId;
  createdAt: SnsLinkDisplayCreatedAt;
  updatedAt: SnsLinkDisplayUpdatedAt;
  profile: SnsLinkDisplayProfile;
  snsLink: SnsLinkDisplaySnsLink;
  displayOrderNumber: SnsLinkDisplayDisplayOrderNumber;
  displayable: SnsLinkDisplayDisplayable;
}

export interface SortItem {
  id?: number;
  sortOrder?: number;
}

export interface SortItemsRequest {
  items: SortItem[];
}

export interface SuggestAddressData {
  suggestedAddress: SuggestedAddressEntity;
}

export type SuggestAddressResponseBodyMetadataAnyOf = { [key: string]: unknown };

export type SuggestAddressResponseBodyMetadata = SuggestAddressResponseBodyMetadataAnyOf | null;

export type SuggestAddressResponseBodyData = SuggestAddressData | null;

export type SuggestAddressResponseBodyErrors = ErrorObject[] | null;

export interface SuggestAddressResponseBody {
  metadata?: SuggestAddressResponseBodyMetadata;
  data: SuggestAddressResponseBodyData;
  errors?: SuggestAddressResponseBodyErrors;
}

export interface SuggestedAddressEntity {
  prefecture: string;
  city: string;
  street: string;
}

export interface TipLimitData {
  tipLimit: TipUpperLimit;
}

export type TipLimitResponseBodyMetadataAnyOf = { [key: string]: unknown };

export type TipLimitResponseBodyMetadata = TipLimitResponseBodyMetadataAnyOf | null;

export type TipLimitResponseBodyData = TipLimitData | null;

export type TipLimitResponseBodyErrors = ErrorObject[] | null;

export interface TipLimitResponseBody {
  metadata?: TipLimitResponseBodyMetadata;
  data: TipLimitResponseBodyData;
  errors?: TipLimitResponseBodyErrors;
}

export interface TipUpperLimit {
  amount?: number;
}

export interface ToggleContentBlockDisplayableData {
  contentBlock: ToggleContentBlockDisplayableContentBlock;
}

export type ToggleContentBlockDisplayableRequestDisplayOrderNum = number | null;

export type ToggleContentBlockDisplayableRequestContentBlockId = number | null;

export type ToggleContentBlockDisplayableRequestDisplayable = boolean | null;

export interface ToggleContentBlockDisplayableRequest {
  displayOrderNum?: ToggleContentBlockDisplayableRequestDisplayOrderNum;
  contentBlockId?: ToggleContentBlockDisplayableRequestContentBlockId;
  displayable?: ToggleContentBlockDisplayableRequestDisplayable;
}

export type ToggleContentBlockDisplayableResponseMetadataAnyOf = { [key: string]: unknown };

export type ToggleContentBlockDisplayableResponseMetadata = ToggleContentBlockDisplayableResponseMetadataAnyOf | null;

export type ToggleContentBlockDisplayableResponseData = ToggleContentBlockDisplayableData | null;

export type ToggleContentBlockDisplayableResponseErrors = ErrorObject[] | null;

export interface ToggleContentBlockDisplayableResponse {
  metadata?: ToggleContentBlockDisplayableResponseMetadata;
  data: ToggleContentBlockDisplayableResponseData;
  errors: ToggleContentBlockDisplayableResponseErrors;
}

export type ToggleContentBlockDisplayableContentBlockDisplayable = boolean | null;

export interface ToggleContentBlockDisplayableContentBlock {
  id?: number;
  displayable?: ToggleContentBlockDisplayableContentBlockDisplayable;
}

export type TotalSalesThisMonth = MonthlySales | null;

export type TotalSalesLastMonth = MonthlySales | null;

export interface TotalSales {
  thisMonth?: TotalSalesThisMonth;
  lastMonth?: TotalSalesLastMonth;
}

export interface UpdateCardRequest {
  cardSequence?: number;
  cardName: string;
  cardHolderName: string;
  expire: string;
}

export type UpdateCartItemRequestQuantity = number | null;

export type UpdateCartItemRequestPurchaserComment = string | null;

export interface UpdateCartItemRequest {
  quantity?: UpdateCartItemRequestQuantity;
  purchaserComment?: UpdateCartItemRequestPurchaserComment;
}

export interface UpdateContentBlockDetailData {
  contentBlockDetail: ContentBlockDetail;
}

/**
 * @maxLength 50
 */
export type UpdateContentBlockDetailRequestTitle = string | null;

/**
 * @maxLength 500
 */
export type UpdateContentBlockDetailRequestDescription = string | null;

/**
 * @maxLength 500
 */
export type UpdateContentBlockDetailRequestAppDescription = string | null;

export type UpdateContentBlockDetailRequestUrl = string | null;

export type UpdateContentBlockDetailRequestIconUrl = string | null;

export type UpdateContentBlockDetailRequestFileUpload = FileUpload | null;

export interface UpdateContentBlockDetailRequest {
  contentBlockDetailId: number;
  /** @maxLength 50 */
  title?: UpdateContentBlockDetailRequestTitle;
  /** @maxLength 500 */
  description?: UpdateContentBlockDetailRequestDescription;
  /** @maxLength 500 */
  appDescription?: UpdateContentBlockDetailRequestAppDescription;
  url?: UpdateContentBlockDetailRequestUrl;
  iconUrl?: UpdateContentBlockDetailRequestIconUrl;
  fileUpload?: UpdateContentBlockDetailRequestFileUpload;
}

export type UpdateContentBlockDetailRequest1Title = string | null;

export type UpdateContentBlockDetailRequest1Description = string | null;

export type UpdateContentBlockDetailRequest1AppDescription = string | null;

export type UpdateContentBlockDetailRequest1Url = string | null;

export type UpdateContentBlockDetailRequest1IconUrl = string | null;

export interface UpdateContentBlockDetailRequest1 {
  contentBlockDetailId?: number;
  title?: UpdateContentBlockDetailRequest1Title;
  description?: UpdateContentBlockDetailRequest1Description;
  appDescription?: UpdateContentBlockDetailRequest1AppDescription;
  url?: UpdateContentBlockDetailRequest1Url;
  iconUrl?: UpdateContentBlockDetailRequest1IconUrl;
}

export type UpdateContentBlockDetailResponseMetadataAnyOf = { [key: string]: unknown };

export type UpdateContentBlockDetailResponseMetadata = UpdateContentBlockDetailResponseMetadataAnyOf | null;

export type UpdateContentBlockDetailResponseData = UpdateContentBlockDetailData | null;

export type UpdateContentBlockDetailResponseErrors = ErrorObject[] | null;

export interface UpdateContentBlockDetailResponse {
  metadata?: UpdateContentBlockDetailResponseMetadata;
  data: UpdateContentBlockDetailResponseData;
  errors: UpdateContentBlockDetailResponseErrors;
}

export type UpdateGachaItemRequestDescription = string | null;

export type UpdateGachaItemRequestSamples = GachaSampleFileForUpdate[] | null;

export type UpdateGachaItemRequestBenefits = GachaBenefitForUpdate[] | null;

export type UpdateGachaItemRequestTags = string[] | null;

export type UpdateGachaItemRequestItemOption = ItemOption1 | null;

export interface UpdateGachaItemRequest {
  name: string;
  description?: UpdateGachaItemRequestDescription;
  thumbnailUri: string;
  thumbnailFrom?: number;
  thumbnailBlurLevel?: number;
  thumbnailWatermarkLevel?: number;
  price?: number;
  available?: boolean;
  itemFiles: GachaFileForUpdate[];
  samples?: UpdateGachaItemRequestSamples;
  benefits?: UpdateGachaItemRequestBenefits;
  tags?: UpdateGachaItemRequestTags;
  itemOption?: UpdateGachaItemRequestItemOption;
}

export type UpdateOrderRequestTransactionId = number | null;

export interface UpdateOrderRequest {
  transactionId?: UpdateOrderRequestTransactionId;
  checkoutId?: number;
  status: string;
}

export interface UpdateProfileCoverData {
  profileCover: ProfileCover;
}

export interface UpdateProfileCoverImageData {
  profileCoverImage: ProfileCoverImage;
}

export interface UpdateProfileCoverImageRequest {
  coverImage: string;
  resourceType: string;
}

export type UpdateProfileCoverImageResponseMetadataAnyOf = { [key: string]: unknown };

export type UpdateProfileCoverImageResponseMetadata = UpdateProfileCoverImageResponseMetadataAnyOf | null;

export type UpdateProfileCoverImageResponseData = UpdateProfileCoverImageData | null;

export type UpdateProfileCoverImageResponseErrors = ErrorObject[] | null;

export interface UpdateProfileCoverImageResponse {
  metadata?: UpdateProfileCoverImageResponseMetadata;
  data: UpdateProfileCoverImageResponseData;
  errors: UpdateProfileCoverImageResponseErrors;
}

export type UpdateProfileCoverRequestCoverImageVisibility = boolean | null;

export type UpdateProfileCoverRequestBrightness = string | null;

export interface UpdateProfileCoverRequest {
  coverImageVisibility?: UpdateProfileCoverRequestCoverImageVisibility;
  brightness?: UpdateProfileCoverRequestBrightness;
}

export type UpdateProfileCoverResponseMetadataAnyOf = { [key: string]: unknown };

export type UpdateProfileCoverResponseMetadata = UpdateProfileCoverResponseMetadataAnyOf | null;

export type UpdateProfileCoverResponseData = UpdateProfileCoverData | null;

export type UpdateProfileCoverResponseErrors = ErrorObject[] | null;

export interface UpdateProfileCoverResponse {
  metadata?: UpdateProfileCoverResponseMetadata;
  data: UpdateProfileCoverResponseData;
  errors: UpdateProfileCoverResponseErrors;
}

export interface UpdateProfileData {
  profile: Profile;
}

export type UpdateProfileRequestBio = string | null;

export type UpdateProfileRequestHeaderImage = string | null;

export type UpdateProfileRequestSnsLinkColor = string | null;

export interface UpdateProfileRequest {
  bio?: UpdateProfileRequestBio;
  headerImage?: UpdateProfileRequestHeaderImage;
  snsLinkColor?: UpdateProfileRequestSnsLinkColor;
}

export type UpdateProfileResponseMetadataAnyOf = { [key: string]: unknown };

export type UpdateProfileResponseMetadata = UpdateProfileResponseMetadataAnyOf | null;

export type UpdateProfileResponseData = UpdateProfileData | null;

export type UpdateProfileResponseErrors = ErrorObject[] | null;

export interface UpdateProfileResponse {
  metadata?: UpdateProfileResponseMetadata;
  data: UpdateProfileResponseData;
  errors: UpdateProfileResponseErrors;
}

export interface UpdateShopItemTypeMarginRatesRequest {
  marginRates: ShopItemTypeMarginRateParam[];
}

export interface UpdateShopLimitationRequest {
  fileCapacity: number;
  fileQuantity: number;
  isChekiExhibitable: boolean;
}

export type UpdateShopLimitationResponseMetadataAnyOf = { [key: string]: unknown };

export type UpdateShopLimitationResponseMetadata = UpdateShopLimitationResponseMetadataAnyOf | null;

export type UpdateShopLimitationResponseData = ShopLimitation | null;

export type UpdateShopLimitationResponseErrors = ErrorObject[] | null;

export interface UpdateShopLimitationResponse {
  metadata?: UpdateShopLimitationResponseMetadata;
  data: UpdateShopLimitationResponseData;
  errors: UpdateShopLimitationResponseErrors;
}

export type UpdateShopRequestDescription = string | null;

export type UpdateShopRequestHeaderImageUri = string | null;

export type UpdateShopRequestMessage = string | null;

export interface UpdateShopRequest {
  name: string;
  description?: UpdateShopRequestDescription;
  headerImageUri?: UpdateShopRequestHeaderImageUri;
  message?: UpdateShopRequestMessage;
}

export type UpdateStatusRequestComment = string | null;

export interface UpdateStatusRequest {
  status?: number;
  comment?: UpdateStatusRequestComment;
}

export interface UpsertUserTutorialRequest {
  displayFlg?: boolean;
}

export type UserId = number | null;

export type UserCreatedAt = Instant | null;

export type UserUpdatedAt = Instant | null;

/**
 * @maxLength 255
 */
export type UserIcon = string | null;

/**
 * @maxLength 255
 */
export type UserName = string | null;

/**
 * @maxLength 255
 */
export type UserGender = string | null;

export type UserBirthday = Instant | null;

export type UserBirthdayConfirmed = boolean | null;

/**
 * @maxLength 255
 */
export type UserAccountIdentity = string | null;

export type UserPublic = boolean | null;

export type UserAllowPublicSharing = boolean | null;

/**
 * @maxLength 255
 */
export type UserUid = string | null;

export type UserDeletedAt = Instant | null;

export type UserFilledProfile = boolean | null;

export type UserPurpose = number | null;

export type UserUserAttributes = string | null;

export type UserIsBirthdayWeek = number | null;

export interface User {
  id?: UserId;
  createdAt: UserCreatedAt;
  updatedAt: UserUpdatedAt;
  /** @maxLength 255 */
  icon?: UserIcon;
  /** @maxLength 255 */
  name: UserName;
  /** @maxLength 255 */
  gender: UserGender;
  birthday: UserBirthday;
  birthdayConfirmed: UserBirthdayConfirmed;
  /** @maxLength 255 */
  accountIdentity: UserAccountIdentity;
  public: UserPublic;
  allowPublicSharing?: UserAllowPublicSharing;
  /** @maxLength 255 */
  uid?: UserUid;
  deletedAt?: UserDeletedAt;
  filledProfile: UserFilledProfile;
  purpose: UserPurpose;
  userAttributes?: UserUserAttributes;
  isBirthdayWeek?: UserIsBirthdayWeek;
  birthdayWeek?: number;
  iconUrl: string;
  displayName: string;
}

/**
 * 利用目的
 */
export type UserAttributesPurpose = string | null;

/**
 * 受取口座
 */
export type UserAttributesBankAccountType = string | null;

export interface UserAttributes {
  /** 利用目的 */
  purpose?: UserAttributesPurpose;
  /** 受取口座 */
  bankAccountType?: UserAttributesBankAccountType;
}

export type UserConsentId = number | null;

export type UserConsentCreatedAt = Instant | null;

export type UserConsentUpdatedAt = Instant | null;

export type UserConsentUser = User | null;

export type UserConsentConsent = Consent1 | null;

export interface UserConsent {
  id?: UserConsentId;
  createdAt: UserConsentCreatedAt;
  updatedAt: UserConsentUpdatedAt;
  user?: UserConsentUser;
  consent: UserConsentConsent;
}

export interface UserCurrentEntryCampaignsData {
  campaigns: Campaign[];
}

export type UserCurrentEntryCampaignsResponseBodyMetadataAnyOf = { [key: string]: unknown };

export type UserCurrentEntryCampaignsResponseBodyMetadata = UserCurrentEntryCampaignsResponseBodyMetadataAnyOf | null;

export type UserCurrentEntryCampaignsResponseBodyData = UserCurrentEntryCampaignsData | null;

export type UserCurrentEntryCampaignsResponseBodyErrors = ErrorObject[] | null;

export interface UserCurrentEntryCampaignsResponseBody {
  metadata?: UserCurrentEntryCampaignsResponseBodyMetadata;
  data: UserCurrentEntryCampaignsResponseBodyData;
  errors?: UserCurrentEntryCampaignsResponseBodyErrors;
}

export type UserMonthlySalesGrowthRate = number | null;

export interface UserMonthlySales {
  yearMonth: string;
  salesAmount?: number;
  purchaserCount?: number;
  purchaseCount?: number;
  growthRate?: UserMonthlySalesGrowthRate;
}

export interface UserProfileResponse {
  profile: GetProfileProfile;
}

export interface UserResponse {
  userResponse: ConsoleGetUserResponse;
}

export type UserSalesThisMonth = MonthlySales | null;

export type UserSalesLastMonth = MonthlySales | null;

export interface UserSales {
  userUid: string;
  userName: string;
  thisMonth?: UserSalesThisMonth;
  lastMonth?: UserSalesLastMonth;
}

export interface UserTutorialData {
  userTutorial: GetUserTutorialUserTutorial;
}

export type UserTutorialResponseBodyMetadataAnyOf = { [key: string]: unknown };

export type UserTutorialResponseBodyMetadata = UserTutorialResponseBodyMetadataAnyOf | null;

export type UserTutorialResponseBodyData = UserTutorialData | null;

export type UserTutorialResponseBodyErrors = ErrorObject[] | null;

export interface UserTutorialResponseBody {
  metadata?: UserTutorialResponseBodyMetadata;
  data: UserTutorialResponseBodyData;
  errors?: UserTutorialResponseBodyErrors;
}

export interface UsersByPartialAccountIdentityResponse {
  usersByPartialAccountIdentityResponse: User[];
}

export type GetAgencyMonthlySalesParams = {
  from?: string | null;
  to?: string | null;
};

export type GetAuditGroupsParams = {
  $count?: boolean | null;
  $skip?: number | null;
  $top?: number | null;
};

export type GetUsersByPartialAccountIdentityParams = {
  'partial-account-identity'?: string | null;
};

export type GetUserMonthlySalesParams = {
  $count?: boolean | null;
  $skip?: number | null;
  $top?: number | null;
  from?: string | null;
  to?: string | null;
};

export type GetUserTransactionParams = {
  $count?: boolean | null;
  $skip?: number | null;
  $top?: number | null;
};

export type GetCurrentUserItemsParams = {
  available?: boolean | null;
  tag?: string | null;
};

export type GetCurrentUserItemParams = {
  includeDeleted?: string | null;
};

export type GetPurchasedItemsParams = {
  includeTip?: boolean | null;
  itemTypes?: number[] | null;
};

export type GetItemsParams = {
  available?: boolean | null;
  tag?: string | null;
};

export type GmoWebhookBody = {
  ShopID: string;
  ShopPass: string;
  AccessID: string;
  AccessPass: string;
  OrderID: string;
  Status: string;
  Amount: string;
  Tax: string;
  PayType: string;
};
