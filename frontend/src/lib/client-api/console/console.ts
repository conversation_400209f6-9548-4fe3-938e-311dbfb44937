/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSwr from 'swr';
import useSWRMutation from 'swr/mutation';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type {
  AuditGroupsResponse,
  AuditObjectsResponse,
  AuditStatusResponseBody,
  BaseResponseBodyContentBlockDetailResponse,
  BaseResponseBodyContentBlockResponse,
  BaseResponseBodyContentBlocks,
  BaseResponseBodyListItemMarginRateResult,
  BaseResponseBodyListShopItemTypeMarginRateResult,
  BaseResponseBodyUserProfileResponse,
  BaseResponseBodyUserResponse,
  BaseResponseBodyUsersByPartialAccountIdentityResponse,
  ConsoleUsersResponseBody,
  CreateContentWithDetailRequest,
  GetAgenciesResponse,
  GetAgencyMonthlySalesParams,
  GetAgencyMonthlySalesResponse,
  GetAgencySalesResponse,
  GetAgencyUsersResponse,
  GetAllShopItemsResponse,
  GetAuditGroupsParams,
  GetUserMonthlySalesParams,
  GetUserMonthlySalesResponse,
  GetUserTransactionParams,
  GetUserTransactionsResponse,
  GetUsersByPartialAccountIdentityParams,
  ItemMarginRateItem,
  ProxyAccessTokenResponse,
  UpdateContentBlockDetailRequest,
  UpdateShopItemTypeMarginRatesRequest,
  UpdateShopLimitationRequest,
  UpdateShopLimitationResponse,
  UpdateStatusRequest,
} from '../client-shop-api.schemas';
import type { Arguments, Key, SWRConfiguration } from 'swr';
import type { SWRMutationConfiguration } from 'swr/mutation';

/**
 * @summary Get Agencies
 */
export const getAgencies = () => {
  return customClientInstance<GetAgenciesResponse>({ url: `/console/agencies`, method: 'GET' });
};

export const getGetAgenciesKey = () => [`/console/agencies`] as const;

export type GetAgenciesQueryResult = NonNullable<Awaited<ReturnType<typeof getAgencies>>>;
export type GetAgenciesQueryError = void;

/**
 * @summary Get Agencies
 */
export const useGetAgencies = <TError = void>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof getAgencies>>, TError> & { swrKey?: Key; enabled?: boolean };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetAgenciesKey() : null));
  const swrFn = () => getAgencies();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Agency Sales
 */
export const getAgencySales = (agencyId: number) => {
  return customClientInstance<GetAgencySalesResponse>({ url: `/console/agencies/${agencyId}/sales`, method: 'GET' });
};

export const getGetAgencySalesKey = (agencyId: number) => [`/console/agencies/${agencyId}/sales`] as const;

export type GetAgencySalesQueryResult = NonNullable<Awaited<ReturnType<typeof getAgencySales>>>;
export type GetAgencySalesQueryError = void;

/**
 * @summary Get Agency Sales
 */
export const useGetAgencySales = <TError = void>(
  agencyId: number,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getAgencySales>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!agencyId;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetAgencySalesKey(agencyId) : null));
  const swrFn = () => getAgencySales(agencyId);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Agency Monthly Sales
 */
export const getAgencyMonthlySales = (agencyId: number, params?: GetAgencyMonthlySalesParams) => {
  return customClientInstance<GetAgencyMonthlySalesResponse>({
    url: `/console/agencies/${agencyId}/sales/monthly`,
    method: 'GET',
    params,
  });
};

export const getGetAgencyMonthlySalesKey = (agencyId: number, params?: GetAgencyMonthlySalesParams) =>
  [`/console/agencies/${agencyId}/sales/monthly`, ...(params ? [params] : [])] as const;

export type GetAgencyMonthlySalesQueryResult = NonNullable<Awaited<ReturnType<typeof getAgencyMonthlySales>>>;
export type GetAgencyMonthlySalesQueryError = void;

/**
 * @summary Get Agency Monthly Sales
 */
export const useGetAgencyMonthlySales = <TError = void>(
  agencyId: number,
  params?: GetAgencyMonthlySalesParams,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getAgencyMonthlySales>>, TError> & {
      swrKey?: Key;
      enabled?: boolean;
    };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!agencyId;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetAgencyMonthlySalesKey(agencyId, params) : null));
  const swrFn = () => getAgencyMonthlySales(agencyId, params);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Agency Users
 */
export const getAgencyUsers = (agencyId: number) => {
  return customClientInstance<GetAgencyUsersResponse>({ url: `/console/agencies/${agencyId}/users`, method: 'GET' });
};

export const getGetAgencyUsersKey = (agencyId: number) => [`/console/agencies/${agencyId}/users`] as const;

export type GetAgencyUsersQueryResult = NonNullable<Awaited<ReturnType<typeof getAgencyUsers>>>;
export type GetAgencyUsersQueryError = void;

/**
 * @summary Get Agency Users
 */
export const useGetAgencyUsers = <TError = void>(
  agencyId: number,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getAgencyUsers>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!agencyId;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetAgencyUsersKey(agencyId) : null));
  const swrFn = () => getAgencyUsers(agencyId);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Audit Groups
 */
export const getAuditGroups = (params?: GetAuditGroupsParams) => {
  return customClientInstance<AuditGroupsResponse>({ url: `/console/audit-groups`, method: 'GET', params });
};

export const getGetAuditGroupsKey = (params?: GetAuditGroupsParams) =>
  [`/console/audit-groups`, ...(params ? [params] : [])] as const;

export type GetAuditGroupsQueryResult = NonNullable<Awaited<ReturnType<typeof getAuditGroups>>>;
export type GetAuditGroupsQueryError = void;

/**
 * @summary Get Audit Groups
 */
export const useGetAuditGroups = <TError = void>(
  params?: GetAuditGroupsParams,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getAuditGroups>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetAuditGroupsKey(params) : null));
  const swrFn = () => getAuditGroups(params);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Audit Objects
 */
export const getAuditObjects = (auditGroupId: number) => {
  return customClientInstance<AuditObjectsResponse>({
    url: `/console/audit-groups/${auditGroupId}/audit-objects`,
    method: 'GET',
  });
};

export const getGetAuditObjectsKey = (auditGroupId: number) =>
  [`/console/audit-groups/${auditGroupId}/audit-objects`] as const;

export type GetAuditObjectsQueryResult = NonNullable<Awaited<ReturnType<typeof getAuditObjects>>>;
export type GetAuditObjectsQueryError = void;

/**
 * @summary Get Audit Objects
 */
export const useGetAuditObjects = <TError = void>(
  auditGroupId: number,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getAuditObjects>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!auditGroupId;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetAuditObjectsKey(auditGroupId) : null));
  const swrFn = () => getAuditObjects(auditGroupId);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Update Audit Status
 */
export const updateAuditStatus = (auditGroupId: number, updateStatusRequest: UpdateStatusRequest) => {
  return customClientInstance<AuditStatusResponseBody>({
    url: `/console/audit-groups/${auditGroupId}/status`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateStatusRequest,
  });
};

export const getUpdateAuditStatusMutationFetcher = (auditGroupId: number) => {
  return (_: Key, { arg }: { arg: UpdateStatusRequest }): Promise<AuditStatusResponseBody> => {
    return updateAuditStatus(auditGroupId, arg);
  };
};
export const getUpdateAuditStatusMutationKey = (auditGroupId: number) =>
  [`/console/audit-groups/${auditGroupId}/status`] as const;

export type UpdateAuditStatusMutationResult = NonNullable<Awaited<ReturnType<typeof updateAuditStatus>>>;
export type UpdateAuditStatusMutationError = void;

/**
 * @summary Update Audit Status
 */
export const useUpdateAuditStatus = <TError = void>(
  auditGroupId: number,
  options?: {
    swr?: SWRMutationConfiguration<
      Awaited<ReturnType<typeof updateAuditStatus>>,
      TError,
      Key,
      UpdateStatusRequest,
      Awaited<ReturnType<typeof updateAuditStatus>>
    > & { swrKey?: string };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getUpdateAuditStatusMutationKey(auditGroupId);
  const swrFn = getUpdateAuditStatusMutationFetcher(auditGroupId);

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Update Item Margin Rates
 */
export const updateItemMarginRates = (accountIdentity: string, itemMarginRateItem: ItemMarginRateItem[]) => {
  return customClientInstance<BaseResponseBodyListItemMarginRateResult>({
    url: `/console/commission-control/creator/${accountIdentity}/items`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: itemMarginRateItem,
  });
};

export const getUpdateItemMarginRatesMutationFetcher = (accountIdentity: string) => {
  return (_: Key, { arg }: { arg: ItemMarginRateItem[] }): Promise<BaseResponseBodyListItemMarginRateResult> => {
    return updateItemMarginRates(accountIdentity, arg);
  };
};
export const getUpdateItemMarginRatesMutationKey = (accountIdentity: string) =>
  [`/console/commission-control/creator/${accountIdentity}/items`] as const;

export type UpdateItemMarginRatesMutationResult = NonNullable<Awaited<ReturnType<typeof updateItemMarginRates>>>;
export type UpdateItemMarginRatesMutationError = void;

/**
 * @summary Update Item Margin Rates
 */
export const useUpdateItemMarginRates = <TError = void>(
  accountIdentity: string,
  options?: {
    swr?: SWRMutationConfiguration<
      Awaited<ReturnType<typeof updateItemMarginRates>>,
      TError,
      Key,
      ItemMarginRateItem[],
      Awaited<ReturnType<typeof updateItemMarginRates>>
    > & { swrKey?: string };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getUpdateItemMarginRatesMutationKey(accountIdentity);
  const swrFn = getUpdateItemMarginRatesMutationFetcher(accountIdentity);

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get All Shop Items
 */
export const getAllShopItems = (accountIdentity: string) => {
  return customClientInstance<GetAllShopItemsResponse>({
    url: `/console/commission-control/creator/${accountIdentity}/shop/items`,
    method: 'GET',
  });
};

export const getGetAllShopItemsKey = (accountIdentity: string) =>
  [`/console/commission-control/creator/${accountIdentity}/shop/items`] as const;

export type GetAllShopItemsQueryResult = NonNullable<Awaited<ReturnType<typeof getAllShopItems>>>;
export type GetAllShopItemsQueryError = void;

/**
 * @summary Get All Shop Items
 */
export const useGetAllShopItems = <TError = void>(
  accountIdentity: string,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getAllShopItems>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!accountIdentity;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetAllShopItemsKey(accountIdentity) : null));
  const swrFn = () => getAllShopItems(accountIdentity);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Update Shop Item Type Margin Rates
 */
export const updateShopItemTypeMarginRates = (
  accountIdentity: string,
  shopId: number,
  updateShopItemTypeMarginRatesRequest: UpdateShopItemTypeMarginRatesRequest,
) => {
  return customClientInstance<BaseResponseBodyListShopItemTypeMarginRateResult>({
    url: `/console/commission-control/creator/${accountIdentity}/shop/${shopId}`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateShopItemTypeMarginRatesRequest,
  });
};

export const getUpdateShopItemTypeMarginRatesMutationFetcher = (accountIdentity: string, shopId: number) => {
  return (
    _: Key,
    { arg }: { arg: UpdateShopItemTypeMarginRatesRequest },
  ): Promise<BaseResponseBodyListShopItemTypeMarginRateResult> => {
    return updateShopItemTypeMarginRates(accountIdentity, shopId, arg);
  };
};
export const getUpdateShopItemTypeMarginRatesMutationKey = (accountIdentity: string, shopId: number) =>
  [`/console/commission-control/creator/${accountIdentity}/shop/${shopId}`] as const;

export type UpdateShopItemTypeMarginRatesMutationResult = NonNullable<
  Awaited<ReturnType<typeof updateShopItemTypeMarginRates>>
>;
export type UpdateShopItemTypeMarginRatesMutationError = void;

/**
 * @summary Update Shop Item Type Margin Rates
 */
export const useUpdateShopItemTypeMarginRates = <TError = void>(
  accountIdentity: string,
  shopId: number,
  options?: {
    swr?: SWRMutationConfiguration<
      Awaited<ReturnType<typeof updateShopItemTypeMarginRates>>,
      TError,
      Key,
      UpdateShopItemTypeMarginRatesRequest,
      Awaited<ReturnType<typeof updateShopItemTypeMarginRates>>
    > & { swrKey?: string };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getUpdateShopItemTypeMarginRatesMutationKey(accountIdentity, shopId);
  const swrFn = getUpdateShopItemTypeMarginRatesMutationFetcher(accountIdentity, shopId);

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Console Users
 */
export const getConsoleUsers = () => {
  return customClientInstance<ConsoleUsersResponseBody>({ url: `/console/console-users`, method: 'GET' });
};

export const getGetConsoleUsersKey = () => [`/console/console-users`] as const;

export type GetConsoleUsersQueryResult = NonNullable<Awaited<ReturnType<typeof getConsoleUsers>>>;
export type GetConsoleUsersQueryError = void;

/**
 * @summary Get Console Users
 */
export const useGetConsoleUsers = <TError = void>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof getConsoleUsers>>, TError> & { swrKey?: Key; enabled?: boolean };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetConsoleUsersKey() : null));
  const swrFn = () => getConsoleUsers();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Users By Partial Account Identity
 */
export const getUsersByPartialAccountIdentity = (params?: GetUsersByPartialAccountIdentityParams) => {
  return customClientInstance<BaseResponseBodyUsersByPartialAccountIdentityResponse>({
    url: `/console/users`,
    method: 'GET',
    params,
  });
};

export const getGetUsersByPartialAccountIdentityKey = (params?: GetUsersByPartialAccountIdentityParams) =>
  [`/console/users`, ...(params ? [params] : [])] as const;

export type GetUsersByPartialAccountIdentityQueryResult = NonNullable<
  Awaited<ReturnType<typeof getUsersByPartialAccountIdentity>>
>;
export type GetUsersByPartialAccountIdentityQueryError = void;

/**
 * @summary Get Users By Partial Account Identity
 */
export const useGetUsersByPartialAccountIdentity = <TError = void>(
  params?: GetUsersByPartialAccountIdentityParams,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getUsersByPartialAccountIdentity>>, TError> & {
      swrKey?: Key;
      enabled?: boolean;
    };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetUsersByPartialAccountIdentityKey(params) : null));
  const swrFn = () => getUsersByPartialAccountIdentity(params);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Update Content Block Detail
 */
export const updateContentBlockDetail = (
  accountIdentity: string,
  updateContentBlockDetailRequest: UpdateContentBlockDetailRequest,
) => {
  return customClientInstance<BaseResponseBodyContentBlockDetailResponse>({
    url: `/console/users/${accountIdentity}/content-blocks`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateContentBlockDetailRequest,
  });
};

export const getUpdateContentBlockDetailMutationFetcher = (accountIdentity: string) => {
  return (
    _: Key,
    { arg }: { arg: UpdateContentBlockDetailRequest },
  ): Promise<BaseResponseBodyContentBlockDetailResponse> => {
    return updateContentBlockDetail(accountIdentity, arg);
  };
};
export const getUpdateContentBlockDetailMutationKey = (accountIdentity: string) =>
  [`/console/users/${accountIdentity}/content-blocks`] as const;

export type UpdateContentBlockDetailMutationResult = NonNullable<Awaited<ReturnType<typeof updateContentBlockDetail>>>;
export type UpdateContentBlockDetailMutationError = void;

/**
 * @summary Update Content Block Detail
 */
export const useUpdateContentBlockDetail = <TError = void>(
  accountIdentity: string,
  options?: {
    swr?: SWRMutationConfiguration<
      Awaited<ReturnType<typeof updateContentBlockDetail>>,
      TError,
      Key,
      UpdateContentBlockDetailRequest,
      Awaited<ReturnType<typeof updateContentBlockDetail>>
    > & { swrKey?: string };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getUpdateContentBlockDetailMutationKey(accountIdentity);
  const swrFn = getUpdateContentBlockDetailMutationFetcher(accountIdentity);

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get User Content Blocks
 */
export const getUserContentBlocks = (accountIdentity: string) => {
  return customClientInstance<BaseResponseBodyContentBlocks>({
    url: `/console/users/${accountIdentity}/content-blocks`,
    method: 'GET',
  });
};

export const getGetUserContentBlocksKey = (accountIdentity: string) =>
  [`/console/users/${accountIdentity}/content-blocks`] as const;

export type GetUserContentBlocksQueryResult = NonNullable<Awaited<ReturnType<typeof getUserContentBlocks>>>;
export type GetUserContentBlocksQueryError = void;

/**
 * @summary Get User Content Blocks
 */
export const useGetUserContentBlocks = <TError = void>(
  accountIdentity: string,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getUserContentBlocks>>, TError> & {
      swrKey?: Key;
      enabled?: boolean;
    };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!accountIdentity;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetUserContentBlocksKey(accountIdentity) : null));
  const swrFn = () => getUserContentBlocks(accountIdentity);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Create Content Block
 */
export const createContentBlock = (
  accountIdentity: string,
  createContentWithDetailRequest: CreateContentWithDetailRequest,
) => {
  return customClientInstance<BaseResponseBodyContentBlockResponse>({
    url: `/console/users/${accountIdentity}/content-blocks`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createContentWithDetailRequest,
  });
};

export const getCreateContentBlockMutationFetcher = (accountIdentity: string) => {
  return (_: Key, { arg }: { arg: CreateContentWithDetailRequest }): Promise<BaseResponseBodyContentBlockResponse> => {
    return createContentBlock(accountIdentity, arg);
  };
};
export const getCreateContentBlockMutationKey = (accountIdentity: string) =>
  [`/console/users/${accountIdentity}/content-blocks`] as const;

export type CreateContentBlockMutationResult = NonNullable<Awaited<ReturnType<typeof createContentBlock>>>;
export type CreateContentBlockMutationError = void;

/**
 * @summary Create Content Block
 */
export const useCreateContentBlock = <TError = void>(
  accountIdentity: string,
  options?: {
    swr?: SWRMutationConfiguration<
      Awaited<ReturnType<typeof createContentBlock>>,
      TError,
      Key,
      CreateContentWithDetailRequest,
      Awaited<ReturnType<typeof createContentBlock>>
    > & { swrKey?: string };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getCreateContentBlockMutationKey(accountIdentity);
  const swrFn = getCreateContentBlockMutationFetcher(accountIdentity);

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Profile
 */
export const getProfile = (accountIdentity: string) => {
  return customClientInstance<BaseResponseBodyUserProfileResponse>({
    url: `/console/users/${accountIdentity}/profile`,
    method: 'GET',
  });
};

export const getGetProfileKey = (accountIdentity: string) => [`/console/users/${accountIdentity}/profile`] as const;

export type GetProfileQueryResult = NonNullable<Awaited<ReturnType<typeof getProfile>>>;
export type GetProfileQueryError = void;

/**
 * @summary Get Profile
 */
export const useGetProfile = <TError = void>(
  accountIdentity: string,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getProfile>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!accountIdentity;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetProfileKey(accountIdentity) : null));
  const swrFn = () => getProfile(accountIdentity);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Create Proxy Access Token
 */
export const createProxyAccessToken = (accountIdentity: string) => {
  return customClientInstance<ProxyAccessTokenResponse>({
    url: `/console/users/${accountIdentity}/proxy-access-token`,
    method: 'POST',
  });
};

export const getCreateProxyAccessTokenMutationFetcher = (accountIdentity: string) => {
  return (_: Key, __: { arg: Arguments }): Promise<ProxyAccessTokenResponse> => {
    return createProxyAccessToken(accountIdentity);
  };
};
export const getCreateProxyAccessTokenMutationKey = (accountIdentity: string) =>
  [`/console/users/${accountIdentity}/proxy-access-token`] as const;

export type CreateProxyAccessTokenMutationResult = NonNullable<Awaited<ReturnType<typeof createProxyAccessToken>>>;
export type CreateProxyAccessTokenMutationError = void;

/**
 * @summary Create Proxy Access Token
 */
export const useCreateProxyAccessToken = <TError = void>(
  accountIdentity: string,
  options?: {
    swr?: SWRMutationConfiguration<
      Awaited<ReturnType<typeof createProxyAccessToken>>,
      TError,
      Key,
      Arguments,
      Awaited<ReturnType<typeof createProxyAccessToken>>
    > & { swrKey?: string };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getCreateProxyAccessTokenMutationKey(accountIdentity);
  const swrFn = getCreateProxyAccessTokenMutationFetcher(accountIdentity);

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get User Monthly Sales
 */
export const getUserMonthlySales = (accountIdentity: string, params?: GetUserMonthlySalesParams) => {
  return customClientInstance<GetUserMonthlySalesResponse>({
    url: `/console/users/${accountIdentity}/sales/monthly`,
    method: 'GET',
    params,
  });
};

export const getGetUserMonthlySalesKey = (accountIdentity: string, params?: GetUserMonthlySalesParams) =>
  [`/console/users/${accountIdentity}/sales/monthly`, ...(params ? [params] : [])] as const;

export type GetUserMonthlySalesQueryResult = NonNullable<Awaited<ReturnType<typeof getUserMonthlySales>>>;
export type GetUserMonthlySalesQueryError = void;

/**
 * @summary Get User Monthly Sales
 */
export const useGetUserMonthlySales = <TError = void>(
  accountIdentity: string,
  params?: GetUserMonthlySalesParams,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getUserMonthlySales>>, TError> & {
      swrKey?: Key;
      enabled?: boolean;
    };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!accountIdentity;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetUserMonthlySalesKey(accountIdentity, params) : null));
  const swrFn = () => getUserMonthlySales(accountIdentity, params);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get User Transaction
 */
export const getUserTransaction = (accountIdentity: string, params?: GetUserTransactionParams) => {
  return customClientInstance<GetUserTransactionsResponse>({
    url: `/console/users/${accountIdentity}/sales/transactions`,
    method: 'GET',
    params,
  });
};

export const getGetUserTransactionKey = (accountIdentity: string, params?: GetUserTransactionParams) =>
  [`/console/users/${accountIdentity}/sales/transactions`, ...(params ? [params] : [])] as const;

export type GetUserTransactionQueryResult = NonNullable<Awaited<ReturnType<typeof getUserTransaction>>>;
export type GetUserTransactionQueryError = void;

/**
 * @summary Get User Transaction
 */
export const useGetUserTransaction = <TError = void>(
  accountIdentity: string,
  params?: GetUserTransactionParams,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getUserTransaction>>, TError> & {
      swrKey?: Key;
      enabled?: boolean;
    };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!accountIdentity;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetUserTransactionKey(accountIdentity, params) : null));
  const swrFn = () => getUserTransaction(accountIdentity, params);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Update Shop Limitation
 */
export const updateShopLimitation = (
  accountIdentity: string,
  updateShopLimitationRequest: UpdateShopLimitationRequest,
) => {
  return customClientInstance<UpdateShopLimitationResponse>({
    url: `/console/users/${accountIdentity}/shop-limitation`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateShopLimitationRequest,
  });
};

export const getUpdateShopLimitationMutationFetcher = (accountIdentity: string) => {
  return (_: Key, { arg }: { arg: UpdateShopLimitationRequest }): Promise<UpdateShopLimitationResponse> => {
    return updateShopLimitation(accountIdentity, arg);
  };
};
export const getUpdateShopLimitationMutationKey = (accountIdentity: string) =>
  [`/console/users/${accountIdentity}/shop-limitation`] as const;

export type UpdateShopLimitationMutationResult = NonNullable<Awaited<ReturnType<typeof updateShopLimitation>>>;
export type UpdateShopLimitationMutationError = void;

/**
 * @summary Update Shop Limitation
 */
export const useUpdateShopLimitation = <TError = void>(
  accountIdentity: string,
  options?: {
    swr?: SWRMutationConfiguration<
      Awaited<ReturnType<typeof updateShopLimitation>>,
      TError,
      Key,
      UpdateShopLimitationRequest,
      Awaited<ReturnType<typeof updateShopLimitation>>
    > & { swrKey?: string };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getUpdateShopLimitationMutationKey(accountIdentity);
  const swrFn = getUpdateShopLimitationMutationFetcher(accountIdentity);

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get User
 */
export const getUser = (id: number) => {
  return customClientInstance<BaseResponseBodyUserResponse>({ url: `/console/users/${id}`, method: 'GET' });
};

export const getGetUserKey = (id: number) => [`/console/users/${id}`] as const;

export type GetUserQueryResult = NonNullable<Awaited<ReturnType<typeof getUser>>>;
export type GetUserQueryError = void;

/**
 * @summary Get User
 */
export const useGetUser = <TError = void>(
  id: number,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getUser>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!id;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetUserKey(id) : null));
  const swrFn = () => getUser(id);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
