import { ReactElement } from 'react';
import clsx from 'clsx';
import Link from 'next/link';
import ShopPublicImage from '@/components/ShopImage';

interface PurchaseGuideButtonProps {
  className?: string;
}

const PurchaseGuideButton = ({ className }: PurchaseGuideButtonProps): ReactElement => {
  return (
    <Link
      href="https://media.fanme.link/2754/"
      target="_blank"
      rel="noopener noreferrer"
      className={clsx(
        className,
        'inline-flex h-8 items-center gap-2 rounded-full border border-orange-50 bg-orange-100 px-2',
      )}
    >
      <ShopPublicImage src="/images/icons/WhiteCart.svg" width={16} height={16} alt="cart" />
      <span className="text-medium-13 text-white">買い方を見る</span>
    </Link>
  );
};

export default PurchaseGuideButton;
