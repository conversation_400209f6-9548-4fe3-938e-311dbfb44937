'use client';
import { useCallback } from 'react';

type QuantitySliderProps = {
  value: number;
  min: number;
  max: number;
  onChange: (value: number) => void;
  disabled?: boolean;
};

const QuantitySlider = ({ value, min, max, onChange, disabled = false }: QuantitySliderProps) => {
  const handleSliderChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = parseInt(e.target.value, 10);
      onChange(newValue);
    },
    [onChange],
  );

  const progressPercentage = ((value - min) / (max - min)) * 100;

  return (
    <div className="w-full">
      <div className="relative" style={{ '--progress': `${progressPercentage}%` } as React.CSSProperties}>
        <input
          type="range"
          min={min}
          max={max}
          value={value}
          onChange={handleSliderChange}
          disabled={disabled}
          className="quantity-slider h-2 w-full cursor-pointer appearance-none rounded-base"
        />
      </div>
    </div>
  );
};

export default QuantitySlider;
