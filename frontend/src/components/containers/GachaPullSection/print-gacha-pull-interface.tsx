'use client';
import { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import Button from '@/components/atoms/button';
import QuantitySlider from '@/components/atoms/slider/quantity-slider';
import ShopPublicImage from '@/components/ShopImage';
import { FeatureFlags } from '@/lib/feature';
import { PRINT_GACHA_MIN_PULL_COUNT, PRINT_GACHA_MAX_PULL_COUNT } from '@/consts/gacha-data';
import { getIsNowOnSale } from '@/utils/item';
import { getItemTypeStrFromValue } from '@/types/item';
import type { Discount } from '@/types/shopItem';

type PrintGachaPullInterfaceProps = {
  price: number;
  currentPrice?: number;
  discount?: Discount;
  itemId: string;
  identityId: string;
  itemType: number;
  disabled?: boolean;
  isPreview?: boolean;
  theme?: 'light' | 'dark';
};

const PrintGachaPullInterface = ({
  price,
  currentPrice,
  discount,
  itemId,
  identityId,
  itemType,
  disabled = false,
  isPreview = false,
  theme = 'light',
}: PrintGachaPullInterfaceProps) => {
  const [quantity, setQuantity] = useState(PRINT_GACHA_MIN_PULL_COUNT);
  const router = useRouter();

  const themeStyles = useMemo(() => {
    if (theme === 'dark') {
      return {
        textColor: 'text-white',
        arrowButtonBg: 'bg-white',
        arrowButtonHoverBg: 'hover:bg-gray-100',
        leftArrowSrc: '/images/icons/Arrow_Back.svg',
        rightArrowSrc: '/images/icons/Arrow_Back.svg',
      };
    }
    return {
      textColor: 'text-black',
      arrowButtonBg: 'bg-black',
      arrowButtonHoverBg: 'hover:bg-gray-800',
      leftArrowSrc: '/images/icons/Arrow_Back_White.svg',
      rightArrowSrc: '/images/icons/Arrow_Back_White.svg',
    };
  }, [theme]);

  const isOnSale = useMemo(() => {
    return getIsNowOnSale({
      startAt: discount?.start,
      endAt: discount?.end,
      discountRate: discount?.percentage,
    });
  }, [discount]);

  const totalPrice = useMemo(() => {
    const basePrice = isOnSale && currentPrice ? currentPrice : price;
    return basePrice * quantity;
  }, [price, currentPrice, isOnSale, quantity]);

  const handlePurchase = () => {
    if (isPreview) return;

    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
    const url = !FeatureFlags.printGacha()
      ? `${baseUrl}/@${identityId}/order?isGacha=true&itemId=${itemId}&quantity=${quantity}`
      : `${baseUrl}/@${identityId}/order?itemType=${getItemTypeStrFromValue(itemType)}&itemId=${itemId}&quantity=${quantity}`;

    router.push(url);
  };

  return (
    <div className="px-4">
      <div className="flex flex-col gap-1">
        <div className="text-center">
          <div className={`flex items-center justify-center gap-1 ${themeStyles.textColor}`}>
            <span className="text-bold-20">{quantity}</span>
            <span className="text-bold-13">回引く</span>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <button
            type="button"
            onClick={() => setQuantity(Math.max(quantity - 1, PRINT_GACHA_MIN_PULL_COUNT))}
            disabled={quantity <= PRINT_GACHA_MIN_PULL_COUNT || disabled}
            className={`flex size-8 flex-shrink-0 items-center justify-center rounded-full shadow-sm transition-all disabled:cursor-not-allowed ${themeStyles.arrowButtonBg} ${themeStyles.arrowButtonHoverBg} disabled:bg-gray-200`}
          >
            <ShopPublicImage src={themeStyles.leftArrowSrc} width={20} height={20} alt="マイナス" />
          </button>

          <div className="flex-1">
            <QuantitySlider
              value={quantity}
              min={PRINT_GACHA_MIN_PULL_COUNT}
              max={PRINT_GACHA_MAX_PULL_COUNT}
              onChange={setQuantity}
              disabled={disabled}
            />
          </div>

          <button
            type="button"
            onClick={() => setQuantity(Math.min(quantity + 1, PRINT_GACHA_MAX_PULL_COUNT))}
            disabled={quantity >= PRINT_GACHA_MAX_PULL_COUNT || disabled}
            className={`flex size-8 flex-shrink-0 items-center justify-center rounded-full shadow-sm transition-all disabled:cursor-not-allowed ${themeStyles.arrowButtonBg} ${themeStyles.arrowButtonHoverBg} disabled:bg-gray-200`}
          >
            <ShopPublicImage
              src={themeStyles.rightArrowSrc}
              width={20}
              height={20}
              alt="次プラス"
              className="scale-x-[-1]"
            />
          </button>
        </div>

        <div className="text-center">
          <div className={`flex items-center justify-center gap-1.25 ${themeStyles.textColor}`}>
            <span className="text-bold-13">合計</span>
            <span className="text-bold-20">¥{totalPrice.toLocaleString()}</span>
          </div>
        </div>
      </div>

      <div className="mt-8 flex justify-center">
        <div className="w-full max-w-81">
          <Button buttonType="primary" onClick={handlePurchase} disabled={disabled} className="w-full">
            購入する
          </Button>
        </div>
      </div>

      <div className="mt-3 space-y-0.4 text-regular-11 text-orange-200">
        <p>※1回の購入ごとに送料がかかります</p>
        <p>※3,000円以上の購入で送料無料!</p>
      </div>
    </div>
  );
};

export default PrintGachaPullInterface;
