'use client';

import React, { useState, useRef, useEffect } from 'react';
import clsx from 'clsx';
import Button from '@/components/atoms/button';
import ShopPublicImage from '@/components/ShopImage';

export type FilterOption = {
  id: string;
  label: string;
  value: string;
  default?: boolean;
};

export type FilterTab = {
  id: string;
  label: string;
  options: FilterOption[];
  isSelected?: boolean;
};

type PulldownFilterProps = {
  tabs: FilterTab[];
  onFilterChange?: (tabId: string, selectedOption: FilterOption) => void;
  className?: string;
  initialSelectedOption?: FilterOption;
};

const PulldownFilter = ({ tabs, onFilterChange, className, initialSelectedOption }: PulldownFilterProps) => {
  const [selectedOptions, setSelectedOptions] = useState<Record<string, FilterOption>>({});
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 最初のタブを取得（複数タブがある場合でも最初のタブのみ表示）
  const activeTab = tabs[0];

  // 初期選択オプションを設定
  useEffect(() => {
    if (initialSelectedOption && activeTab) {
      setSelectedOptions({
        [activeTab.id]: initialSelectedOption,
      });
    }
  }, [initialSelectedOption, activeTab]);

  // 選択されたオプションがあるかチェック
  const hasSelectedOption = selectedOptions[activeTab?.id];

  // 外部クリックでドロップダウンを閉じる
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleHeaderClick = () => {
    setIsOpen(!isOpen);
  };

  const handleOptionSelect = (option: FilterOption) => {
    setSelectedOptions((prev) => ({
      ...prev,
      [activeTab.id]: option,
    }));
    setIsOpen(false);
    onFilterChange?.(activeTab.id, option);
  };

  if (!activeTab) return null;

  return (
    <div className="w-34">
      <div className={clsx('relative inline-block w-full', className)} ref={dropdownRef}>
        {/* フィルターヘッダー */}
        <div className="w-full">
          <Button
            buttonType="light-shadow"
            buttonSize="free"
            onClick={handleHeaderClick}
            className="!h-auto"
            buttonClassNames="h-8 w-full items-center !justify-start gap-1 px-3 "
            {...{ 'aria-expanded': isOpen, 'aria-haspopup': 'menu' }}
          >
            <ShopPublicImage src="/images/icons/sortOrange.svg" alt="sort" width={16} height={16} />
            <div className={clsx('w-full text-center text-medium-13 text-orange-100')}>
              {hasSelectedOption ? hasSelectedOption.label : activeTab.label}
            </div>
          </Button>
        </div>

        {/* ドロップダウンコンテンツ */}
        {isOpen && (
          <div className="absolute left-0 top-full z-50 mt-2 w-full px-2">
            <ul className="rounded bg-white drop-shadow-s" role="menu" aria-label={`${activeTab.label}オプション`}>
              {activeTab.options.map((option, index) => {
                const isSelected = selectedOptions[activeTab.id]?.id === option.id;
                const isLastItem = index === activeTab.options.length - 1;

                return (
                  <li
                    key={option.id}
                    role="menuitem"
                    onClick={() => handleOptionSelect(option)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleOptionSelect(option);
                      }
                    }}
                    tabIndex={0}
                    className={clsx(
                      'flex w-full cursor-pointer items-center justify-center px-2.5 py-1.5 text-center text-medium-13 font-medium transition-colors duration-150',
                      'hover:bg-gray-50 focus:bg-gray-50 focus:outline-none',
                      isSelected ? 'text-orange-100' : 'text-gray-800',
                      !isLastItem && 'border-b border-gray-300',
                    )}
                    aria-selected={isSelected}
                  >
                    {option.label}
                  </li>
                );
              })}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default PulldownFilter;
