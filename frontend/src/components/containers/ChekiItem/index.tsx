import React from 'react';
import clsx from 'clsx';
import StateBadge from '@/components/atoms/badges/state-badge';
import DiscountBadge from '@/components/atoms/DiscountBadge';
import ItemIcon from '@/components/atoms/itemIcon';
import BenefitBadge from '@/components/containers/ShopItem/BenefitBadge';
import ShopPublicImage from '@/components/ShopImage';
import ForSaleBadge from '../ShopItem/ForSaleBadge';
import OnSaleBadge from '../ShopItem/OnSaleBadge';
import { getIsNotEndForSale, getIsNowOnSale } from '@/utils/item';
import { ForSale, OnSale } from '@/types/shopItem';

type ChekiItemProps = {
  itemId: number;
  thumbnail: string;
  isNew?: boolean;
  title: string;
  price: number;
  currentPrice: number;
  minPrice: number;
  isAdded?: boolean;
  isCheckout?: boolean;
  hasPassword?: boolean;
  onSale?: OnSale | undefined;
  forSale?: ForSale;
  hasBenefit?: boolean;
  remainingAmount?: number;
  isStarted?: boolean;
  isPreview?: boolean;
  onShopItemEvent?: (itemId: number, e: React.MouseEvent) => void;
};

const ChekiItem = ({
  itemId,
  thumbnail,
  isNew,
  title,
  price,
  currentPrice,
  minPrice,
  isAdded,
  isCheckout,
  hasPassword = false,
  onSale,
  forSale,
  remainingAmount,
  isStarted,
  hasBenefit = false,
  isPreview,
  onShopItemEvent,
}: ChekiItemProps) => {
  const isNowOnSale = getIsNowOnSale(onSale);
  const isNotEndForSale = getIsNotEndForSale(isNowOnSale, forSale);

  const handleCardClick = (e: React.MouseEvent) => {
    if (onShopItemEvent) {
      onShopItemEvent(itemId, e);
    }
  };

  return (
    <div className={clsx(`relative flex h-32 w-full items-center`)}>
      <div
        onClick={onShopItemEvent ? handleCardClick : undefined}
        className={clsx(
          'flex size-full select-none items-center gap-x-3',
          'border-2 border-navy-50 shadow-light',
          'bg-white',
          'rounded-xl transition-all duration-75',
        )}
      >
        {remainingAmount && (
          <div className="absolute left-0 top-0 size-25">
            <div className="absolute -left-3 -top-3 z-30">
              <ShopPublicImage src="/images/LimitedSale_full.png" alt="Limited Sale badge" width={100} height={100} />
            </div>
            <div className={clsx('absolute inset-0.5 z-40 mt-1 flex -rotate-45 items-start justify-center')}>
              <span className="text-bold-16 text-white">{Math.max(remainingAmount, 0).toLocaleString()}</span>
            </div>
          </div>
        )}

        {isStarted && !isPreview && (
          <div className="absolute inset-x-0 top-0 z-40 mx-auto flex size-full items-center justify-center rounded-xl bg-transparent-white">
            <div className="h-8 w-32 bg-blue-200 text-center text-medium-13 leading-8 text-white">まもなく販売</div>
          </div>
        )}

        <div className={clsx(`relative h-full w-31 shrink-0 py-0.4 pl-0.4`)}>
          <ItemIcon thumbnail={thumbnail} thumbnailRatio={1} title={title} size={120} radius="lxl-l" />
          <div className="absolute inset-0 mb-2 flex items-end justify-center">
            <ForSaleBadge forSale={forSale} isShowOnSaleBadge={isNowOnSale} isShowForSaleBadge={isNotEndForSale} />
            <OnSaleBadge onSale={onSale} isShowOnSaleBadge={isNowOnSale} isShowForSaleBadge={isNotEndForSale} />
          </div>
          <div className="absolute right-2 top-2">
            {isAdded && (
              <StateBadge type="square-lined" color="lined-orange" size="sm">
                追加済み
              </StateBadge>
            )}
            {isCheckout && (
              <StateBadge type="square-lined" color="lined-blue" size="lg">
                お支払い待ち
              </StateBadge>
            )}
          </div>
          {hasPassword && (
            <div className="absolute inset-0 flex items-center justify-center">
              <ShopPublicImage src="/images/Pass.webp" alt="time" width={46} height={46} />
            </div>
          )}
        </div>

        <div className="flex h-32 w-full flex-col justify-between pb-2 pr-2 pt-3">
          <div
            className={clsx(
              'flex w-full items-center overflow-hidden',
              isNowOnSale ? 'max-h-9 min-h-9' : 'max-h-14 min-h-14',
            )}
          >
            <h3
              className={clsx(
                isNowOnSale ? 'line-clamp-2' : 'line-clamp-3',
                'w-full overflow-hidden text-ellipsis break-all text-medium-13',
              )}
            >
              {isNew && (
                <span className={clsx('mr-2 text-bold-15 text-pink-100', title.length <= 32 ? 'block' : 'inline')}>
                  NEW!
                </span>
              )}
              {title}
            </h3>
          </div>

          <div className="flex w-full items-center justify-between">
            <div className="flex flex-col">
              {isNowOnSale && onSale?.discountRate && onSale.discountRate > 0 && currentPrice ? (
                <>
                  <div className="flex items-center">
                    <div className="mr-1 text-bold-20">¥{currentPrice.toLocaleString()}</div>
                    <span className="mr-1 text-regular-10">(税込)</span>
                    <DiscountBadge percentage={onSale.discountRate} className="mr-2" />
                  </div>
                  <div className="text-regular-12 text-gray-400">
                    通常価格 <span className="line-through">¥{price.toLocaleString()}</span>
                  </div>
                </>
              ) : (
                <div className="flex items-center">
                  <div className="mr-1 text-bold-20">¥{minPrice.toLocaleString()}</div>
                  <span className="text-regular-10">(税込)</span>
                </div>
              )}
            </div>
          </div>
          {hasBenefit && <BenefitBadge />}
          {/*{itemType === ITEM_TYPE.digitalGacha ? (*/}
          {/*  <GachaCompleteProgressBarWithIcon*/}
          {/*    collectedUniqueItemsCount={collectedUniqueItemsCount || 0}*/}
          {/*    total={totalItemFileCount!}*/}
          {/*  />*/}
          {/*) : (*/}
          {/*  setTypeMap && (*/}
          {/*    <div className="mt-1 flex gap-2">*/}
          {/*      <GoodsTypesBadge*/}
          {/*        activeImage={setTypeMap.image}*/}
          {/*        activeVideo={setTypeMap.video}*/}
          {/*        activeAudio={setTypeMap.audio}*/}
          {/*        activeTokuten={hasBenefit}*/}
          {/*      />*/}
          {/*    </div>*/}
          {/*  )*/}
          {/*)}*/}
        </div>
      </div>
    </div>
  );
};

export default ChekiItem;
