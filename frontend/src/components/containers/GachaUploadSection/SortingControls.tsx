import { FC, RefObject } from 'react';
import clsx from 'clsx';
import FloatButton from '@/components/atoms/button/float-button';
import ShopPublicImage from '@/components/ShopImage';
import { MAX_GACHA_ITEM_COUNT } from '@/consts/sizes';

interface SortingControlsProps {
  tempItemFiles: any[];
  isEdit: boolean;
  isLoading: boolean;
  isSorting: boolean;
  isScrolled: boolean;
  isVisible: boolean;
  isFileQuantityDefault: boolean;
  floatButtonRef: RefObject<HTMLDivElement>;
  onSortStart: () => void;
  onSortComplete: () => void;
}

const SortingControls: FC<SortingControlsProps> = ({
  tempItemFiles,
  isEdit,
  isLoading,
  isSorting,
  isScrolled,
  isVisible,
  isFileQuantityDefault,
  floatButtonRef,
  onSortStart,
  onSortComplete,
}) => {
  if (tempItemFiles.length === 0 || isEdit) {
    return null;
  }

  return (
    <div className="relative my-4 flex h-10 items-center justify-between">
      <div
        ref={floatButtonRef}
        className={clsx(
          'inset-x-0 m-auto flex items-center justify-center',
          !isScrolled && 'absolute',
          isScrolled && 'fixed top-15 z-50',
          isScrolled && isVisible ? 'slide-in-animation visible' : '',
        )}
      >
        <FloatButton
          buttonSize="sm"
          disabled={isLoading}
          onClick={isSorting ? onSortComplete : onSortStart}
          className={clsx(isSorting && '!bg-primary')}
        >
          {!isSorting && (
            <div className="flex w-full items-center justify-start pl-2.5">
              <ShopPublicImage src="/images/icons/Sort2.svg" width={16} height={16} alt="sort" />
              <span className="ml-1">並び替え</span>
            </div>
          )}
          {isSorting && (
            <div className="flex w-full items-center justify-start">
              <ShopPublicImage
                src="/images/gacha/icons/Finish.svg"
                width={32}
                height={32}
                alt="complete"
                className="brightness-0"
              />
              <span>操作完了</span>
            </div>
          )}
        </FloatButton>
      </div>
      <div className="absolute right-0 flex items-center">
        <ShopPublicImage
          src="/images/icons/GrayBox.svg"
          width={14}
          height={14}
          alt="upload"
          className="brightness-130"
        />
        <span className="text-regular-14 text-gray-400">
          <em className="text-regular-16 not-italic">{tempItemFiles.length}</em>
          {isFileQuantityDefault && <>/{MAX_GACHA_ITEM_COUNT}</>}
        </span>
      </div>
    </div>
  );
};

export default SortingControls;
