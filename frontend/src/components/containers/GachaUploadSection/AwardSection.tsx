import React from 'react';
import clsx from 'clsx';
import { SortableContext, rectSortingStrategy } from '@dnd-kit/sortable';
import ShopPublicImage from '@/components/ShopImage';
import { SortableItem } from './gacha-drag-drop';
import FileGrid from './FileGrid';
import UploadButton from './UploadButton';
import { GachaItemFile, Award, AWARD_TYPE } from '@/types/gacha';
import { awardLabels } from '@/utils/gacha-validation-rules';

interface AwardSectionProps {
  awardType: Award;
  files: GachaItemFile[];
  sortableItems: GachaItemFile[];
  isSorting: boolean;
  isEdit: boolean;
  isLoading: boolean;
  uploadProgress: Record<string, number>;
  onDeleteFile?: (fileId: string) => void;
  onToggleSecret: (fileId: string, isSecret: boolean) => void;
  onSetThumbnail: (fileId: string) => (image: string) => Promise<void>;
  onClickUpload: (awardType?: Award) => void;
  onOpenIntroductionModal: () => void;
}

const AwardSection: React.FC<AwardSectionProps> = ({
  awardType,
  files,
  sortableItems,
  isSorting,
  isEdit,
  isLoading,
  uploadProgress,
  onDeleteFile,
  onToggleSecret,
  onSetThumbnail,
  onClickUpload,
  onOpenIntroductionModal,
}) => {
  const { label, icon } = awardLabels[awardType];
  const hasFiles = files.length > 0;

  // この賞タイプのファイルのみをフィルタリング
  const awardTypeFiles = isSorting ? sortableItems.filter((file) => file.awardType === awardType) : files;

  // 賞のセクションコンテンツを作成
  const awardSectionContent = (
    <div
      className={clsx(awardTypeFiles.length > 0 && 'border-b border-gray-200 pb-3.75', 'mt-6')}
      data-award-type={awardType}
    >
      {!isSorting && (
        <div>
          <div
            className={clsx(
              'my-4 flex items-center',
              awardTypeFiles.length === 0 && 'border-b border-gray-200 pb-3.75',
            )}
          >
            <ShopPublicImage src={icon} width={80} height={48} alt={label} />
            <UploadButton awardType={awardType} isLoading={isLoading} isEdit={isEdit} onClick={onClickUpload} />
          </div>

          {/* S賞には常にSECRETメッセージを表示 */}
          {awardType === AWARD_TYPE.S && (
            <div className="mb-4 text-center text-medium-13 text-orange-100 underline">
              <span onClick={onOpenIntroductionModal} className="cursor-pointer">
                SECRET設定をすると、サムネイルを
                <br />
                「シークレット」状態で出品できます
              </span>
            </div>
          )}
        </div>
      )}

      {isSorting ? (
        <SortableContext
          items={[
            ...awardTypeFiles.map((file) => file.id),
            // ドロップ可能エリアIDをアイテムリストに追加
            `droppable-${awardType}`,
          ]}
          strategy={rectSortingStrategy}
        >
          <div className="mb-4 flex items-center">
            <ShopPublicImage src={icon} width={80} height={48} alt={label} />
          </div>
          {awardTypeFiles.length > 0 ? (
            <FileGrid
              files={awardTypeFiles}
              awardType={awardType}
              isSorting={isSorting}
              isEdit={isEdit}
              uploadProgress={uploadProgress}
              onDeleteFile={onDeleteFile}
              onToggleSecret={onToggleSecret}
              onSetThumbnail={onSetThumbnail}
            />
          ) : (
            // ソート中の空のセクション用にドロップ可能エリアを作成
            <SortableItem id={`droppable-${awardType}`}>
              <div
                className="flex min-h-[100px] items-center justify-center rounded-md border-2 border-dashed border-gray-200"
                data-award-type={awardType}
              >
                <p className="text-gray-400">ここにドラッグして{awardLabels[awardType].label}に移動</p>
              </div>
            </SortableItem>
          )}
        </SortableContext>
      ) : (
        hasFiles && (
          <FileGrid
            files={files}
            awardType={awardType}
            isSorting={isSorting}
            isEdit={isEdit}
            uploadProgress={uploadProgress}
            onDeleteFile={onDeleteFile}
            onToggleSecret={onToggleSecret}
            onSetThumbnail={onSetThumbnail}
          />
        )
      )}
    </div>
  );

  return awardSectionContent;
};

export default AwardSection;
