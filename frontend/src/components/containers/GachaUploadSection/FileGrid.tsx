import React from 'react';
import Switch from '@/components/atoms/switch';
import UploadedItem from '@/components/containers/UploadedFile';
import { SortableItem } from './gacha-drag-drop';
import { GachaItemFile, Award, AWARD_TYPE } from '@/types/gacha';

interface FileGridProps {
  files: GachaItemFile[];
  awardType: Award;
  isSorting: boolean;
  isEdit: boolean;
  uploadProgress: Record<string, number>;
  onDeleteFile?: (fileId: string) => void;
  onToggleSecret: (fileId: string, isSecret: boolean) => void;
  onSetThumbnail: (fileId: string) => (image: string) => Promise<void>;
}

const FileGrid: React.FC<FileGridProps> = ({
  files,
  awardType,
  isSorting,
  isEdit,
  uploadProgress,
  onDeleteFile,
  onToggleSecret,
  onSetThumbnail,
}) => {
  if (files.length === 0) return null;

  return (
    <div className="grid grid-cols-3 gap-4">
      {files.map((file) => {
        const src =
          file.type === 'audio'
            ? '/shop/images/voice.png'
            : file.type === 'video'
              ? file.thumbnail
              : file.src || file.thumbnail;

        const fileContent = (
          <div className={`relative ${isSorting ? 'animate-shake_weekly' : ''}`}>
            <div className="mb-3">
              <UploadedItem
                id={file.id}
                src={src || ''}
                title={file.title || ''}
                type={file.type}
                setThumbImage={onSetThumbnail(file.id)}
                thumbnail={file.preSignedThumbnailUrl || file.thumbnail}
                handleDelete={!isSorting && !isEdit && onDeleteFile ? () => onDeleteFile(file.id) : undefined}
                showType={true}
                progress={uploadProgress[file.id] || 0}
                isLoading={file.isLoading}
                sorting={isSorting}
              />
            </div>

            {/* UploadedItemの下にSECRETトグルを追加 - S賞（awardType=4）の場合のみ表示 */}
            {!isSorting && awardType === AWARD_TYPE.S && (
              <div className="flex items-center justify-center">
                <span className="mr-2 text-xs">SECRET</span>
                <Switch
                  id={`switch-${file.id}`}
                  isOn={!!file.isSecret}
                  handleOnChange={(e) => onToggleSecret(file.id, e.target.checked)}
                  type="green"
                />
              </div>
            )}
          </div>
        );

        if (isSorting && !isEdit) {
          return (
            <SortableItem key={file.id} id={file.id}>
              {fileContent}
            </SortableItem>
          );
        } else {
          return <div key={file.id}>{fileContent}</div>;
        }
      })}
    </div>
  );
};

export default FileGrid;
