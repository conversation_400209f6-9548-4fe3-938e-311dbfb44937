import Button from '@/components/atoms/button';
import FloatButton from '@/components/atoms/button/float-button';
import ShopPublicImage from '@/components/ShopImage';
import { Award } from '@/types/gacha';

interface UploadButtonProps {
  awardType?: Award;
  isLoading: boolean;
  isEdit: boolean;
  variant?: 'initial' | 'section';
  onClick: (awardType?: Award) => void;
}

const UploadButton: React.FC<UploadButtonProps> = ({
  awardType = 4, // デフォルトはS賞
  isLoading,
  isEdit,
  variant = 'section',
  onClick,
}) => {
  if (variant === 'initial') {
    // 初期状態のアップロードボタン
    return (
      <div className="mb-3 mt-6 flex items-center justify-center">
        <Button buttonType="light-shadow" onClick={() => onClick()} disabled={isLoading}>
          <div className="absolute left-1 flex size-9.5 items-center justify-center rounded-full bg-gray-50">
            <ShopPublicImage src="/images/gacha/icons/uploadGacha.svg" width={22} height={22} alt="upload" />
          </div>
          <span>アップロード</span>
        </Button>
      </div>
    );
  }

  // 編集モードでは表示しない
  if (isEdit) {
    return null;
  }

  // セクション内のアップロードボタン
  return (
    <div className="ml-auto flex gap-2">
      <FloatButton buttonSize="md" onClick={() => onClick(awardType)} disabled={isLoading}>
        <div className="flex w-full items-center justify-start pl-2">
          <div className="mr-1 flex size-4.5 items-center justify-center rounded-full bg-secondary">
            <ShopPublicImage src="/images/icons/Close.svg" width={8} height={8} alt="add" className="rotate-45" />
          </div>
          <span>アップロード</span>
        </div>
      </FloatButton>
    </div>
  );
};

export default UploadButton;
