import { toast } from 'react-hot-toast';
import CustomToast from '@/components/atoms/toast/custom-toast';
import { ACCEPTED_IMAGE_FILE_TYPES, IMAGE_LONGER_SIDE } from '@/consts/file';
import { PRINT_SIZE, TRADING_CARD_SIZE } from '@/consts/sizes';
import { getTheLongerSide } from '@/utils/base';
import { handleGachaFileUpload } from '@/utils/gachaUpload';
import { ITEM_TYPE } from '@/types/item';
import { GachaItemFile, Award } from '@/types/gacha';
import { ShopLimitation } from '@/lib/server-api/shop-api.schemas';

export interface CropHandlerParams {
  file: File;
  itemType: string;
  currentAwardType: Award;
  tempItemFiles: GachaItemFile[];
  shopLimitation: ShopLimitation;
  identityId: string;
  itemId: string;
  calculateTotalFileSize: (files: GachaItemFile[]) => void;
  handleProgress: (fileId: string, progress: number) => void;
  resetProgress: () => void;
  onCropOpen: () => void;
  onCropClose: () => void;
  setCropperProps: (props: any) => void;
  cropMaskText: React.ReactNode;
}

// 画像の寸法をチェックする関数
export const checkImageDimensions = (file: File): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({ width: img.width, height: img.height });
    };
    img.onerror = () => {
      reject(new Error('画像の読み込みに失敗しました'));
    };
    img.src = URL.createObjectURL(file);
  });
};

// アイテムタイプに応じた画像検証とアスペクト比の取得
export const getImageValidationConfig = (
  itemType: string,
  width: number,
  height: number
): { isValid: boolean; errorMessage: string; aspectRatio: number } => {
  let isValid = false;
  let errorMessage = '';
  let aspectRatio = 1;

  if (itemType === ITEM_TYPE.PRINT_GACHA.str) {
    const longerSide = getTheLongerSide(width, height);
    isValid = longerSide >= IMAGE_LONGER_SIDE;
    errorMessage = `画像の長辺が${IMAGE_LONGER_SIDE}px以上である必要があります`;

    const isPortrait = height > width;
    aspectRatio = isPortrait
      ? PRINT_SIZE.LL.width / PRINT_SIZE.LL.height
      : PRINT_SIZE.LL.height / PRINT_SIZE.LL.width;
  } else if (itemType === ITEM_TYPE.TRADING_CARD_GACHA.str) {
    const longerSide = getTheLongerSide(width, height);
    const shorterSide = Math.min(width, height);
    isValid = longerSide >= TRADING_CARD_SIZE.height && shorterSide >= TRADING_CARD_SIZE.width;
    errorMessage = `${TRADING_CARD_SIZE.width}x${TRADING_CARD_SIZE.height} ピクセル以上の画像をアップロードしてください。`;

    const isPortrait = height > width;
    aspectRatio = isPortrait
      ? TRADING_CARD_SIZE.width / TRADING_CARD_SIZE.height
      : TRADING_CARD_SIZE.height / TRADING_CARD_SIZE.width;
  }

  return { isValid, errorMessage, aspectRatio };
};

// クロップ後の画像検証
export const validateCroppedImage = async (
  croppedFile: File,
  itemType: string
): Promise<{ isValid: boolean; errorMessage: string }> => {
  const { width: croppedWidth, height: croppedHeight } = await checkImageDimensions(croppedFile);

  let isValid = false;
  let errorMessage = '';

  if (itemType === ITEM_TYPE.PRINT_GACHA.str) {
    const croppedLongerSide = getTheLongerSide(croppedWidth, croppedHeight);
    isValid = croppedLongerSide >= IMAGE_LONGER_SIDE;
    errorMessage = `クロップされた画像の長辺が${IMAGE_LONGER_SIDE}px以上である必要があります`;
  } else if (itemType === ITEM_TYPE.TRADING_CARD_GACHA.str) {
    const croppedLongerSide = getTheLongerSide(croppedWidth, croppedHeight);
    const croppedShorterSide = Math.min(croppedWidth, croppedHeight);
    isValid = croppedLongerSide >= TRADING_CARD_SIZE.height && croppedShorterSide >= TRADING_CARD_SIZE.width;
    errorMessage = `クロップされた画像が${TRADING_CARD_SIZE.width}×${TRADING_CARD_SIZE.height} ピクセル以上である必要があります`;
  }

  return { isValid, errorMessage };
};

// メインの画像クロップ処理関数
export const handleCropGachaFile = async (params: CropHandlerParams) => {
  const {
    file,
    itemType,
    currentAwardType,
    tempItemFiles,
    shopLimitation,
    identityId,
    itemId,
    calculateTotalFileSize,
    handleProgress,
    resetProgress,
    onCropOpen,
    onCropClose,
    setCropperProps,
    cropMaskText,
  } = params;

  try {
    // 画像の寸法をチェック
    const { width, height } = await checkImageDimensions(file);

    // アイテムタイプに応じた検証とアスペクト比の設定
    const { isValid, errorMessage, aspectRatio } = getImageValidationConfig(itemType, width, height);

    if (!isValid) {
      toast.custom((t) => CustomToast(t, 'error', errorMessage));
      return;
    }

    const imageUrl = URL.createObjectURL(file);
    const isPortrait = height > width;

    const handleCropComplete = async (croppedImageUrl: string): Promise<void> => {
      try {
        const response = await fetch(croppedImageUrl);
        const blob = await response.blob();
        const croppedFile = new File([blob], file.name, { type: blob.type });

        // クロップ後の検証
        const { isValid: isCroppedValid, errorMessage: croppedErrorMessage } = await validateCroppedImage(
          croppedFile,
          itemType
        );

        if (!isCroppedValid) {
          toast.custom((t) => CustomToast(t, 'error', croppedErrorMessage));
          setCropperProps({
            image: imageUrl,
            aspect: aspectRatio,
            minZoom: 1,
            maskText: cropMaskText,
            onCropComplete: handleCropComplete,
            showAspectToggle: true,
            isPortrait: isPortrait,
          });
          return;
        }

        const fileList = new DataTransfer();
        fileList.items.add(croppedFile);

        await handleGachaFileUpload({
          currentFiles: fileList.files,
          existingFiles: tempItemFiles,
          shopLimitation,
          identityId,
          setFiles: (files) => calculateTotalFileSize(files),
          onProgress: handleProgress,
          resetProgress,
          awardType: currentAwardType,
          itemId,
          isNeedWatermark: true,
        });

        onCropClose();
        URL.revokeObjectURL(imageUrl);
      } catch (error) {
        console.error('Error processing cropped image:', error);
        toast.custom((t) => CustomToast(t, 'error', 'ファイルのアップロードに失敗しました'));
        onCropClose();
        URL.revokeObjectURL(imageUrl);
      }
    };

    onCropOpen();
    setCropperProps({
      image: imageUrl,
      aspect: aspectRatio,
      minZoom: 1,
      maskText: cropMaskText,
      showAspectToggle: true,
      isPortrait: isPortrait,
      onCropComplete: handleCropComplete,
    });
  } catch (error) {
    console.error('Error checking image dimensions:', error);
    toast.custom((t) => CustomToast(t, 'error', '画像の処理に失敗しました'));
  }
};
