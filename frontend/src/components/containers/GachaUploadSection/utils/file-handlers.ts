import { toast } from 'react-hot-toast';
import CustomToast from '@/components/atoms/toast/custom-toast';
import { GroupedGachaItems } from '@/hooks/useGroupGachaItems';
import { validateDeleteWithGroupedItems } from '@/utils/gacha-validation-rules';
import { uploadThumbnail } from '@/utils/thumbnail';
import { GachaItemFile, Award } from '@/types/gacha';

export interface FileHandlerParams {
  tempItemFiles: GachaItemFile[];
  itemId: string;
  setItemFiles: (itemId: string, files: GachaItemFile[]) => void;
  setTotalCapacity: (itemId: string, totalSize: number) => void;
}

// ファイル削除ハンドラ
export const createDeleteFileHandler = (params: FileHandlerParams, groupedItems: GroupedGachaItems) => {
  return (fileId: string) => {
    // グループ化されたアイテムを使用して削除検証を実行
    const validationResult = validateDeleteWithGroupedItems(groupedItems, fileId);
    if (!validationResult.valid) {
      toast.custom((t) => CustomToast(t, 'error', validationResult.message));
      return;
    }

    const updatedFiles = params.tempItemFiles.filter((file) => file.id !== fileId);
    const totalSize = updatedFiles.reduce((total, file) => total + (file.size || 0), 0);

    params.setItemFiles(params.itemId, updatedFiles);
    params.setTotalCapacity(params.itemId, totalSize);
  };
};

// シークレットトグルハンドラ
export const createToggleSecretHandler = (params: FileHandlerParams) => {
  return (fileId: string, isSecret: boolean) => {
    const updatedFiles = params.tempItemFiles.map((file) => {
      // ファイルIDが一致する場合、S賞（awardType=4）の場合のみisSecretを設定、それ以外はfalse
      if (file.id === fileId) {
        return {
          ...file,
          isSecret: file.awardType === 4 ? isSecret : false, // AWARD_TYPE.S = 4
        };
      }
      return file;
    });
    params.setItemFiles(params.itemId, updatedFiles);
  };
};

// サムネイル設定ハンドラ
export const createThumbnailHandler = (params: FileHandlerParams) => {
  return (fileId: string) => {
    return async (image: string) => {
      try {
        // 現在のファイル内でファイルを検索
        const fileIndex = params.tempItemFiles.findIndex((item) => item.id === fileId);
        if (fileIndex === -1) return;

        // 更新されたサムネイルで新しい配列を作成
        const updatedFiles = params.tempItemFiles.map((item) => {
          if (item.id === fileId) {
            return {
              ...item,
              thumbnail: item.type === 'audio' ? '/shop/images/voice.png' : image,
            };
          }
          return item;
        });

        // サムネイルをS3にアップロード
        const thumbnailUrl = await uploadThumbnail({
          img: image,
          fileId: fileId,
        });

        // アップロードされたサムネイルURLでファイルを更新
        const finalUpdatedFiles = updatedFiles.map((item) => {
          if (item.id === fileId) {
            return {
              ...item,
              thumbnail: thumbnailUrl.url,
            };
          }
          return item;
        });

        // ストア内のファイルを更新
        params.setItemFiles(params.itemId, finalUpdatedFiles);
      } catch (error) {
        console.error('Error updating thumbnail:', error);
        toast.custom((t) => CustomToast(t, 'error', 'サムネイルの更新に失敗しました'));
      }
    };
  };
};

// ファイルサイズ計算ハンドラ
export const createCalculateTotalFileSizeHandler = (params: FileHandlerParams) => {
  return (files: GachaItemFile[]) => {
    const totalSize = files.reduce((total, file) => total + (file.size || 0), 0);
    params.setItemFiles(params.itemId, files);
    params.setTotalCapacity(params.itemId, totalSize);
  };
};

// アップロードクリックハンドラ
export const createUploadClickHandler = (
  uploadRef: React.RefObject<HTMLInputElement>,
  setCurrentAwardType: (awardType: Award) => void,
  exhibitItem: any,
  isPrintGacha: (item: any) => boolean,
  ACCEPTED_IMAGE_FILE_TYPES: string,
  ACCEPTED_FILE_TYPES: string,
) => {
  return (awardType: Award = 4) => {
    // アップロード用の現在の賞タイプを設定
    setCurrentAwardType(awardType);

    // ファイル入力クリックをトリガー
    uploadRef.current?.setAttribute(
      'accept',
      exhibitItem && isPrintGacha(exhibitItem) ? ACCEPTED_IMAGE_FILE_TYPES : ACCEPTED_FILE_TYPES,
    );
    uploadRef.current?.click();
  };
};
