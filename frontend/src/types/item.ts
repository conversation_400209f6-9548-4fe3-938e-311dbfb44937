export const ITEM_TYPE = {
  DIGITAL_BUNDLE: {
    value: 0,
    str: 'digitalBundle',
  },
  DIGITAL_GACHA: {
    value: 1,
    str: 'digitalGacha',
  },
  CHEKI: {
    value: 2,
    str: 'cheki',
  },
  REAL_PHOTO: {
    value: 3,
    str: 'realPhoto',
  },
  PRINT_GACHA: {
    value: 4,
    str: 'printGacha',
  },
  TRADING_CARD_GACHA: {
    value: 5,
    str: 'tradingCardGacha',
  },
} as const;

export const PHYSICAL_ITEM_TYPES: number[] = [ITEM_TYPE.CHEKI.value] as const;
export const GACHA_ITEM_TYPES: number[] = [ITEM_TYPE.DIGITAL_GACHA.value, ITEM_TYPE.PRINT_GACHA.value] as const;
export const GACHA_ITEM_TYPES_STR: ItemTypeString[] = [ITEM_TYPE.DIGITAL_GACHA.str, ITEM_TYPE.PRINT_GACHA.str] as const;

export type ItemTypeKey = keyof typeof ITEM_TYPE;
export type ItemTypeValue = (typeof ITEM_TYPE)[ItemTypeKey]['value'];
export type ItemTypeString = (typeof ITEM_TYPE)[ItemTypeKey]['str'];

export const getItemTypeStrFromValue = (value: number): ItemTypeString => {
  const entry = Object.values(ITEM_TYPE).find((itemType) => itemType.value === value);
  if (!entry) {
    throw new Error(`Invalid item type value: ${value}`);
  }
  return entry.str;
};

export const getItemTypeValueFromStr = (str: ItemTypeString): ItemTypeValue => {
  const entry = Object.values(ITEM_TYPE).find((itemType) => itemType.str === str);
  if (!entry) {
    throw new Error(`Invalid item type string: ${str}`);
  }
  return entry.value;
};
