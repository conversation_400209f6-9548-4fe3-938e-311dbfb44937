'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useGetConsents } from '@/lib/fanme-api/fanme/fanme';
import { useCurrentUser } from '@/store/useCurrentUser';

export const useConsentCheck = () => {
  const { currentUser } = useCurrentUser();
  const router = useRouter();
  const { data: consentsData } = useGetConsents({
    swr: {
      enabled: !!currentUser,
    },
  });

  useEffect(() => {
    if (!currentUser || !consentsData?.data?.consents) {
      return;
    }

    const consents = consentsData.data.consents;

    // 必須の同意がある場合のリダイレクト処理
    const requiredConsent = consents.find((consent) => consent.required === true);
    if (requiredConsent?.url) {
      const currentPageUrl = encodeURIComponent(window.location.href);
      const redirectUrl = `${requiredConsent.url}?return-url=${currentPageUrl}`;
      router.push(redirectUrl);
    }
  }, [currentUser, consentsData, router]);
};
