const envVars = require(`./env/.env.${process.env.APP_ENV || 'local'}.json`);

const isDevelopment = () => {
  return process.env.APP_ENV === 'development';
};

/** @type {import('next').NextConfig} */
const nextConfig = {
  basePath: '/shop',
  assetPrefix: '/shop',

  reactStrictMode: true,

  distDir:
    process.env.APP_ENV === 'production'
      ? 'build/production'
      : process.env.APP_ENV === 'staging'
        ? 'build/staging'
        : process.env.APP_ENV === 'dev1'
          ? 'build/dev1'
          : process.env.APP_ENV === 'dev2'
            ? 'build/dev2'
            : process.env.APP_ENV === 'demo'
              ? 'build/demo'
              : 'build/local',
  experimental: {
    turbo: false,
  },

  env: envVars,

  // ローカルではこちらを使用
  // env: {
  //   PORT: 3000,
  //   NEXT_PUBLIC_BASE_URL: 'http://localhost:3000',
  // },

  async rewrites() {
    return [
      {
        source: '/hc',
        basePath: false,
        // ALBのヘルスチェックを通すにはlocalhostにする必要あり
        destination: process.env.APP_ENV === 'development' ? `${envVars.NEXT_PUBLIC_FRONT_API_BASE_URL}/hc` : 'http://localhost:3000/shop/hc'
      },
      {
        source: '/shop/:path*',
        destination: '/:path*',
      },
      {
        source: '/shop/api/:path*',
        destination: '/api/:path*',
      },
      {
        source: '/api/fanme/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://host.docker.internal:27002'}/api/fanme/:path*`,
      },
      {
        source: '/shop/images/:query*',
        destination: '/_next/image/:query*',
      },
      {
        source: '/shop/_next/:path*',
        destination: '/_next/:path*',
      },
    ];
  },

  images: {
    unoptimized: true,
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'host.docker.internal',
      },
      {
        protocol: 'https',
        hostname: 's3.ap-northeast-1.amazonaws.com',
      },
    ],
  },
  webpack(config) {
    // Grab the existing rule that handles SVG imports
    const fileLoaderRule = config.module.rules.find((rule) => rule.test?.test?.('.svg'));

    config.module.rules.push(
      // Reapply the existing rule, but only for svg imports ending in ?url
      {
        ...fileLoaderRule,
        test: /\.svg$/i,
        resourceQuery: /url/, // *.svg?url
      },
      // Convert all other *.svg imports to React components
      {
        test: /\.svg$/i,
        issuer: fileLoaderRule.issuer,
        resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] }, // exclude if *.svg?url
        use: ['@svgr/webpack'],
      },
    );

    // Modify the file loader rule to ignore *.svg, since we have it handled now.
    fileLoaderRule.exclude = /\.svg$/i;

    return config;
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
};

module.exports = nextConfig;
