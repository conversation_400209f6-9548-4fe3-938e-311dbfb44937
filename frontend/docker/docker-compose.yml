name: fanme-shop-frontend
services:
  app:
    image: node:20-alpine
    container_name: fanme-shop-frontend
    command: sh -c "cd /app && ([ -d /app/node_modules ] || yarn install --check-files) && yarn dev"
    volumes:
      - ..:/app
      - ../node_modules:/app/node_modules
      - next_cache:/app/.next
    working_dir: /app
    ports:
      - '27002:3000'
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 2G
    environment:
      - NODE_ENV=development
      - WATCHPACK_POLLING=true
      - CHOKIDAR_USEPOLLING=true

volumes:
  node_modules:
  next_cache:
