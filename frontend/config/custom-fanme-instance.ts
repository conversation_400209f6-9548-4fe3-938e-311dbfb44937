import axios from 'axios';
import * as cookie from 'cookie';
import { userServices } from '@/services/user';
import { type Camelize, camelizeKeys } from '@/utils/case-converter';

export type AllowedHeaders = {
  Accept?: string;
  Authorization?: string;
  'Content-Type'?: string;
  'X-Requested-With'?: string;
  'X-XSRF-TOKEN'?: string;
  'Accept-Language'?: string;
  'Cache-Control'?: string;
  Pragma?: string;
  'X-Proxy-Access-Token'?: string;
};

export const customFanmeAxios = axios.create({
  baseURL: process.env.NEXT_PUBLIC_FANME_API_URL,
  withCredentials: false,
  timeout: 10000, // 10秒タイムアウト
  headers: {
    Accept: 'application/json',
    'Content-Type': 'application/json',
  },
});

if (typeof window !== 'undefined') {
  // クライアントサイドのみ実行
  customFanmeAxios.interceptors.request.use(
    async (config) => {
      const token = await userServices.getFanmeCookie();
      if (token && config.headers) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      const cookies = cookie.parse(document.cookie);
      const proxyAccessToken = cookies['proxy_access_token'];
      if (proxyAccessToken && config.headers) {
        config.headers['X-Proxy-Access-Token'] = proxyAccessToken;
      }

      return config;
    },
    (error) => {
      return Promise.reject(error);
    },
  );
}

export const customFanmeInstance = async <T>(config: {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: AllowedHeaders;
  params?: any;
  data?: unknown;
  signal?: AbortSignal;
}): Promise<Camelize<T>> => {
  return customFanmeAxios
    .request<T>({
      url: config.url,
      method: config.method,
      headers: config.headers,
      params: config.params,
      data: config.data,
      signal: config.signal,
    })
    .then((res) => {
      if (
        res.data &&
        (res.data as any).errors &&
        Array.isArray((res.data as any).errors) &&
        (res.data as any).errors.length > 0
      ) {
        return Promise.reject(res.data);
      }
      const camelizedData = camelizeKeys(res.data);
      return camelizedData;
    })
    .catch((err) => {
      return Promise.reject(err);
    });
};
