plugins {
    kotlin("jvm") version "2.1.20"
    kotlin("plugin.allopen") version "2.1.20"
    id("io.quarkus") version "3.21.3"
    id("com.ncorti.ktfmt.gradle") version "0.22.0"
    id("io.gitlab.arturbosch.detekt") version "1.23.5"
}

repositories { mavenCentral() }

val quarkusPlatformGroupId: String by project
val quarkusPlatformArtifactId: String by project
val quarkusPlatformVersion: String by project

dependencies {
    implementation(
        enforcedPlatform(
            "${quarkusPlatformGroupId}:${quarkusPlatformArtifactId}:${quarkusPlatformVersion}"
        )
    )
    implementation("io.quarkus:quarkus-kotlin")
    implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8")
    implementation("io.quarkus:quarkus-arc")
    implementation("io.quarkus:quarkus-vertx-http")
    implementation("io.quarkus:quarkus-reactive-routes")

    // REST
    implementation("io.quarkus:quarkus-rest-jackson")
    implementation("io.quarkus:quarkus-rest")
    implementation("io.quarkus:quarkus-smallrye-openapi")

    // REST Client
    implementation("io.quarkus:quarkus-rest-client-jackson")

    // Auth
    implementation("io.quarkus:quarkus-elytron-security-properties-file")

    // DB
    implementation("io.quarkus:quarkus-jdbc-mysql")
    implementation("io.quarkus:quarkus-hibernate-orm-panache-kotlin")
    implementation("io.quarkus:quarkus-hibernate-orm")
    implementation("io.quarkus:quarkus-flyway")
    implementation("org.flywaydb:flyway-mysql")
    implementation("io.quarkus:quarkus-hibernate-validator")

    // Redis/Valkey
    implementation("io.quarkus:quarkus-redis-client")

    // Media
    implementation("org.imgscalr:imgscalr-lib:4.2")
    implementation("org.bytedeco:javacv-platform:1.5.11")
    implementation("org.bytedeco:ffmpeg-platform:4.3.2-1.5.8")

    // Quarkus MicroProfile JWT
    implementation("io.quarkus:quarkus-smallrye-jwt")
    implementation("io.quarkus:quarkus-smallrye-jwt-build")

    // Development
    implementation("com.michael-bull.kotlin-result:kotlin-result:2.0.1")

    // AWS
    implementation("software.amazon.awssdk:url-connection-client:2.31.33")
    implementation("software.amazon.awssdk:cloudfront:2.31.33")
    implementation("io.quarkiverse.amazonservices:quarkus-amazon-s3:3.4.0")

    // serialization
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.19.+")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.19.+")

    // CSV
    implementation("org.apache.commons:commons-csv:1.10.0")

    // Detekt - Static code analysis
    detektPlugins("io.gitlab.arturbosch.detekt:detekt-formatting:1.23.5")

    // TEST
    testImplementation("io.quarkus:quarkus-junit5")
    testImplementation("io.rest-assured:rest-assured")
    testImplementation("io.github.serpro69:kotlin-faker:1.16.0")
    testImplementation("io.quarkus:quarkus-junit5-mockito")
    testImplementation("org.testcontainers:mysql")
    testImplementation("org.reflections:reflections:0.10.2")
    testImplementation("io.quarkus:quarkus-test-security")
    testImplementation("io.quarkus:quarkus-jdbc-h2")

    // ArchUnit - アーキテクチャテスト用
    testImplementation("com.tngtech.archunit:archunit-junit5:1.2.1")

    // Mail
    implementation("io.quarkus:quarkus-mailer")

    // Logging
    implementation("io.quarkus:quarkus-logging-json")
}

group = "jp.co.torihada"

version = "1.0.0-SNAPSHOT"

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

tasks.withType<Test> {
    systemProperty("java.util.logging.manager", "org.jboss.logmanager.LogManager")
}

allOpen {
    annotation("jakarta.persistence.Entity")
    annotation("jakarta.ws.rs.Path")
    annotation("jakarta.enterprise.context.ApplicationScoped")
    annotation("io.quarkus.test.junit.QuarkusTest")
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
    kotlinOptions.jvmTarget = JavaVersion.VERSION_17.toString()
    kotlinOptions.javaParameters = true
}

ktfmt { kotlinLangStyle() }

// @Tag("slow")のテストをprepushでスキップする
tasks.test {
    useJUnitPlatform {
        if (project.hasProperty("skipSlow")) {
            excludeTags("slow")
        }
    }
}

detekt {
    buildUponDefaultConfig = true
    allRules = false
    config.setFrom("$projectDir/detekt.config.yml")
    baseline = file("$projectDir/detekt.baseline.xml")

    reports {
        html.required.set(true)
        xml.required.set(true)
        txt.required.set(true)
    }
}

tasks.register("detektUnused") {
    dependsOn("detekt")
    doLast { println("Checking for unused variables and functions completed") }
}
