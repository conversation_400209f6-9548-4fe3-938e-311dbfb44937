---
openapi: 3.1.0
components:
  schemas:
    ActionType:
      type: string
      enum:
      - NONE
      - MARGIN_TYPE1
    Agency:
      type: object
      required:
      - created_at
      - updated_at
      - name
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        name:
          type: string
        deleted_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
    AgencyMonthlySales:
      type: object
      required:
      - yearMonth
      properties:
        yearMonth:
          type: string
        totalSalesAmount:
          type: integer
          format: int32
        totalPurchaserCount:
          type: integer
          format: int32
        totalPurchaseCount:
          type: integer
          format: int32
        monthOverMonthGrowthRate:
          type:
          - number
          - "null"
    AgencySales:
      type: object
      required:
      - totalSales
      - usersSales
      properties:
        totalSales:
          $ref: "#/components/schemas/TotalSales"
        usersSales:
          type: array
          items:
            $ref: "#/components/schemas/UserSales"
    AllShopItemsResponse:
      type: object
      required:
      - shop
      - items
      properties:
        shop:
          $ref: "#/components/schemas/ShopForGetShop"
        items:
          type: array
          items:
            $ref: "#/components/schemas/Item"
    ApplePayParam:
      type: object
      required:
      - token
      properties:
        token:
          type: string
    AssetType:
      type: string
      enum:
      - IMAGE
      - VOICE
      - MOVIE
      - ANY
    AuditGroup:
      type: object
      required:
      - created_at
      - updated_at
      - userUid
      - auditType
      - operationType
      - status
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        userUid:
          type: string
          maxLength: 50
        user:
          anyOf:
          - $ref: "#/components/schemas/User"
          - type: "null"
        auditType:
          $ref: "#/components/schemas/AuditType"
        operationType:
          $ref: "#/components/schemas/OperationType"
        itemId:
          type:
          - integer
          - "null"
          format: int64
        metadata:
          type:
          - string
          - "null"
        status:
          $ref: "#/components/schemas/AuditStatus"
        comment:
          type:
          - string
          - "null"
        auditedAt:
          anyOf:
          - $ref: "#/components/schemas/LocalDateTime"
          - type: "null"
        auditedUserUid:
          type:
          - string
          - "null"
          maxLength: 50
        metadata_object:
          anyOf:
          - $ref: "#/components/schemas/AuditGroupMetadata"
          - type: "null"
    AuditGroupMetadata:
      type: object
      properties:
        shop_id:
          type:
          - integer
          - "null"
          format: int64
          description: ショップID
          example: 123
        item_id:
          type:
          - string
          - "null"
          description: 商品ID
          example: "456"
        title:
          type:
          - string
          - "null"
          description: タイトル
          example: 商品名
        description:
          type:
          - string
          - "null"
          description: 説明
          example: 商品の説明文
    AuditGroupsData:
      type: object
      required:
      - auditGroups
      properties:
        auditGroups:
          type: array
          items:
            $ref: "#/components/schemas/AuditGroup"
        totalCount:
          type:
          - integer
          - "null"
          format: int64
    AuditGroupsResponse:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/AuditGroupsData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    AuditObject:
      type: object
      required:
      - created_at
      - updated_at
      - auditGroupId
      - bucket
      - filePath
      - assetType
      - file_url
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        auditGroup:
          anyOf:
          - $ref: "#/components/schemas/AuditGroup"
          - type: "null"
        auditGroupId:
          type: integer
          format: int64
        bucket:
          type: string
          maxLength: 50
        filePath:
          type: string
          maxLength: 255
        assetType:
          $ref: "#/components/schemas/AssetType"
        file_url:
          type: string
    AuditObjectsData:
      type: object
      required:
      - auditObjects
      properties:
        auditObjects:
          type: array
          items:
            $ref: "#/components/schemas/AuditObject"
    AuditObjectsResponse:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/AuditObjectsData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    AuditStatus:
      type: string
      enum:
      - UNAUDITED
      - REJECTED
      - PENDING
      - RESEND
      - APPROVED
    AuditStatusData:
      type: object
      properties:
        result:
          type: boolean
    AuditStatusResponseBody:
      type: object
      required:
      - data
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/AuditStatusData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    AuditType:
      type: string
      enum:
      - SHOP
      - SHOP_ITEM
      - FANME_PROFILE
      - FANME_CONTENT
    AwardProbability:
      type: object
      properties:
        award_type:
          type: integer
          format: int32
        probability:
          type: integer
          format: int32
    AwardProbability1:
      type: object
      properties:
        awardType:
          type: integer
          format: int32
        probability:
          type: integer
          format: int32
    BadgeRankingData:
      type: object
      required:
      - ranking
      properties:
        ranking:
          type: array
          items:
            $ref: "#/components/schemas/GetGachaCompleteBadgeRankingResponse"
    BadgeRankingResponseBody:
      type: object
      required:
      - data
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/BadgeRankingData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    BaseResponseBodyContentBlockDetailResponse:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/ContentBlockDetailResponse"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    BaseResponseBodyContentBlockResponse:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/ContentBlockResponse"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    BaseResponseBodyContentBlocks:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/ContentBlocks"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    BaseResponseBodyCreateUserConsentResponse:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/CreateUserConsentResponse"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    BaseResponseBodyGetConsentsResponse:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/GetConsentsResponse"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    BaseResponseBodyItem:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/Item"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    BaseResponseBodyListItemMarginRateResult:
      type: object
      properties:
        data:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ItemMarginRateResult"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    BaseResponseBodyListShopItemTypeMarginRateResult:
      type: object
      properties:
        data:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ShopItemTypeMarginRateResult"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    BaseResponseBodyUser:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/User"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    BaseResponseBodyUserProfileResponse:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/UserProfileResponse"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    BaseResponseBodyUserResponse:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/UserResponse"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    BaseResponseBodyUsersByPartialAccountIdentityResponse:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/UsersByPartialAccountIdentityResponse"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    BenefitFile:
      type: object
      required:
      - name
      - file_type
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        name:
          type: string
        object_uri:
          type:
          - string
          - "null"
        thumbnail_uri:
          type:
          - string
          - "null"
        file_type:
          type: string
        size:
          type: number
          format: float
        duration:
          type:
          - integer
          - "null"
          format: int32
        item_thumbnail_selected:
          type:
          - boolean
          - "null"
        sort_order:
          type:
          - integer
          - "null"
          format: int32
    BenefitParam:
      type: object
      properties:
        id:
          type: integer
          format: int64
        description:
          type:
          - string
          - "null"
        condition_type:
          type: integer
          format: int32
        files:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/BenefitFile"
    Campaign:
      type: object
      required:
      - created_at
      - updated_at
      - campaignIdentity
      - title
      - entryType
      - actionType
      - startAt
      - endAt
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        campaignIdentity:
          type: string
        title:
          type: string
        user:
          anyOf:
          - $ref: "#/components/schemas/User"
          - type: "null"
        entryType:
          $ref: "#/components/schemas/EntryType"
        actionType:
          $ref: "#/components/schemas/ActionType"
        actionDurationDays:
          type: integer
          format: int32
        startAt:
          $ref: "#/components/schemas/Instant"
        endAt:
          $ref: "#/components/schemas/Instant"
    CardParam:
      type: object
      properties:
        cardSequence:
          type: integer
          format: int32
    CartItem:
      type: object
      required:
      - name
      - thumbnail_uri
      properties:
        cart_item_id:
          type: integer
          format: int64
        cart_id:
          type: integer
          format: int64
        item_type:
          type: integer
          format: int32
        item_id:
          type: integer
          format: int64
        file_id:
          type:
          - integer
          - "null"
          format: int64
        quantity:
          type: integer
          format: int32
        name:
          type: string
        thumbnail_uri:
          type: string
        price:
          type: integer
          format: int32
        margin_rate:
          type: number
          format: float
        current_price:
          type: integer
          format: int32
        discount_rate:
          type: number
          format: float
        file_type:
          type:
          - string
          - "null"
        file_quantities:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/FileQuantity"
        for_sale:
          type: boolean
        sold_out:
          type: boolean
        purchasable_quantity:
          type:
          - integer
          - "null"
          format: int32
        purchaser_comment:
          type:
          - string
          - "null"
    CartItemData:
      type: object
      required:
      - cart_items
      properties:
        cart_items:
          type: array
          items:
            $ref: "#/components/schemas/CartItem"
        delivery_fee:
          type:
          - integer
          - "null"
          format: int32
        is_locked:
          type: boolean
    CartItemsResponseBody:
      type: object
      required:
      - cart
      properties:
        cart:
          $ref: "#/components/schemas/CartItemData"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    CheckCartItemPriceRequest:
      type: object
      required:
      - itemPrices
      properties:
        itemPrices:
          type: array
          items:
            $ref: "#/components/schemas/ItemPriceSet"
    CheckoutStatus:
      type: string
      enum:
      - UNPROCESSED
      - REQSUCCESS
      - PAYSUCCESS
      - EXPIRED
      - CANCEL
      - PAYFAILED
    CompleteBadgeData:
      type: object
      required:
      - badge
      properties:
        badge:
          $ref: "#/components/schemas/Output1"
    CompleteBadgeResponseBody:
      type: object
      required:
      - data
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/CompleteBadgeData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    Consent:
      type: object
      required:
      - name
      - url
      properties:
        name:
          type: string
        url:
          type: string
        required:
          type: boolean
    Consent1:
      type: string
      enum:
      - TERMS_20250801
    ConsoleGetUserResponse:
      type: object
      required:
      - user
      - role
      properties:
        user:
          $ref: "#/components/schemas/User"
        role:
          type: string
        agencyId:
          type:
          - integer
          - "null"
          format: int64
    ConsoleUser:
      type: object
      required:
      - created_at
      - updated_at
      - user
      - role
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        user:
          $ref: "#/components/schemas/User"
        agency_id:
          type:
          - integer
          - "null"
          format: int64
        agency:
          anyOf:
          - $ref: "#/components/schemas/Agency"
          - type: "null"
        role:
          type: string
        deleted_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
    ConsoleUsersData:
      type: object
      required:
      - consoleUsers
      properties:
        consoleUsers:
          type: array
          items:
            $ref: "#/components/schemas/ConsoleUser"
    ConsoleUsersResponseBody:
      type: object
      required:
      - data
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/ConsoleUsersData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    ContentBlock:
      type: object
      required:
      - created_at
      - updated_at
      - user
      - contentBlockTypeId
      - displayOrderNumber
      - displayable
      - contentBlockGroups
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        user:
          anyOf:
          - $ref: "#/components/schemas/User"
          - type: "null"
        contentBlockTypeId:
          type:
          - integer
          - "null"
          format: int64
        displayOrderNumber:
          type:
          - integer
          - "null"
          format: int32
        displayable:
          type:
          - boolean
          - "null"
        contentBlockGroups:
          type: array
          uniqueItems: true
          items:
            $ref: "#/components/schemas/ContentBlockGroup"
    ContentBlockDetail:
      type: object
      required:
      - created_at
      - updated_at
      - title
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        icon:
          type:
          - string
          - "null"
          maxLength: 255
        title:
          type:
          - string
          - "null"
          maxLength: 255
        description:
          type:
          - string
          - "null"
        appDescription:
          type:
          - string
          - "null"
        url:
          type:
          - string
          - "null"
        style:
          type:
          - object
          - "null"
          additionalProperties: {}
        contentBlockGroup:
          anyOf:
          - $ref: "#/components/schemas/ContentBlockGroup"
          - type: "null"
        iconUrl:
          type:
          - string
          - "null"
    ContentBlockDetailResponse:
      type: object
      required:
      - contentBlockDetail
      properties:
        contentBlockDetail:
          $ref: "#/components/schemas/ContentBlockDetail"
    ContentBlockGroup:
      type: object
      required:
      - created_at
      - updated_at
      - contentBlock
      - contentBlockDetail
      - contentGroupNumber
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        contentBlock:
          anyOf:
          - $ref: "#/components/schemas/ContentBlock"
          - type: "null"
        contentBlockDetail:
          anyOf:
          - $ref: "#/components/schemas/ContentBlockDetail"
          - type: "null"
        contentGroupNumber:
          type:
          - integer
          - "null"
          format: int32
    ContentBlockResponse:
      type: object
      required:
      - contentBlock
      properties:
        contentBlock:
          $ref: "#/components/schemas/ContentBlock"
    ContentBlocks:
      type: object
      required:
      - contentBlocks
      properties:
        contentBlocks:
          type: array
          items:
            $ref: "#/components/schemas/GetContentBlock_ContentBlock"
    ConvenienceParam:
      type: object
      required:
      - convenience
      - customerName
      - customerKana
      - telNo
      properties:
        convenience:
          type: string
        customerName:
          type: string
        customerKana:
          type: string
        telNo:
          type: string
    CreateCartItemRequest:
      type: object
      properties:
        itemId:
          type: integer
          format: int64
        singleFile:
          type:
          - integer
          - "null"
          format: int64
        quantity:
          type: integer
          format: int32
    CreateContentBlockData:
      type: object
      required:
      - contentBlock
      properties:
        contentBlock:
          $ref: "#/components/schemas/ContentBlock"
    CreateContentBlockRequest:
      type: object
      required:
      - contentBlockType
      properties:
        contentBlockType:
          type: string
    CreateContentBlockResponse:
      type: object
      required:
      - data
      - errors
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/CreateContentBlockData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    CreateContentWithDetailRequest:
      type: object
      required:
      - contentBlockType
      properties:
        contentBlockType:
          type: integer
          format: int64
        title:
          type:
          - string
          - "null"
          maxLength: 50
        description:
          type:
          - string
          - "null"
          maxLength: 500
        appDescription:
          type:
          - string
          - "null"
          maxLength: 500
        url:
          type:
          - string
          - "null"
          pattern: ""
        iconUrl:
          type:
          - string
          - "null"
          pattern: ""
    CreateContentWithDetailRequest1:
      type: object
      required:
      - title
      - url
      properties:
        contentBlockType:
          type: integer
          format: int64
        title:
          type: string
        description:
          type:
          - string
          - "null"
        appDescription:
          type:
          - string
          - "null"
        url:
          type: string
        iconUrl:
          type:
          - string
          - "null"
    CreateGachaItemRequest:
      type: object
      required:
      - name
      - thumbnailUri
      - itemFiles
      - itemOption
      - awardProbabilities
      properties:
        name:
          type: string
        description:
          type:
          - string
          - "null"
        thumbnailUri:
          type: string
        thumbnailFrom:
          type: integer
          format: int32
        thumbnailBlurLevel:
          type: integer
          format: int32
        thumbnailWatermarkLevel:
          type: integer
          format: int32
        price:
          type: integer
          format: int32
        available:
          type: boolean
        itemFiles:
          type: array
          items:
            $ref: "#/components/schemas/GachaFile"
        samples:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/GachaSampleFile"
        benefits:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/GachaBenefit"
        tags:
          type:
          - array
          - "null"
          items:
            type: string
        itemOption:
          $ref: "#/components/schemas/ItemOption"
        isDuplicated:
          type: boolean
        awardProbabilities:
          type: array
          items:
            $ref: "#/components/schemas/AwardProbability1"
        itemType:
          type: integer
          format: int32
    CreateItemPasswordUnlockCacheRequest:
      type: object
      required:
      - userInputPassword
      properties:
        userInputPassword:
          type: string
    CreateOrUpdateItemRequest:
      type: object
      required:
      - name
      - thumbnailUri
      - itemFiles
      - itemOption
      properties:
        name:
          type: string
        description:
          type:
          - string
          - "null"
        thumbnailUri:
          type: string
        thumbnailFrom:
          type: integer
          format: int32
        thumbnailBlurLevel:
          type: integer
          format: int32
        thumbnailWatermarkLevel:
          type: integer
          format: int32
        price:
          type: integer
          format: int32
        available:
          type: boolean
        itemFiles:
          type: array
          items:
            $ref: "#/components/schemas/DigitalBundleFile"
        samples:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/DigitalBundleSampleFile"
        benefits:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/DigitalBundleBenefit"
        tags:
          type:
          - array
          - "null"
          items:
            type: string
        itemOption:
          $ref: "#/components/schemas/ItemOption"
        itemType:
          type:
          - integer
          - "null"
          format: int32
    CreateOrderRequest:
      type: object
      required:
      - cartItemIds
      - paymentMethod
      properties:
        cartId:
          type: integer
          format: int64
        cartItemIds:
          type: array
          items:
            type: integer
            format: int64
        tip:
          type: integer
          format: int32
        paymentMethod:
          type: string
        cardParam:
          anyOf:
          - $ref: "#/components/schemas/CardParam"
          - type: "null"
        convenienceParam:
          anyOf:
          - $ref: "#/components/schemas/ConvenienceParam"
          - type: "null"
        googlePayParam:
          anyOf:
          - $ref: "#/components/schemas/GooglePayParam"
          - type: "null"
        applePayParam:
          anyOf:
          - $ref: "#/components/schemas/ApplePayParam"
          - type: "null"
    CreateShopRequest:
      type: object
      required:
      - name
      properties:
        name:
          type: string
        description:
          type:
          - string
          - "null"
        headerImageUri:
          type:
          - string
          - "null"
        message:
          type:
          - string
          - "null"
        campaignIdentity:
          type:
          - string
          - "null"
    CreateSingleOrderRequest:
      type: object
      required:
      - paymentMethod
      properties:
        itemId:
          type: integer
          format: int64
        quantity:
          type: integer
          format: int32
        tip:
          type: integer
          format: int32
        paymentMethod:
          type: string
        cardParam:
          anyOf:
          - $ref: "#/components/schemas/CardParam"
          - type: "null"
        convenienceParam:
          anyOf:
          - $ref: "#/components/schemas/ConvenienceParam"
          - type: "null"
        googlePayParam:
          anyOf:
          - $ref: "#/components/schemas/GooglePayParam"
          - type: "null"
        applePayParam:
          anyOf:
          - $ref: "#/components/schemas/ApplePayParam"
          - type: "null"
    CreateSnsLinks_Response:
      type: object
      required:
      - snsLinks
      properties:
        snsLinks:
          type: array
          items:
            $ref: "#/components/schemas/SnsLink"
    CreateUserConsentRequest:
      type: object
      required:
      - consentName
      properties:
        consentName:
          type: string
    CreateUserConsentResponse:
      type: object
      properties:
        userConsent:
          anyOf:
          - $ref: "#/components/schemas/UserConsent"
          - type: "null"
    DeleteContentBlockData:
      type: object
      properties:
        success:
          type: boolean
    DeleteContentBlockResponse:
      type: object
      required:
      - data
      - errors
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/DeleteContentBlockData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    DeliveryFeeData:
      type: object
      properties:
        deliveryFee:
          type: integer
          format: int32
    DeliveryFeeResponseBody:
      type: object
      required:
      - data
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/DeliveryFeeData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    DigitalBundleBenefit:
      type: object
      required:
      - files
      properties:
        id:
          type: integer
          format: int64
        description:
          type:
          - string
          - "null"
        conditionType:
          type: integer
          format: int32
        files:
          type: array
          items:
            $ref: "#/components/schemas/DigitalBundleBenefitFile"
    DigitalBundleBenefitFile:
      type: object
      required:
      - name
      - objectUri
      - fileType
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        name:
          type: string
        objectUri:
          type: string
        thumbnailUri:
          type:
          - string
          - "null"
        fileType:
          type: string
        size:
          type: number
          format: float
        duration:
          type: integer
          format: int32
        itemThumbnailSelected:
          type:
          - boolean
          - "null"
        sortOrder:
          type:
          - integer
          - "null"
          format: int32
        conditionType:
          type: integer
          format: int32
    DigitalBundleFile:
      type: object
      required:
      - name
      - objectUri
      - thumbnailUri
      - fileType
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        name:
          type: string
        objectUri:
          type: string
        thumbnailUri:
          type: string
        price:
          type:
          - integer
          - "null"
          format: int32
        fileType:
          type: string
        size:
          type: number
          format: float
        duration:
          type: integer
          format: int32
        itemThumbnailSelected:
          type:
          - boolean
          - "null"
        sortOrder:
          type: integer
          format: int32
    DigitalBundleSampleFile:
      type: object
      required:
      - name
      - objectUri
      - fileType
      properties:
        name:
          type: string
        objectUri:
          type: string
        thumbnailUri:
          type:
          - string
          - "null"
        fileType:
          type: string
        size:
          type: number
          format: float
        duration:
          type: integer
          format: int32
    DisplayableCoverImage:
      type: object
      required:
      - created_at
      - updated_at
      - profileCover
      - profileCoverImage
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        profileCover:
          anyOf:
          - $ref: "#/components/schemas/ProfileCover"
          - type: "null"
        profileCoverImage:
          anyOf:
          - $ref: "#/components/schemas/ProfileCoverImage"
          - type: "null"
    DownloadUrl:
      type: object
      required:
      - key
      - url
      properties:
        key:
          type: string
        url:
          type: string
    DownloadUrlData:
      type: object
      required:
      - downloadUrls
      properties:
        downloadUrls:
          type: array
          items:
            $ref: "#/components/schemas/DownloadUrl"
    DownloadUrlResponseBody:
      type: object
      required:
      - data
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/DownloadUrlData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    EntryType:
      type: string
      enum:
      - NONE
      - LOGIN
      - SIGNUP
      - SHOP_CREATION
      - SHOP_CREATION_ALL
    ErrorObject:
      type: object
      required:
      - code
      - message
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
    EventData:
      type: object
      required:
      - event
      properties:
        event:
          $ref: "#/components/schemas/RankingEventWithBoost"
    FanmeCustomerData:
      type: object
      required:
      - fanmeCustomer
      properties:
        fanmeCustomer:
          $ref: "#/components/schemas/FanmeCustomerEntity"
    FanmeCustomerEntity:
      type: object
      required:
      - first_name
      - last_name
      - first_name_kana
      - last_name_kana
      - postal_code
      - prefecture
      - city
      - street
      - phone_number
      properties:
        creator_uid:
          type:
          - string
          - "null"
        first_name:
          type: string
        last_name:
          type: string
        first_name_kana:
          type: string
        last_name_kana:
          type: string
        postal_code:
          type: string
        prefecture:
          type: string
        city:
          type: string
        street:
          type: string
        building:
          type:
          - string
          - "null"
        phone_number:
          type: string
    FanmeCustomerResponseBody:
      type: object
      required:
      - data
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/FanmeCustomerData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    File:
      type: object
      required:
      - name
      - file_type
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        name:
          type: string
        object_uri:
          type:
          - string
          - "null"
        thumbnail_uri:
          type:
          - string
          - "null"
        masked_thumbnail_uri:
          type:
          - string
          - "null"
        watermark_thumbnail_uri:
          type:
          - string
          - "null"
        price:
          type:
          - integer
          - "null"
          format: int32
        current_price:
          type:
          - integer
          - "null"
          format: int32
        file_type:
          type: string
        size:
          type: number
          format: float
        duration:
          type:
          - integer
          - "null"
          format: int32
        is_purchased:
          type: boolean
        is_checkout:
          type: boolean
        item_thumbnail_selected:
          type: boolean
        award_type:
          type:
          - integer
          - "null"
          format: int32
        is_secret:
          type: boolean
        condition_type:
          type: integer
          format: int32
        received_file_count:
          type:
          - integer
          - "null"
          format: int32
    FileForPullGachaItems:
      type: object
      required:
      - name
      - file_type
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        object_uri:
          type:
          - string
          - "null"
        thumbnail_uri:
          type:
          - string
          - "null"
        file_type:
          type: string
        size:
          type: number
          format: float
        duration:
          type:
          - integer
          - "null"
          format: int32
        award_type:
          type: integer
          format: int32
        is_secret:
          type:
          - boolean
          - "null"
        watermark_thumbnail_uri:
          type:
          - string
          - "null"
    FileQuantity:
      type: object
      required:
      - file_type
      properties:
        file_type:
          type: string
        quantity:
          type: integer
          format: int32
    FileUpload:
      type: object
    FinalizeCreditCard3DSecureRequest:
      type: object
      properties:
        transactionId:
          type: integer
          format: int64
        checkoutId:
          type: integer
          format: int64
    ForSale:
      type: object
      properties:
        startAt:
          type:
          - string
          - "null"
        endAt:
          type:
          - string
          - "null"
    ForSaleData:
      type: object
      properties:
        start_at:
          type:
          - string
          - "null"
        end_at:
          type:
          - string
          - "null"
    GachaBenefit:
      type: object
      required:
      - files
      properties:
        id:
          type: integer
          format: int64
        description:
          type:
          - string
          - "null"
        conditionType:
          type: integer
          format: int32
        files:
          type: array
          items:
            $ref: "#/components/schemas/GachaBenefitFile"
    GachaBenefitFile:
      type: object
      required:
      - name
      - objectUri
      - fileType
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        name:
          type: string
        objectUri:
          type: string
        thumbnailUri:
          type:
          - string
          - "null"
        fileType:
          type: string
        size:
          type: number
          format: float
        duration:
          type: integer
          format: int32
        itemThumbnailSelected:
          type:
          - boolean
          - "null"
        sortOrder:
          type:
          - integer
          - "null"
          format: int32
        conditionType:
          type: integer
          format: int32
    GachaBenefitFileForUpdate:
      type: object
      required:
      - name
      - objectUri
      - fileType
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        objectUri:
          type: string
        thumbnailUri:
          type:
          - string
          - "null"
        fileType:
          type: string
        size:
          type: number
          format: float
        duration:
          type: integer
          format: int32
        itemThumbnailSelected:
          type:
          - boolean
          - "null"
        sortOrder:
          type:
          - integer
          - "null"
          format: int32
    GachaBenefitForUpdate:
      type: object
      required:
      - files
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        description:
          type:
          - string
          - "null"
        files:
          type: array
          items:
            $ref: "#/components/schemas/GachaBenefitFileForUpdate"
    GachaFile:
      type: object
      required:
      - name
      - objectUri
      - fileType
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        name:
          type: string
        objectUri:
          type: string
        thumbnailUri:
          type:
          - string
          - "null"
        maskedThumbnailUri:
          type:
          - string
          - "null"
        watermarkThumbnailUri:
          type:
          - string
          - "null"
        price:
          type:
          - integer
          - "null"
          format: int32
        fileType:
          type: string
        size:
          type: number
          format: float
        duration:
          type: integer
          format: int32
        itemThumbnailSelected:
          type:
          - boolean
          - "null"
        sortOrder:
          type:
          - integer
          - "null"
          format: int32
        awardType:
          type: integer
          format: int32
        isSecret:
          type:
          - boolean
          - "null"
    GachaFileForUpdate:
      type: object
      required:
      - name
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        isSecret:
          type: boolean
    GachaPullData:
      type: object
      required:
      - files
      properties:
        files:
          type: array
          items:
            $ref: "#/components/schemas/FileForPullGachaItems"
    GachaPullResponseBody:
      type: object
      required:
      - data
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/GachaPullData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    GachaPullableCountData:
      type: object
      required:
      - item
      properties:
        item:
          $ref: "#/components/schemas/Output"
    GachaPullableCountResponseBody:
      type: object
      required:
      - data
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/GachaPullableCountData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    GachaSampleFile:
      type: object
      required:
      - name
      - objectUri
      - fileType
      properties:
        name:
          type: string
        objectUri:
          type: string
        thumbnailUri:
          type:
          - string
          - "null"
        fileType:
          type: string
        size:
          type: number
          format: float
        duration:
          type: integer
          format: int32
    GachaSampleFileForUpdate:
      type: object
      required:
      - name
      - objectUri
      - fileType
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        name:
          type: string
        objectUri:
          type: string
        thumbnailUri:
          type:
          - string
          - "null"
        fileType:
          type: string
        size:
          type: number
          format: float
        duration:
          type: integer
          format: int32
    GetAgenciesData:
      type: object
      required:
      - agencies
      properties:
        agencies:
          type: array
          items:
            $ref: "#/components/schemas/Agency"
    GetAgenciesResponse:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/GetAgenciesData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    GetAgencyMonthlySalesData:
      type: object
      required:
      - agencyMonthlySalesList
      properties:
        agencyMonthlySalesList:
          type: array
          items:
            $ref: "#/components/schemas/AgencyMonthlySales"
    GetAgencyMonthlySalesResponse:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/GetAgencyMonthlySalesData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    GetAgencySalesData:
      type: object
      required:
      - agencySales
      properties:
        agencySales:
          $ref: "#/components/schemas/AgencySales"
    GetAgencySalesResponse:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/GetAgencySalesData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    GetAgencyUsersData:
      type: object
      required:
      - users
      properties:
        users:
          type: array
          items:
            $ref: "#/components/schemas/User"
    GetAgencyUsersResponse:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/GetAgencyUsersData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    GetAllShopItemsData:
      type: object
      required:
      - creatorShopItems
      properties:
        creatorShopItems:
          $ref: "#/components/schemas/AllShopItemsResponse"
    GetAllShopItemsResponse:
      type: object
      required:
      - data
      - errors
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/GetAllShopItemsData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    GetConsentsResponse:
      type: object
      required:
      - consents
      properties:
        consents:
          type: array
          items:
            $ref: "#/components/schemas/Consent"
    GetContentBlockData:
      type: object
      required:
      - contentBlocks
      properties:
        contentBlocks:
          type: array
          items:
            $ref: "#/components/schemas/GetContentBlock_ContentBlock"
    GetContentBlockResponse:
      type: object
      required:
      - data
      - errors
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/GetContentBlockData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    GetContentBlock_ContentBlock:
      type: object
      required:
      - contentBlockDetails
      properties:
        id:
          type: integer
          format: int64
        contentBlockDetails:
          type: array
          items:
            $ref: "#/components/schemas/GetContentBlock_ContentBlockDetail"
        contentBlockType:
          type: integer
          format: int64
        displayOrderNumber:
          type: integer
          format: int32
        displayable:
          type: boolean
    GetContentBlock_ContentBlockDetail:
      type: object
      required:
      - title
      properties:
        id:
          type: integer
          format: int64
        contentGroupNumber:
          type: integer
          format: int32
        isSetIcon:
          type: boolean
        title:
          type: string
        url:
          type:
          - string
          - "null"
        description:
          type:
          - string
          - "null"
        appDescription:
          type:
          - string
          - "null"
        icon:
          type:
          - string
          - "null"
        style:
          type:
          - object
          - "null"
          additionalProperties: {}
    GetDownloadUrlRequest:
      type: object
      required:
      - metadataList
      properties:
        metadataList:
          type: array
          items:
            $ref: "#/components/schemas/MetadataForGetDownloadUrlRequest"
        itemId:
          type: integer
          format: int64
    GetGachaCompleteBadgeRankingResponse:
      type: object
      required:
      - userUid
      - userAccountIdentity
      - userName
      - userIcon
      - getBadgeAt
      properties:
        userUid:
          type: string
        userAccountIdentity:
          type: string
        userName:
          type: string
        userIcon:
          type: string
        getBadgeAt:
          type: string
    GetPreSignedUrlRequest:
      type: object
      required:
      - metadataList
      - creatorAccountIdentity
      properties:
        metadataList:
          type: array
          items:
            $ref: "#/components/schemas/MetadataForGetPreSignedUrlRequest"
        creatorAccountIdentity:
          type: string
    GetProfileData:
      type: object
      required:
      - profile
      properties:
        profile:
          $ref: "#/components/schemas/GetProfile_Profile"
    GetProfileResponse:
      type: object
      required:
      - data
      - errors
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/GetProfileData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    GetProfile_CoverImage:
      type: object
      required:
      - resourceType
      - resource
      - brightness
      properties:
        resourceType:
          type: string
        resource:
          type: string
        coverVisibility:
          type: boolean
        brightness:
          type: string
    GetProfile_Profile:
      type: object
      required:
      - snsLinks
      properties:
        id:
          type: integer
          format: int64
        userId:
          type: integer
          format: int64
        bio:
          type:
          - string
          - "null"
        coverImage:
          anyOf:
          - $ref: "#/components/schemas/GetProfile_CoverImage"
          - type: "null"
        headerImage:
          type:
          - string
          - "null"
        snsLinkColor:
          type:
          - string
          - "null"
        snsLinks:
          type: array
          items:
            $ref: "#/components/schemas/GetProfile_SnsLink"
        themeColor:
          type:
          - string
          - "null"
        officialFlg:
          type: boolean
    GetProfile_SnsLink:
      type: object
      required:
      - type
      - snsAccountId
      properties:
        id:
          type: integer
          format: int64
        type:
          type: string
        snsAccountId:
          type: string
        displayOrderNumber:
          type: integer
          format: int32
        displayable:
          type: boolean
    GetUploadUrlRequest:
      type: object
      required:
      - metadataList
      properties:
        metadataList:
          type: array
          items:
            $ref: "#/components/schemas/MetadataForGetUploadUrlRequest"
    GetUserMonthlySalesData:
      type: object
      required:
      - userMonthlySalesList
      properties:
        userMonthlySalesList:
          type: array
          items:
            $ref: "#/components/schemas/UserMonthlySales"
    GetUserMonthlySalesResponse:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/GetUserMonthlySalesData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    GetUserTransactionsData:
      type: object
      required:
      - purchaseHistories
      properties:
        purchaseHistories:
          type: array
          items:
            $ref: "#/components/schemas/PurchaseHistory"
    GetUserTransactionsResponse:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/GetUserTransactionsData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    GetUserTutorial_UserTutorial:
      type: object
      required:
      - userUuid
      - name
      properties:
        userUuid:
          type: string
        name:
          type: string
        displayFlg:
          type: boolean
    GooglePayParam:
      type: object
      required:
      - token
      properties:
        token:
          type: string
    Instant:
      type: string
      format: date-time
      examples:
      - 2022-03-10T16:15:50Z
    Item:
      type: object
      required:
      - created_at
      - updated_at
      - shop
      - name
      - thumbnailUri
      - thumbnailFrom
      - thumbnailBlurLevel
      - thumbnailWatermarkLevel
      - price
      - fileType
      - available
      - marginRate
      - sortOrder
      - itemType
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        shop:
          $ref: "#/components/schemas/Shop"
        name:
          type: string
          minLength: 1
          maxLength: 100
          pattern: \S
        description:
          type:
          - string
          - "null"
          maxLength: 800
        thumbnailUri:
          type: string
          pattern: \S
        thumbnailFrom:
          type: integer
          format: int32
          maximum: 1
          minimum: 0
        thumbnailBlurLevel:
          type: integer
          format: int32
          maximum: 2
          minimum: 0
        thumbnailWatermarkLevel:
          type: integer
          format: int32
          maximum: 2
          minimum: 0
        price:
          type: integer
          format: int32
          maximum: 1000000
          minimum: 100
        fileType:
          type: integer
          format: int32
        available:
          type: boolean
        marginRate:
          type: number
          format: float
        sortOrder:
          type: integer
          format: int32
        itemType:
          $ref: "#/components/schemas/ItemType"
        digital:
          type: boolean
    ItemCostData:
      type: object
      properties:
        cost:
          type: integer
          format: int32
    ItemCostResponseBody:
      type: object
      required:
      - data
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/ItemCostData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    ItemData:
      type: object
      required:
      - item
      properties:
        item:
          $ref: "#/components/schemas/ItemForGetItem"
    ItemForGetItem:
      type: object
      required:
      - creator_account_identity
      - name
      - thumbnail_uri
      - file_type
      - item_option
      properties:
        id:
          type: integer
          format: int64
        creator_account_identity:
          type: string
        name:
          type: string
        description:
          type:
          - string
          - "null"
        thumbnail_uri:
          type: string
        thumbnail_from:
          type: integer
          format: int32
        thumbnail_blur_level:
          type: integer
          format: int32
        thumbnail_watermark_level:
          type: integer
          format: int32
        price:
          type: integer
          format: int32
        current_price:
          type: integer
          format: int32
        file_type:
          type: string
        available:
          type: boolean
        award_probabilities:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/AwardProbability"
        is_duplicated_digital_gacha_items:
          type:
          - boolean
          - "null"
        item_type:
          type: integer
          format: int32
        files:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/File"
        samples:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/File"
        benefits:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/BenefitParam"
        tags:
          type:
          - array
          - "null"
          items:
            type: string
        item_option:
          $ref: "#/components/schemas/OptionData"
        is_purchased:
          type: boolean
        is_checkout:
          type: boolean
        purchased_count:
          type: integer
          format: int32
        collected_unique_items_count:
          type: integer
          format: int32
        is_completed:
          type: boolean
        remaining_unique_pull_count:
          type:
          - integer
          - "null"
          format: int32
        is_publish_locked:
          type: boolean
    ItemMarginRateItem:
      type: object
      properties:
        itemId:
          type: integer
          format: int64
        marginRate:
          type: number
          format: float
    ItemMarginRateResult:
      type: object
      properties:
        shopId:
          type: integer
          format: int64
        itemId:
          type: integer
          format: int64
        marginRate:
          type: number
          format: float
    ItemOption:
      type: object
      properties:
        isSingleSales:
          type: boolean
        qtyTotal:
          type:
          - integer
          - "null"
          format: int32
        qtyPerUser:
          type:
          - integer
          - "null"
          format: int32
        forSale:
          anyOf:
          - $ref: "#/components/schemas/ForSale"
          - type: "null"
        password:
          type:
          - string
          - "null"
        onSale:
          anyOf:
          - $ref: "#/components/schemas/OnSale"
          - type: "null"
    ItemOption1:
      type: object
      properties:
        password:
          type:
          - string
          - "null"
        onSale:
          anyOf:
          - $ref: "#/components/schemas/OnSale"
          - type: "null"
        forSale:
          anyOf:
          - $ref: "#/components/schemas/ForSale"
          - type: "null"
    ItemPriceSet:
      type: object
      properties:
        cartItemId:
          type: integer
          format: int64
        displayedPrice:
          type: integer
          format: int32
    ItemResponseBody:
      type: object
      required:
      - data
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/ItemData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    ItemType:
      type: string
      enum:
      - DIGITAL_BUNDLE
      - DIGITAL_GACHA
      - CHEKI
      - REAL_PHOTO
      - PRINT_GACHA
    LocalDateTime:
      type: string
      format: date-time
      examples:
      - 2022-03-10T12:15:50
    MetadataForGetDownloadUrlRequest:
      type: object
      required:
      - key
      properties:
        key:
          type: string
        name:
          type:
          - string
          - "null"
    MetadataForGetPreSignedUrlRequest:
      type: object
      required:
      - id
      - key
      properties:
        id:
          type: string
        key:
          type: string
    MetadataForGetUploadUrlRequest:
      type: object
      required:
      - id
      properties:
        id:
          type: string
        name:
          type:
          - string
          - "null"
    MonthlySales:
      type: object
      required:
      - yearMonth
      properties:
        yearMonth:
          type: string
        sellerSalesAmount:
          type: integer
          format: int32
        purchaserCount:
          type: integer
          format: int32
        purchaseCount:
          type: integer
          format: int32
    MoveContentBlockData:
      type: object
      required:
      - contentBlocks
      properties:
        contentBlocks:
          $ref: "#/components/schemas/MoveContentBlock_ContentBlocks"
    MoveContentBlockDetailData:
      type: object
      required:
      - contentBlock
      properties:
        contentBlock:
          $ref: "#/components/schemas/MoveContentBlockDetail_ContentBlocks"
    MoveContentBlockDetailRequest:
      type: object
      properties:
        fromContentBlockDetailId:
          type:
          - integer
          - "null"
          format: int64
        toContentBlockDetailId:
          type:
          - integer
          - "null"
          format: int64
        contentBlockDetailIds:
          type:
          - array
          - "null"
          items:
            type: integer
            format: int64
    MoveContentBlockDetailResponse:
      type: object
      required:
      - data
      - errors
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/MoveContentBlockDetailData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    MoveContentBlockDetail_ContentBlock:
      type: object
      required:
      - contentBlockDetails
      properties:
        id:
          type: integer
          format: int64
        contentBlockDetails:
          type: array
          items:
            $ref: "#/components/schemas/MoveContentBlockDetail_ContentBlockDetail"
    MoveContentBlockDetail_ContentBlockDetail:
      type: object
      properties:
        id:
          type: integer
          format: int64
        contentGroupNumber:
          type: integer
          format: int32
    MoveContentBlockDetail_ContentBlocks:
      type: object
      required:
      - contentBlock
      properties:
        contentBlock:
          $ref: "#/components/schemas/MoveContentBlockDetail_ContentBlock"
    MoveContentBlockRequest:
      type: object
      properties:
        displayOrderNum:
          type:
          - integer
          - "null"
          format: int32
        upDown:
          type:
          - string
          - "null"
        contentBlockIds:
          type:
          - array
          - "null"
          items:
            type: integer
            format: int64
    MoveContentBlockResponse:
      type: object
      required:
      - data
      - errors
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/MoveContentBlockData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    MoveContentBlock_ContentBlock:
      type: object
      properties:
        id:
          type: integer
          format: int64
        displayOrderNumber:
          type: integer
          format: int32
    MoveContentBlock_ContentBlocks:
      type: object
      required:
      - contentBlocks
      properties:
        contentBlocks:
          type: array
          items:
            $ref: "#/components/schemas/MoveContentBlock_ContentBlock"
    MoveSnsLinks_Response:
      type: object
      properties:
        success:
          type: boolean
    OnSale:
      type: object
      properties:
        discountRate:
          type: number
          format: float
        startAt:
          type:
          - string
          - "null"
        endAt:
          type:
          - string
          - "null"
    OnSaleData:
      type: object
      properties:
        discount_rate:
          type: number
          format: float
        start_at:
          type:
          - string
          - "null"
        end_at:
          type:
          - string
          - "null"
    OperationType:
      type: string
      enum:
      - INSERT
      - UPDATE
    OptionData:
      type: object
      properties:
        is_single_sales:
          type: boolean
        qty_total:
          type:
          - integer
          - "null"
          format: int32
        qty_per_user:
          type:
          - integer
          - "null"
          format: int32
        remaining_amount:
          type:
          - integer
          - "null"
          format: int32
        for_sale:
          anyOf:
          - $ref: "#/components/schemas/ForSaleData"
          - type: "null"
        password:
          type:
          - string
          - "null"
        on_sale:
          anyOf:
          - $ref: "#/components/schemas/OnSaleData"
          - type: "null"
    Order:
      type: object
      required:
      - created_at
      - updated_at
      - purchaserUid
      - shop
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        purchaserUid:
          type: string
          pattern: \S
        shop:
          $ref: "#/components/schemas/Shop"
        transactionId:
          type:
          - integer
          - "null"
          format: int64
        checkoutId:
          type:
          - integer
          - "null"
          format: int64
    OrderResult:
      type: object
      required:
      - order
      properties:
        order:
          $ref: "#/components/schemas/Order"
        purchased_items:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/PurchasedItem"
        convenience_checkout:
          anyOf:
          - $ref: "#/components/schemas/Output2"
          - type: "null"
        redirect_url:
          type:
          - string
          - "null"
    OrderedItem:
      type: object
      required:
      - name
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        itemType:
          type: integer
          format: int32
        price:
          type: integer
          format: int32
        marginRate:
          type: number
          format: float
        quantity:
          type: integer
          format: int32
    Output:
      type: object
      properties:
        item_id:
          type: integer
          format: int64
        remaining_pull_count:
          type: integer
          format: int32
    Output1:
      type: object
      properties:
        item_id:
          type: integer
          format: int64
        is_acquired:
          type: boolean
        rank:
          type:
          - integer
          - "null"
          format: int32
    Output2:
      type: object
      required:
      - convenience
      - confNo
      - receiptNo
      - paymentTerm
      - status
      properties:
        checkoutId:
          type: integer
          format: int64
        convenience:
          type: string
        confNo:
          type: string
        receiptNo:
          type: string
        paymentTerm:
          type: string
        receiptUrl:
          type:
          - string
          - "null"
        status:
          $ref: "#/components/schemas/CheckoutStatus"
    Profile:
      type: object
      required:
      - created_at
      - updated_at
      - bio
      - snsLinkColor
      - officialFlg
      - snsLinks
      - snsLinkDisplays
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        user:
          anyOf:
          - $ref: "#/components/schemas/User"
          - type: "null"
        bio:
          type:
          - string
          - "null"
        headerImage:
          type:
          - string
          - "null"
          maxLength: 255
        snsLinkColor:
          type:
          - string
          - "null"
          maxLength: 255
        officialFlg:
          type:
          - boolean
          - "null"
        cover:
          anyOf:
          - $ref: "#/components/schemas/ProfileCover"
          - type: "null"
        themeColor:
          anyOf:
          - $ref: "#/components/schemas/ProfileThemeColor"
          - type: "null"
        snsLinks:
          type: array
          uniqueItems: true
          items:
            $ref: "#/components/schemas/SnsLink"
        snsLinkDisplays:
          type: array
          uniqueItems: true
          items:
            $ref: "#/components/schemas/SnsLinkDisplay"
    ProfileCover:
      type: object
      required:
      - created_at
      - updated_at
      - brightness
      - coverVisibility
      - coverImage
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        profile:
          anyOf:
          - $ref: "#/components/schemas/Profile"
          - type: "null"
        brightness:
          type:
          - string
          - "null"
          maxLength: 255
        coverVisibility:
          type:
          - boolean
          - "null"
        coverImage:
          type: array
          uniqueItems: true
          items:
            $ref: "#/components/schemas/ProfileCoverImage"
        displayableCoverImage:
          anyOf:
          - $ref: "#/components/schemas/DisplayableCoverImage"
          - type: "null"
    ProfileCoverImage:
      type: object
      required:
      - created_at
      - updated_at
      - profileCover
      - resource
      - resourceType
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        profileCover:
          anyOf:
          - $ref: "#/components/schemas/ProfileCover"
          - type: "null"
        resource:
          type:
          - string
          - "null"
          maxLength: 255
        resourceType:
          type:
          - string
          - "null"
          maxLength: 255
        displayableCoverImage:
          anyOf:
          - $ref: "#/components/schemas/DisplayableCoverImage"
          - type: "null"
    ProfileThemeColor:
      type: object
      required:
      - created_at
      - updated_at
      - themeColorId
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        profile:
          anyOf:
          - $ref: "#/components/schemas/Profile"
          - type: "null"
        themeColorId:
          type: integer
          format: int64
        customColor:
          type:
          - string
          - "null"
          maxLength: 255
    ProxyAccessTokenData:
      type: object
      required:
      - proxyAccessToken
      properties:
        proxyAccessToken:
          type: string
    ProxyAccessTokenResponse:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/ProxyAccessTokenData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
    PullGachaRequest:
      type: object
      properties:
        itemId:
          type: integer
          format: int64
    PurchaseHistory:
      type: object
      required:
      - purchaseDate
      - purchaserName
      - itemNames
      properties:
        purchaseDate:
          type: string
        purchaserName:
          type: string
        itemNames:
          type: array
          items:
            type: string
        sellerSalesAmount:
          type: integer
          format: int32
        quantity:
          type: integer
          format: int32
        paymentType:
          type:
          - string
          - "null"
    PurchaseItemData:
      type: object
      required:
      - purchasedItem
      properties:
        purchasedItem:
          $ref: "#/components/schemas/PurchasedItemDetail"
    PurchaseItemResponseBody:
      type: object
      required:
      - data
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/PurchaseItemData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    PurchasedItem:
      type: object
      required:
      - created_at
      - updated_at
      - order
      - purchaserUid
      - item
      - status
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        order:
          $ref: "#/components/schemas/Order"
        purchaserUid:
          type: string
          pattern: \S
        item:
          type: object
          required:
          - created_at
          - updated_at
          - name
          - thumbnailUri
          - thumbnailFrom
          - thumbnailBlurLevel
          - thumbnailWatermarkLevel
          - price
          - fileType
          - available
          - marginRate
          - sortOrder
          - itemType
          properties:
            id:
              type:
              - integer
              - "null"
              format: int64
            created_at:
              anyOf:
              - $ref: "#/components/schemas/Instant"
              - type: "null"
            updated_at:
              anyOf:
              - $ref: "#/components/schemas/Instant"
              - type: "null"
            name:
              type: string
              minLength: 1
              maxLength: 100
              pattern: \S
            description:
              type:
              - string
              - "null"
              maxLength: 800
            thumbnailUri:
              type: string
              pattern: \S
            thumbnailFrom:
              type: integer
              format: int32
              maximum: 1
              minimum: 0
            thumbnailBlurLevel:
              type: integer
              format: int32
              maximum: 2
              minimum: 0
            thumbnailWatermarkLevel:
              type: integer
              format: int32
              maximum: 2
              minimum: 0
            price:
              type: integer
              format: int32
              maximum: 1000000
              minimum: 100
            fileType:
              type: integer
              format: int32
            available:
              type: boolean
            marginRate:
              type: number
              format: float
            sortOrder:
              type: integer
              format: int32
            itemType:
              $ref: "#/components/schemas/ItemType"
            digital:
              type: boolean
        itemFile:
          type:
          - object
          - "null"
          required:
          - created_at
          - updated_at
          - name
          - objectUri
          - fileType
          - itemThumbnailSelected
          - sortOrder
          properties:
            id:
              type:
              - integer
              - "null"
              format: int64
            created_at:
              anyOf:
              - $ref: "#/components/schemas/Instant"
              - type: "null"
            updated_at:
              anyOf:
              - $ref: "#/components/schemas/Instant"
              - type: "null"
            name:
              type: string
              maxLength: 30
              pattern: \S
            objectUri:
              type:
              - string
              - "null"
              pattern: \S
            thumbnailUri:
              type:
              - string
              - "null"
            maskedThumbnailUri:
              type:
              - string
              - "null"
            watermarkThumbnailUri:
              type:
              - string
              - "null"
            price:
              type:
              - integer
              - "null"
              format: int32
              maximum: 1000000
              minimum: 0
            fileType:
              type: string
              pattern: \S
            size:
              type: number
              format: float
            duration:
              type: integer
              format: int32
              minimum: 0
            itemThumbnailSelected:
              type: boolean
            sortOrder:
              type: integer
              format: int32
        price:
          type: integer
          format: int32
          maximum: 1000000
          minimum: 100
        quantity:
          type: integer
          format: int32
          minimum: 1
        purchaserComment:
          type:
          - string
          - "null"
        status:
          type: string
          pattern: \S
        purchasedAt:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
    PurchasedItemCheckout:
      type: object
      properties:
        payment_type:
          type:
          - string
          - "null"
        convenience:
          type:
          - string
          - "null"
        conf_no:
          type:
          - string
          - "null"
        receipt_no:
          type:
          - string
          - "null"
        payment_term:
          type:
          - string
          - "null"
        receipt_url:
          type:
          - string
          - "null"
        status:
          type:
          - string
          - "null"
        total:
          type:
          - integer
          - "null"
          format: int32
        tip_amount:
          type:
          - integer
          - "null"
          format: int32
        fee:
          type:
          - integer
          - "null"
          format: int32
        delivery_fee:
          type:
          - integer
          - "null"
          format: int32
    PurchasedItemDetail:
      type: object
      required:
      - purchased_at
      - order
      properties:
        id:
          type: integer
          format: int64
        item_id:
          type: integer
          format: int64
        purchased_at:
          type: string
        order:
          $ref: "#/components/schemas/PurchasedItemOrder"
        checkout:
          anyOf:
          - $ref: "#/components/schemas/PurchasedItemCheckout"
          - type: "null"
        purchaser_comment:
          type:
          - string
          - "null"
    PurchasedItemOrder:
      type: object
      required:
      - order_number
      - items
      - ordered_at
      properties:
        id:
          type: integer
          format: int64
        order_number:
          type: string
        items:
          type: array
          items:
            $ref: "#/components/schemas/OrderedItem"
        ordered_at:
          type: string
    PurchasedItemReceivedFile:
      type: object
      required:
      - title
      properties:
        id:
          type: integer
          format: int64
        title:
          type: string
        thumbnail:
          type:
          - string
          - "null"
        award_type:
          type: integer
          format: int32
        count:
          type: integer
          format: int32
    PurchasedItemReceivedFilesData:
      type: object
      required:
      - files
      properties:
        files:
          type: array
          items:
            $ref: "#/components/schemas/PurchasedItemReceivedFile"
    PurchasedItemReceivedFilesResponseBody:
      type: object
      required:
      - data
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/PurchasedItemReceivedFilesData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    PurposeAndUserAttributesRequestBody:
      type: object
      required:
      - userAttributes
      properties:
        userAttributes:
          $ref: "#/components/schemas/UserAttributes"
    RankingEvent:
      type: object
      required:
      - created_at
      - updated_at
      - eventIdentity
      - name
      - description
      - imageUrl
      - applyStartAt
      - applyEndAt
      - startAt
      - endAt
      - calculatedAt
      - archivedAt
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        eventIdentity:
          type:
          - string
          - "null"
          maxLength: 255
        name:
          type:
          - string
          - "null"
          maxLength: 255
        description:
          type:
          - string
          - "null"
        imageUrl:
          type:
          - string
          - "null"
          maxLength: 255
        baseColor:
          type:
          - string
          - "null"
          maxLength: 255
        addInfos:
          type:
          - string
          - "null"
          maxLength: 255
        judgeX:
          type:
          - string
          - "null"
          maxLength: 255
        judgeInstagram:
          type:
          - string
          - "null"
          maxLength: 255
        shareHashTags:
          type:
          - string
          - "null"
          maxLength: 255
        results:
          type:
          - string
          - "null"
          maxLength: 255
        applyStartAt:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        applyEndAt:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        startAt:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        endAt:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        calculatedAt:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        archivedAt:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
    RankingEventInfoResponseBody:
      type: object
      required:
      - data
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/EventData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    RankingEventWithBoost:
      type: object
      properties:
        rankingEvent:
          anyOf:
          - $ref: "#/components/schemas/RankingEvent"
          - type: "null"
        boost:
          anyOf:
          - $ref: "#/components/schemas/RankingYellBoost"
          - type: "null"
    RankingYellBoost:
      type: object
      required:
      - created_at
      - updated_at
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        startAt:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        endAt:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        boostRatio:
          type:
          - number
          - "null"
    RegisterCardRequest:
      type: object
      required:
      - cardName
      - token
      properties:
        cardName:
          type: string
        token:
          type: string
    SaveFanmeCustomerRequest:
      type: object
      required:
      - firstName
      - lastName
      - firstNameKana
      - lastNameKana
      - postalCode
      - prefecture
      - city
      - street
      - phoneNumber
      properties:
        firstName:
          type: string
        lastName:
          type: string
        firstNameKana:
          type: string
        lastNameKana:
          type: string
        postalCode:
          type: string
        prefecture:
          type: string
        city:
          type: string
        street:
          type: string
        building:
          type:
          - string
          - "null"
        phoneNumber:
          type: string
    SendEmailRequest:
      type: object
      properties:
        transactionId:
          type: integer
          format: int64
    Shop:
      type: object
      required:
      - created_at
      - updated_at
      - name
      - tenant
      - creatorUid
      - message
      - marginRate
      - tipMarginRate
      - isOpen
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        name:
          type: string
          maxLength: 50
        tenant:
          type: string
        creatorUid:
          type:
          - string
          - "null"
          maxLength: 50
        description:
          type:
          - string
          - "null"
          maxLength: 500
        headerImageUri:
          type:
          - string
          - "null"
        message:
          type: string
          maxLength: 100
          pattern: \S
        marginRate:
          type: number
          format: float
          minimum: 0
        tipMarginRate:
          type: number
          format: float
          minimum: 0
        isOpen:
          type: boolean
        open:
          type: boolean
    ShopData:
      type: object
      required:
      - shop
      properties:
        shop:
          $ref: "#/components/schemas/ShopForGetShop"
    ShopForGetShop:
      type: object
      required:
      - tenant
      - creator_name
      - creator_icon_uri
      - creator_uid
      - creator_account_identity
      - name
      - item_type_margin_rate
      - limitation
      properties:
        id:
          type: integer
          format: int64
        tenant:
          type: string
        creator_name:
          type: string
        creator_icon_uri:
          type: string
        creator_uid:
          type: string
        creator_account_identity:
          type: string
        name:
          type: string
        description:
          type:
          - string
          - "null"
        header_image_uri:
          type:
          - string
          - "null"
        message:
          type:
          - string
          - "null"
        item_type_margin_rate:
          type: object
          additionalProperties:
            type: number
            format: float
        is_open:
          type: boolean
        limitation:
          $ref: "#/components/schemas/ShopLimitation"
        open:
          type: boolean
    ShopItemTypeMarginRateParam:
      type: object
      required:
      - itemType
      - marginRate
      properties:
        itemType:
          type: string
        marginRate:
          type: number
          format: float
          maximum: 1.0
          minimum: 0.0
    ShopItemTypeMarginRateResult:
      type: object
      required:
      - itemType
      properties:
        shopId:
          type: integer
          format: int64
        itemType:
          type: string
        marginRate:
          type: number
          format: float
    ShopLimitation:
      type: object
      required:
      - created_at
      - updated_at
      - file_capacity
      - file_quantity
      - is_cheki_exhibitable
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        file_capacity:
          type: integer
          format: int32
        file_quantity:
          type: integer
          format: int32
        is_cheki_exhibitable:
          type: boolean
        cheki_exhibitable:
          type: boolean
    ShopResponseBody:
      type: object
      required:
      - data
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/ShopData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    SingleOrderData:
      type: object
      required:
      - order
      properties:
        order:
          $ref: "#/components/schemas/OrderResult"
    SingleOrderResponseBody:
      type: object
      required:
      - data
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/SingleOrderData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    SnsLink:
      type: object
      required:
      - created_at
      - updated_at
      - profile
      - type
      - snsAccountId
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        profile:
          anyOf:
          - $ref: "#/components/schemas/Profile"
          - type: "null"
        type:
          type:
          - string
          - "null"
          maxLength: 255
        snsAccountId:
          type:
          - string
          - "null"
          maxLength: 255
        snsLinkDisplay:
          anyOf:
          - $ref: "#/components/schemas/SnsLinkDisplay"
          - type: "null"
    SnsLinkDisplay:
      type: object
      required:
      - created_at
      - updated_at
      - profile
      - snsLink
      - displayOrderNumber
      - displayable
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        profile:
          anyOf:
          - $ref: "#/components/schemas/Profile"
          - type: "null"
        snsLink:
          anyOf:
          - $ref: "#/components/schemas/SnsLink"
          - type: "null"
        displayOrderNumber:
          type:
          - integer
          - "null"
          format: int32
        displayable:
          type:
          - boolean
          - "null"
    SortItem:
      type: object
      properties:
        id:
          type: integer
          format: int64
        sortOrder:
          type: integer
          format: int32
    SortItemsRequest:
      type: object
      required:
      - items
      properties:
        items:
          type: array
          items:
            $ref: "#/components/schemas/SortItem"
    SuggestAddressData:
      type: object
      required:
      - suggestedAddress
      properties:
        suggestedAddress:
          $ref: "#/components/schemas/SuggestedAddressEntity"
    SuggestAddressResponseBody:
      type: object
      required:
      - data
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/SuggestAddressData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    SuggestedAddressEntity:
      type: object
      required:
      - prefecture
      - city
      - street
      properties:
        prefecture:
          type: string
        city:
          type: string
        street:
          type: string
    TipLimitData:
      type: object
      required:
      - tip_limit
      properties:
        tip_limit:
          $ref: "#/components/schemas/TipUpperLimit"
    TipLimitResponseBody:
      type: object
      required:
      - data
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/TipLimitData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    TipUpperLimit:
      type: object
      properties:
        amount:
          type: integer
          format: int32
    ToggleContentBlockDisplayableData:
      type: object
      required:
      - contentBlock
      properties:
        contentBlock:
          $ref: "#/components/schemas/ToggleContentBlockDisplayable_ContentBlock"
    ToggleContentBlockDisplayableRequest:
      type: object
      properties:
        displayOrderNum:
          type:
          - integer
          - "null"
          format: int32
        contentBlockId:
          type:
          - integer
          - "null"
          format: int64
        displayable:
          type:
          - boolean
          - "null"
    ToggleContentBlockDisplayableResponse:
      type: object
      required:
      - data
      - errors
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/ToggleContentBlockDisplayableData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    ToggleContentBlockDisplayable_ContentBlock:
      type: object
      properties:
        id:
          type: integer
          format: int64
        displayable:
          type:
          - boolean
          - "null"
    TotalSales:
      type: object
      properties:
        thisMonth:
          anyOf:
          - $ref: "#/components/schemas/MonthlySales"
          - type: "null"
        lastMonth:
          anyOf:
          - $ref: "#/components/schemas/MonthlySales"
          - type: "null"
    UpdateCardRequest:
      type: object
      required:
      - cardName
      - cardHolderName
      - expire
      properties:
        cardSequence:
          type: integer
          format: int32
        cardName:
          type: string
        cardHolderName:
          type: string
        expire:
          type: string
    UpdateCartItemRequest:
      type: object
      properties:
        quantity:
          type:
          - integer
          - "null"
          format: int32
        purchaserComment:
          type:
          - string
          - "null"
    UpdateContentBlockDetailData:
      type: object
      required:
      - contentBlockDetail
      properties:
        contentBlockDetail:
          $ref: "#/components/schemas/ContentBlockDetail"
    UpdateContentBlockDetailRequest:
      type: object
      required:
      - contentBlockDetailId
      properties:
        contentBlockDetailId:
          type: integer
          format: int64
        title:
          type:
          - string
          - "null"
          maxLength: 50
        description:
          type:
          - string
          - "null"
          maxLength: 500
        appDescription:
          type:
          - string
          - "null"
          maxLength: 500
        url:
          type:
          - string
          - "null"
          pattern: ""
        iconUrl:
          type:
          - string
          - "null"
          pattern: ""
        fileUpload:
          anyOf:
          - $ref: "#/components/schemas/FileUpload"
          - type: "null"
    UpdateContentBlockDetailRequest1:
      type: object
      properties:
        contentBlockDetailId:
          type: integer
          format: int64
        title:
          type:
          - string
          - "null"
        description:
          type:
          - string
          - "null"
        appDescription:
          type:
          - string
          - "null"
        url:
          type:
          - string
          - "null"
        iconUrl:
          type:
          - string
          - "null"
    UpdateContentBlockDetailResponse:
      type: object
      required:
      - data
      - errors
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/UpdateContentBlockDetailData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    UpdateGachaItemRequest:
      type: object
      required:
      - name
      - thumbnailUri
      - itemFiles
      properties:
        name:
          type: string
        description:
          type:
          - string
          - "null"
        thumbnailUri:
          type: string
        thumbnailFrom:
          type: integer
          format: int32
        thumbnailBlurLevel:
          type: integer
          format: int32
        thumbnailWatermarkLevel:
          type: integer
          format: int32
        price:
          type: integer
          format: int32
        available:
          type: boolean
        itemFiles:
          type: array
          items:
            $ref: "#/components/schemas/GachaFileForUpdate"
        samples:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/GachaSampleFileForUpdate"
        benefits:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/GachaBenefitForUpdate"
        tags:
          type:
          - array
          - "null"
          items:
            type: string
        itemOption:
          anyOf:
          - $ref: "#/components/schemas/ItemOption1"
          - type: "null"
    UpdateOrderRequest:
      type: object
      required:
      - status
      properties:
        transactionId:
          type:
          - integer
          - "null"
          format: int64
        checkoutId:
          type: integer
          format: int64
        status:
          type: string
    UpdateProfileCoverData:
      type: object
      required:
      - profileCover
      properties:
        profileCover:
          $ref: "#/components/schemas/ProfileCover"
    UpdateProfileCoverImageData:
      type: object
      required:
      - profileCoverImage
      properties:
        profileCoverImage:
          $ref: "#/components/schemas/ProfileCoverImage"
    UpdateProfileCoverImageRequest:
      type: object
      required:
      - coverImage
      - resourceType
      properties:
        coverImage:
          type: string
        resourceType:
          type: string
    UpdateProfileCoverImageResponse:
      type: object
      required:
      - data
      - errors
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/UpdateProfileCoverImageData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    UpdateProfileCoverRequest:
      type: object
      properties:
        coverImageVisibility:
          type:
          - boolean
          - "null"
        brightness:
          type:
          - string
          - "null"
    UpdateProfileCoverResponse:
      type: object
      required:
      - data
      - errors
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/UpdateProfileCoverData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    UpdateProfileData:
      type: object
      required:
      - profile
      properties:
        profile:
          $ref: "#/components/schemas/Profile"
    UpdateProfileRequest:
      type: object
      properties:
        bio:
          type:
          - string
          - "null"
        headerImage:
          type:
          - string
          - "null"
        snsLinkColor:
          type:
          - string
          - "null"
    UpdateProfileResponse:
      type: object
      required:
      - data
      - errors
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/UpdateProfileData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    UpdateShopItemTypeMarginRatesRequest:
      type: object
      required:
      - marginRates
      properties:
        marginRates:
          type: array
          items:
            $ref: "#/components/schemas/ShopItemTypeMarginRateParam"
    UpdateShopLimitationRequest:
      type: object
      required:
      - fileCapacity
      - fileQuantity
      - isChekiExhibitable
      properties:
        fileCapacity:
          type: integer
          format: int32
        fileQuantity:
          type: integer
          format: int32
        isChekiExhibitable:
          type: boolean
    UpdateShopLimitationResponse:
      type: object
      required:
      - data
      - errors
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/ShopLimitation"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    UpdateShopRequest:
      type: object
      required:
      - name
      properties:
        name:
          type: string
        description:
          type:
          - string
          - "null"
        headerImageUri:
          type:
          - string
          - "null"
        message:
          type:
          - string
          - "null"
    UpdateStatusRequest:
      type: object
      properties:
        status:
          type: integer
          format: int32
        comment:
          type:
          - string
          - "null"
    UpsertUserTutorialRequest:
      type: object
      properties:
        displayFlg:
          type: boolean
    User:
      type: object
      required:
      - created_at
      - updated_at
      - name
      - gender
      - birthday
      - birthdayConfirmed
      - accountIdentity
      - public
      - filledProfile
      - purpose
      - iconUrl
      - displayName
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        icon:
          type:
          - string
          - "null"
          maxLength: 255
        name:
          type:
          - string
          - "null"
          maxLength: 255
        gender:
          type:
          - string
          - "null"
          maxLength: 255
        birthday:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        birthdayConfirmed:
          type:
          - boolean
          - "null"
        accountIdentity:
          type:
          - string
          - "null"
          maxLength: 255
        public:
          type:
          - boolean
          - "null"
        allowPublicSharing:
          type:
          - boolean
          - "null"
        uid:
          type:
          - string
          - "null"
          maxLength: 255
        deletedAt:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        filledProfile:
          type:
          - boolean
          - "null"
        purpose:
          type:
          - integer
          - "null"
          format: int32
        userAttributes:
          type:
          - string
          - "null"
        isBirthdayWeek:
          type:
          - integer
          - "null"
          format: int32
        birthdayWeek:
          type: integer
          format: int32
        iconUrl:
          type: string
        displayName:
          type: string
    UserAttributes:
      type: object
      properties:
        purpose:
          type:
          - string
          - "null"
          description: 利用目的
          example: user_influencer
        bankAccountType:
          type:
          - string
          - "null"
          description: 受取口座
          example: corporate_account
    UserConsent:
      type: object
      required:
      - created_at
      - updated_at
      - consent
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        user:
          anyOf:
          - $ref: "#/components/schemas/User"
          - type: "null"
        consent:
          anyOf:
          - $ref: "#/components/schemas/Consent1"
          - type: "null"
    UserCurrentEntryCampaignsData:
      type: object
      required:
      - campaigns
      properties:
        campaigns:
          type: array
          items:
            $ref: "#/components/schemas/Campaign"
    UserCurrentEntryCampaignsResponseBody:
      type: object
      required:
      - data
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/UserCurrentEntryCampaignsData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    UserMonthlySales:
      type: object
      required:
      - yearMonth
      properties:
        yearMonth:
          type: string
        salesAmount:
          type: integer
          format: int32
        purchaserCount:
          type: integer
          format: int32
        purchaseCount:
          type: integer
          format: int32
        growthRate:
          type:
          - number
          - "null"
          format: float
    UserProfileResponse:
      type: object
      required:
      - profile
      properties:
        profile:
          $ref: "#/components/schemas/GetProfile_Profile"
    UserResponse:
      type: object
      required:
      - userResponse
      properties:
        userResponse:
          $ref: "#/components/schemas/ConsoleGetUserResponse"
    UserSales:
      type: object
      required:
      - userUid
      - userName
      properties:
        userUid:
          type: string
        userName:
          type: string
        thisMonth:
          anyOf:
          - $ref: "#/components/schemas/MonthlySales"
          - type: "null"
        lastMonth:
          anyOf:
          - $ref: "#/components/schemas/MonthlySales"
          - type: "null"
    UserTutorialData:
      type: object
      required:
      - userTutorial
      properties:
        userTutorial:
          $ref: "#/components/schemas/GetUserTutorial_UserTutorial"
    UserTutorialResponseBody:
      type: object
      required:
      - data
      properties:
        metadata:
          type:
          - object
          - "null"
          additionalProperties: {}
        data:
          anyOf:
          - $ref: "#/components/schemas/UserTutorialData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    UsersByPartialAccountIdentityResponse:
      type: object
      required:
      - usersByPartialAccountIdentityResponse
      properties:
        usersByPartialAccountIdentityResponse:
          type: array
          items:
            $ref: "#/components/schemas/User"
  securitySchemes:
    SecurityScheme:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: Authentication
paths:
  /address/suggestion/postal-code/{postal-code}:
    get:
      operationId: suggestAddress
      tags:
      - FANME
      parameters:
      - name: postal-code
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SuggestAddressResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Suggest Address
      security:
      - SecurityScheme:
        - LoginUser
  /auth/fanme:
    get:
      summary: Fanme OAuth認証開始
      description: OAuth認証を開始し、認証プロバイダへのリダイレクト先URLを取得するエンドポイント
      operationId: fanme
      tags:
      - FANME
      parameters:
      - description: 処理種別
        name: proc
        in: query
        schema:
          type:
          - string
          - "null"
      - description: 戻り先URL
        name: return_url
        in: query
        schema:
          type:
          - string
          - "null"
      responses:
        "200":
          description: 認証プロバイダへのリダイレクトURLを返す
  /auth/fanme/callback:
    get:
      summary: OAuthコールバック処理
      description: OAuthプロバイダからのコールバックを受け取り、認証処理を実行する
      operationId: callback
      tags:
      - FANME
      parameters:
      - description: 認証コード
        name: code
        in: query
        schema:
          type: string
        required: true
      - description: 戻り先URL
        name: return_url
        in: query
        schema:
          type: string
        required: true
      - description: 状態値
        name: state
        in: query
        schema:
          type: string
        required: true
      - description: ログイン失敗回数
        name: login_failure_count
        in: cookie
        schema:
          type:
          - string
          - "null"
      - description: Cookie内のNonce
        name: nonce
        in: cookie
        schema:
          type:
          - string
          - "null"
      - description: Cookie内の状態値
        name: state
        in: cookie
        schema:
          type:
          - string
          - "null"
      responses:
        "200":
          description: 認証成功時のレスポンスを返す
        "500":
          description: 内部サーバエラーが発生した場合のレスポンスを返す
  /cards:
    post:
      operationId: registerCard
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RegisterCardRequest"
        required: true
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
      summary: Register Card
      tags:
      - Card Endpoints
    get:
      operationId: fetchCard
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Fetch Card
      tags:
      - Card Endpoints
      security:
      - SecurityScheme:
        - LoginUser
  /cards/update:
    put:
      operationId: updateCard
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateCardRequest"
        required: true
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
      summary: Update Card
      tags:
      - Card Endpoints
  /cards/{card_sequence}:
    delete:
      operationId: deleteCard
      parameters:
      - name: card_sequence
        in: path
        required: true
        schema:
          type: integer
          format: int32
      responses:
        "200":
          description: OK
      summary: Delete Card
      tags:
      - Card Endpoints
  /console/agencies:
    get:
      operationId: getAgencies
      tags:
      - CONSOLE
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAgenciesResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Agencies
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
        - AGENT
  /console/agencies/{agency-id}/sales:
    get:
      operationId: getAgencySales
      tags:
      - CONSOLE
      parameters:
      - name: agency-id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAgencySalesResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Agency Sales
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
        - AGENT
  /console/agencies/{agency-id}/sales/monthly:
    get:
      operationId: getAgencyMonthlySales
      tags:
      - CONSOLE
      parameters:
      - name: agency-id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      - name: from
        in: query
        schema:
          type:
          - string
          - "null"
      - name: to
        in: query
        schema:
          type:
          - string
          - "null"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAgencyMonthlySalesResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Agency Monthly Sales
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
        - AGENT
  /console/agencies/{agency-id}/users:
    get:
      operationId: getAgencyUsers
      tags:
      - CONSOLE
      parameters:
      - name: agency-id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAgencyUsersResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Agency Users
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
        - AGENT
  /console/audit-groups:
    get:
      operationId: getAuditGroups
      tags:
      - CONSOLE
      parameters:
      - name: $count
        in: query
        schema:
          type:
          - boolean
          - "null"
      - name: $skip
        in: query
        schema:
          type:
          - integer
          - "null"
          format: int32
      - name: $top
        in: query
        schema:
          type:
          - integer
          - "null"
          format: int32
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AuditGroupsResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Audit Groups
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
  /console/audit-groups/{audit-group-id}/audit-objects:
    get:
      operationId: getAuditObjects
      tags:
      - CONSOLE
      parameters:
      - name: audit-group-id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AuditObjectsResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Audit Objects
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
  /console/audit-groups/{audit-group-id}/status:
    put:
      operationId: updateAuditStatus
      tags:
      - CONSOLE
      parameters:
      - name: audit-group-id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateStatusRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AuditStatusResponseBody"
        "400":
          description: Bad Request
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Update Audit Status
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
  /console/commission-control/creator/{account-identity}/items:
    put:
      operationId: updateItemMarginRates
      tags:
      - CONSOLE
      parameters:
      - name: account-identity
        in: path
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: "#/components/schemas/ItemMarginRateItem"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BaseResponseBodyListItemMarginRateResult"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Update Item Margin Rates
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
  /console/commission-control/creator/{account-identity}/shop/items:
    get:
      operationId: getAllShopItems
      tags:
      - CONSOLE
      parameters:
      - name: account-identity
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAllShopItemsResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get All Shop Items
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
  /console/commission-control/creator/{account-identity}/shop/{shop-id}:
    put:
      operationId: updateShopItemTypeMarginRates
      tags:
      - CONSOLE
      parameters:
      - name: account-identity
        in: path
        required: true
        schema:
          type: string
      - name: shop-id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateShopItemTypeMarginRatesRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BaseResponseBodyListShopItemTypeMarginRateResult"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Update Shop Item Type Margin Rates
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
  /console/console-users:
    get:
      operationId: getConsoleUsers
      tags:
      - CONSOLE
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ConsoleUsersResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Console Users
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
  /console/creators/{creator-account-identity}/shop/items/{item-id}:
    put:
      operationId: updateItem
      tags:
      - CONSOLE
      parameters:
      - name: creator-account-identity
        in: path
        required: true
        schema:
          type: string
      - name: item-id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOrUpdateItemRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BaseResponseBodyItem"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Update Item
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
        - AGENT
  /console/users:
    get:
      operationId: getUsersByPartialAccountIdentity
      tags:
      - CONSOLE
      parameters:
      - name: partial-account-identity
        in: query
        schema:
          type:
          - string
          - "null"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BaseResponseBodyUsersByPartialAccountIdentityResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Users By Partial Account Identity
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
  /console/users/{account-identity}/content-blocks:
    put:
      operationId: updateContentBlockDetail
      tags:
      - CONSOLE
      parameters:
      - name: account-identity
        in: path
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateContentBlockDetailRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BaseResponseBodyContentBlockDetailResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Update Content Block Detail
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
        - AGENT
    get:
      operationId: getUserContentBlocks
      tags:
      - CONSOLE
      parameters:
      - name: account-identity
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BaseResponseBodyContentBlocks"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get User Content Blocks
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
        - AGENT
    post:
      operationId: createContentBlock
      tags:
      - CONSOLE
      parameters:
      - name: account-identity
        in: path
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateContentWithDetailRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BaseResponseBodyContentBlockResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Create Content Block
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
        - AGENT
  /console/users/{account-identity}/profile:
    get:
      operationId: getProfile
      tags:
      - CONSOLE
      parameters:
      - name: account-identity
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BaseResponseBodyUserProfileResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Profile
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
        - AGENT
  /console/users/{account-identity}/proxy-access-token:
    post:
      operationId: createProxyAccessToken
      tags:
      - CONSOLE
      parameters:
      - name: account-identity
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProxyAccessTokenResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Create Proxy Access Token
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
        - AGENT
        - CREATOR
  /console/users/{account-identity}/sales/monthly:
    get:
      operationId: getUserMonthlySales
      tags:
      - CONSOLE
      parameters:
      - name: account-identity
        in: path
        required: true
        schema:
          type: string
      - name: $count
        in: query
        schema:
          type:
          - boolean
          - "null"
      - name: $skip
        in: query
        schema:
          type:
          - integer
          - "null"
          format: int32
      - name: $top
        in: query
        schema:
          type:
          - integer
          - "null"
          format: int32
      - name: from
        in: query
        schema:
          type:
          - string
          - "null"
      - name: to
        in: query
        schema:
          type:
          - string
          - "null"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetUserMonthlySalesResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get User Monthly Sales
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
        - AGENT
  /console/users/{account-identity}/sales/transactions:
    get:
      operationId: getUserTransaction
      tags:
      - CONSOLE
      parameters:
      - name: account-identity
        in: path
        required: true
        schema:
          type: string
      - name: $count
        in: query
        schema:
          type:
          - boolean
          - "null"
      - name: $skip
        in: query
        schema:
          type:
          - integer
          - "null"
          format: int32
      - name: $top
        in: query
        schema:
          type:
          - integer
          - "null"
          format: int32
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetUserTransactionsResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get User Transaction
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
        - AGENT
  /console/users/{account-identity}/shop-limitation:
    put:
      operationId: updateShopLimitation
      tags:
      - CONSOLE
      parameters:
      - name: account-identity
        in: path
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateShopLimitationRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateShopLimitationResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Update Shop Limitation
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
  /console/users/{id}:
    get:
      operationId: getUser
      tags:
      - CONSOLE
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BaseResponseBodyUserResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get User
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
  /email/payment:
    post:
      operationId: sendPaymentEmail
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SendEmailRequest"
        required: true
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
      summary: Send Payment Email
      tags:
      - Email Endpoint
  /fanme/content_blocks:
    get:
      operationId: getContentBlocks
      tags:
      - FANME
      parameters:
      - name: creator_account_identity
        in: query
        schema:
          type: string
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetContentBlockResponse"
      summary: Get Content Blocks
  /fanme/content_blocks/create:
    post:
      operationId: createContentBlock
      tags:
      - FANME
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateContentBlockRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateContentBlockResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Create Content Block
      security:
      - SecurityScheme:
        - LoginUser
  /fanme/content_blocks/create_with_detail:
    post:
      operationId: createWithDetail
      tags:
      - FANME
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateContentWithDetailRequest1"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Create With Detail
      security:
      - SecurityScheme:
        - LoginUser
  /fanme/content_blocks/current:
    get:
      operationId: getCurrentUserContentBlocks
      tags:
      - FANME
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetContentBlockResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Current User Content Blocks
      security:
      - SecurityScheme:
        - LoginUser
  /fanme/content_blocks/detail:
    put:
      operationId: updateContentBlockDetail
      tags:
      - FANME
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateContentBlockDetailRequest1"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateContentBlockDetailResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Update Content Block Detail
      security:
      - SecurityScheme:
        - LoginUser
  /fanme/content_blocks/detail/move:
    put:
      operationId: moveContentBlockDetail
      tags:
      - FANME
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MoveContentBlockDetailRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MoveContentBlockDetailResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Move Content Block Detail
      security:
      - SecurityScheme:
        - LoginUser
  /fanme/content_blocks/move:
    put:
      operationId: moveContentBlock
      tags:
      - FANME
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MoveContentBlockRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MoveContentBlockResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Move Content Block
      security:
      - SecurityScheme:
        - LoginUser
  /fanme/content_blocks/toggle_displayable:
    put:
      operationId: toggleContentBlockDisplayable
      tags:
      - FANME
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ToggleContentBlockDisplayableRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ToggleContentBlockDisplayableResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Toggle Content Block Displayable
      security:
      - SecurityScheme:
        - LoginUser
  /fanme/content_blocks/{content_block_id}:
    delete:
      operationId: deleteContentBlock
      tags:
      - FANME
      parameters:
      - name: content_block_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DeleteContentBlockResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Delete Content Block
      security:
      - SecurityScheme:
        - LoginUser
  /fanme/fanme-customers:
    post:
      operationId: saveFanmeCustomer
      tags:
      - FANME
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SaveFanmeCustomerRequest"
        required: true
      responses:
        "200":
          description: OK
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Save Fanme Customer
      security:
      - SecurityScheme:
        - LoginUser
    get:
      operationId: getFanmeCustomer
      tags:
      - FANME
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FanmeCustomerResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Fanme Customer
      security:
      - SecurityScheme:
        - LoginUser
  /fanme/profiles/current:
    put:
      operationId: updateProfile
      tags:
      - FANME
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateProfileRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateProfileResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Update Profile
      security:
      - SecurityScheme:
        - LoginUser
    get:
      operationId: getCurrentProfile
      tags:
      - FANME
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetProfileResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Current Profile
      security:
      - SecurityScheme:
        - LoginUser
  /fanme/profiles/current/cover:
    put:
      operationId: updateProfileCover
      tags:
      - FANME
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateProfileCoverRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateProfileCoverResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Update Profile Cover
      security:
      - SecurityScheme:
        - LoginUser
  /fanme/profiles/current/cover/image:
    put:
      operationId: updateProfileCoverImage
      tags:
      - FANME
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateProfileCoverImageRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateProfileCoverImageResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Update Profile Cover Image
      security:
      - SecurityScheme:
        - LoginUser
  /fanme/profiles/{creator_account_identity}:
    get:
      operationId: getProfile
      tags:
      - FANME
      parameters:
      - name: creator_account_identity
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetProfileResponse"
      summary: Get Profile
  /fanme/user-consents:
    get:
      operationId: getConsents
      tags:
      - FANME
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BaseResponseBodyGetConsentsResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Consents
      security:
      - SecurityScheme:
        - LoginUser
    post:
      operationId: createUserConsent
      tags:
      - FANME
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateUserConsentRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BaseResponseBodyCreateUserConsentResponse"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Create User Consent
      security:
      - SecurityScheme:
        - LoginUser
  /fanme/users/current:
    get:
      operationId: getCurrentUser
      tags:
      - FANME
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema: {}
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Current User
      security:
      - SecurityScheme:
        - LoginUser
  /fanme/users/current/entry_campaigns:
    get:
      operationId: getCurrentUserEntryCampaigns
      tags:
      - FANME
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserCurrentEntryCampaignsResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Current User Entry Campaigns
      security:
      - SecurityScheme:
        - LoginUser
  /fanme/users/current/user_tutorials/{flag_key}:
    put:
      operationId: upsertUserTutorial
      tags:
      - FANME
      parameters:
      - name: flag_key
        in: path
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpsertUserTutorialRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserTutorialResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Upsert User Tutorial
      security:
      - SecurityScheme:
        - LoginUser
    get:
      operationId: getUserTutorial
      tags:
      - FANME
      parameters:
      - name: flag_key
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserTutorialResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get User Tutorial
      security:
      - SecurityScheme:
        - LoginUser
  /fanme/users/purpose_and_user_attributes:
    post:
      operationId: updatePurposeAndUserAttributes
      tags:
      - FANME
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PurposeAndUserAttributesRequestBody"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BaseResponseBodyUser"
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Update Purpose And User Attributes
      security:
      - SecurityScheme:
        - LoginUser
  /fanme/users/{user_uuid}:
    get:
      operationId: getUser
      tags:
      - FANME
      parameters:
      - name: user_uuid
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema: {}
      summary: Get User
  /hc:
    get:
      operationId: healthCheck
      responses:
        "200":
          description: OK
      summary: Health Check
      tags:
      - Health Check Endpoints
  /orders:
    put:
      operationId: updateOrder
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateOrderRequest"
        required: true
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
      summary: Update Order
      tags:
      - Order Endpoint
    get:
      operationId: getOrders
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Orders
      tags:
      - Order Endpoint
      security:
      - SecurityScheme:
        - LoginUser
    post:
      operationId: createOrder
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOrderRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Create Order
      tags:
      - Order Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /orders/convenience-fees:
    get:
      operationId: getConvenienceFees
      responses:
        "200":
          description: OK
      summary: Get Convenience Fees
      tags:
      - Order Endpoint
  /orders/finalize-credit-card-3d-secure:
    post:
      operationId: finalizeCreditCard3DSecure
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/FinalizeCreditCard3DSecureRequest"
        required: true
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
      summary: Finalize Credit Card 3 D Secure
      tags:
      - Order Endpoint
  /orders/tip-upper-limit/{creator_account_identity}:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    get:
      operationId: getTipLimit
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TipLimitResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Tip Limit
      tags:
      - Order Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current:
    put:
      operationId: updateShop
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateShopRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Update Shop
      tags:
      - Current User Shop Endpoint
      security:
      - SecurityScheme:
        - LoginUser
    get:
      operationId: getCurrentUserShop
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Shop
      tags:
      - Current User Shop Endpoint
      security:
      - SecurityScheme:
        - LoginUser
    post:
      operationId: createShop
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateShopRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Create Shop
      tags:
      - Current User Shop Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current/files/download-url:
    post:
      operationId: getDownloadUrl
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetDownloadUrlRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DownloadUrlResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Get Download Url
      tags:
      - File Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current/files/presigned-url:
    post:
      operationId: getPreSignedUrl
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetPreSignedUrlRequest"
        required: true
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
      summary: Get Pre Signed Url
      tags:
      - File Endpoint
  /shops/current/files/upload-url:
    post:
      operationId: getUploadUrl
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetUploadUrlRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Get Upload Url
      tags:
      - File Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current/gacha:
    post:
      operationId: createGachaItem
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateGachaItemRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ItemResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Create Gacha Item
      tags:
      - Current User Gacha Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current/gacha/{item_id}:
    put:
      operationId: updateGachaItem
      parameters:
      - name: item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateGachaItemRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ItemResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Update Gacha Item
      tags:
      - Current User Gacha Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current/items:
    get:
      operationId: getCurrentUserItems
      parameters:
      - name: available
        in: query
        schema:
          type:
          - boolean
          - "null"
      - name: tag
        in: query
        schema:
          type:
          - string
          - "null"
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Items
      tags:
      - Current User Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
    post:
      operationId: createItem
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOrUpdateItemRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Create Item
      tags:
      - Current User Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current/items/sort:
    put:
      operationId: sortItems
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SortItemsRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Sort Items
      tags:
      - Current User Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current/items/{item_id}:
    put:
      operationId: updateItem
      parameters:
      - name: item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOrUpdateItemRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Update Item
      tags:
      - Current User Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
    get:
      operationId: getCurrentUserItem
      parameters:
      - name: item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      - name: include_deleted
        in: query
        schema:
          type:
          - string
          - "null"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ItemResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Item
      tags:
      - Current User Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current/purchased-items:
    get:
      operationId: getPurchasedItems
      parameters:
      - name: include_tip
        in: query
        schema:
          type:
          - boolean
          - "null"
      - name: item_types
        in: query
        schema:
          type:
          - array
          - "null"
          items:
            type: integer
            format: int32
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Purchased Items
      tags:
      - Purchased Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current/purchased-items/{purchased_item_id}:
    get:
      operationId: getPurchasedItem
      parameters:
      - name: purchased_item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PurchaseItemResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Purchased Item
      tags:
      - Purchased Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current/sales-history:
    get:
      operationId: getSalesHistories
      responses:
        "200":
          description: OK
      summary: Get Sales Histories
      tags:
      - Sales History Endpoint
  /shops/gacha/pull:
    post:
      operationId: pull
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PullGachaRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GachaPullResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Pull
      tags:
      - Gacha Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/gacha/purchased-items/{purchasedItemId}/received-files:
    get:
      operationId: getPurchasedItemReceivedFiles
      parameters:
      - name: purchasedItemId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PurchasedItemReceivedFilesResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Purchased Item Received Files
      tags:
      - Gacha Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/gacha/{item_id}/complete-badge:
    get:
      operationId: getCompleteBadge
      parameters:
      - name: item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CompleteBadgeResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Complete Badge
      tags:
      - Gacha Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/gacha/{item_id}/complete-badge-ranking:
    get:
      operationId: getCompleteBadgeRanking
      parameters:
      - name: item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BadgeRankingResponseBody"
        "500":
          description: Internal Server Error
      summary: Get Complete Badge Ranking
      tags:
      - Gacha Endpoint
  /shops/gacha/{item_id}/pullable-count:
    get:
      operationId: getPullableGachaCount
      parameters:
      - name: item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GachaPullableCountResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Pullable Gacha Count
      tags:
      - Gacha Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/ranking_event_info/creator/{creator_account_identity}/active:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    get:
      operationId: getCreatorActive
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RankingEventInfoResponseBody"
        "204":
          description: No active event found
        "500":
          description: Internal Server Error
      summary: Get Creator Active
      tags:
      - Ranking Event Info Endpoint
  /shops/{creator_account_identity}:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    get:
      operationId: getShop
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ShopResponseBody"
        "500":
          description: Internal Server Error
      summary: Get Shop
      tags:
      - Shop Endpoint
  /shops/{creator_account_identity}/cart-items:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    get:
      operationId: getCartItems
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CartItemsResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Cart Items
      tags:
      - Cart Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
    post:
      operationId: createCartItem
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateCartItemRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Create Cart Item
      tags:
      - Cart Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/{creator_account_identity}/cart-items/check-price:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    post:
      operationId: checkCartItemPrice
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CheckCartItemPriceRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Check Cart Item Price
      tags:
      - Cart Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/{creator_account_identity}/cart-items/invalid-items:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    delete:
      operationId: getInvalidCartItems
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Invalid Cart Items
      tags:
      - Cart Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/{creator_account_identity}/cart-items/{cart_item_id}:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    put:
      operationId: updateCartItem
      parameters:
      - name: cart_item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateCartItemRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Update Cart Item
      tags:
      - Cart Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
    delete:
      operationId: deleteCartItem
      parameters:
      - name: cart_item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Delete Cart Item
      tags:
      - Cart Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/{creator_account_identity}/items:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    get:
      operationId: getItems
      parameters:
      - name: available
        in: query
        schema:
          type:
          - boolean
          - "null"
      - name: tag
        in: query
        schema:
          type:
          - string
          - "null"
      responses:
        "200":
          description: OK
      summary: Get Items
      tags:
      - Item Endpoint
  /shops/{creator_account_identity}/items/cost/{item_type}:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    get:
      operationId: getCost
      parameters:
      - name: item_type
        in: path
        required: true
        schema:
          type: integer
          format: int32
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ItemCostResponseBody"
        "500":
          description: Internal Server Error
      summary: Get Cost
      tags:
      - Item Endpoint
  /shops/{creator_account_identity}/items/delivery-fee/{item_type}:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    get:
      operationId: getDeliveryFee
      parameters:
      - name: item_type
        in: path
        required: true
        schema:
          type: integer
          format: int32
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DeliveryFeeResponseBody"
        "500":
          description: Internal Server Error
      summary: Get Delivery Fee
      tags:
      - Item Endpoint
  /shops/{creator_account_identity}/items/{item_id}:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    get:
      operationId: getItem
      parameters:
      - name: item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ItemResponseBody"
        "500":
          description: Internal Server Error
      summary: Get Item
      tags:
      - Item Endpoint
  /shops/{creator_account_identity}/items/{item_id}/password-unlock:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    get:
      operationId: getItemPasswordUnlockCache
      parameters:
      - name: item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Item Password Unlock Cache
      tags:
      - Item Password Unlock Cache Endpoint
      security:
      - SecurityScheme:
        - LoginUser
    post:
      operationId: createItemPasswordUnlockCache
      parameters:
      - name: item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateItemPasswordUnlockCacheRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Create Item Password Unlock Cache
      tags:
      - Item Password Unlock Cache Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/{creator_account_identity}/seller-apps/digital-fan-letter-link:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    get:
      operationId: getDigitalFanLetterLink
      responses:
        "200":
          description: OK
      summary: Get Digital Fan Letter Link
      tags:
      - Seller Apps Endpoint
  /single-order:
    post:
      operationId: createOrder
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateSingleOrderRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SingleOrderResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Create Order
      tags:
      - Single Order Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /single-order/finalize-credit-card-3d-secure:
    post:
      operationId: finalizeCreditCard3DSecureSingleOrder
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/FinalizeCreditCard3DSecureRequest"
        required: true
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
      summary: Finalize Credit Card 3 D Secure Single Order
      tags:
      - Single Order Endpoint
  /webhook/gmo/payment/notice:
    post:
      operationId: gmoWebhook
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              required:
              - ShopID
              - ShopPass
              - AccessID
              - AccessPass
              - OrderID
              - Status
              - Amount
              - Tax
              - PayType
              properties:
                ShopID:
                  type: string
                ShopPass:
                  type: string
                AccessID:
                  type: string
                AccessPass:
                  type: string
                OrderID:
                  type: string
                Status:
                  type: string
                Amount:
                  type: string
                Tax:
                  type: string
                PayType:
                  type: string
        required: true
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
      summary: Gmo Webhook
      tags:
      - Webhook Endpoints
tags:
- name: CONSOLE
  description: CONSOLE APIサーバー
- name: FANME
  description: FANME APIサーバー
info:
  title: backend API
  version: 1.0.0-SNAPSHOT
