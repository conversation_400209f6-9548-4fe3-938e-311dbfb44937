package jp.co.torihada.fanme.modules.shop.usecases.shop

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.modules.fanme.Const
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.UserAttributes
import jp.co.torihada.fanme.modules.shop.lib.TestResultLogger
import jp.co.torihada.fanme.modules.shop.usecases.TestResourceLifecycleManager
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class UpdateUserPurposeAndUserAttributesTest {

    @Inject private lateinit var userController: UserController

    val creatorUid = "**********"
    val creatorName = "test_creator"
    val accountIdentity = "@test_account"
    val purpose = 0
    val purposeType = Const.PurposeSurvey.USER_ARTIST.userPurpose
    val bankAccountType = Const.BankAccountType.CORPORATE_ACCOUNT.bankAccountOption
    val purposeCreator = Const.Purpose.Creator.value

    @BeforeEach
    @Transactional
    fun setup() {
        val testUser =
            UserFactory.createTestUser(
                uid = creatorUid,
                name = creatorName,
                accountIdentity = accountIdentity,
                purpose = purpose,
            )
        testUser?.persistAndFlush()
    }

    @Test
    @TestTransaction
    fun `should update purpose and user attributes test`() {

        userController.updatePurposeAndUserAttributes(
            userUuid = creatorUid,
            userAttributes =
                UserAttributes(purpose = purposeType, bankAccountType = bankAccountType),
        )

        val updatedUser = userController.getUser(creatorUid)

        assert(updatedUser.purpose == purposeCreator) {
            "Purpose should be updated to $purposeCreator"
        }

        val objectMapper = jacksonObjectMapper()
        val userAttributes: UserAttributes =
            objectMapper.readValue(updatedUser.userAttributes.toString())

        assert(userAttributes.bankAccountType == bankAccountType) {
            "BankAccountType should be updated to $bankAccountType"
        }
    }
}
