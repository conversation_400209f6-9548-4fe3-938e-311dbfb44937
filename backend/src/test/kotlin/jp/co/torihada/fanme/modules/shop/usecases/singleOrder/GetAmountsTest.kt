package jp.co.torihada.fanme.modules.shop.usecases.singleOrder

import com.github.michaelbull.result.getOrThrow
import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.shop.factories.ItemFactory
import jp.co.torihada.fanme.modules.shop.factories.ShopFactory
import jp.co.torihada.fanme.modules.shop.lib.TestResultLogger
import jp.co.torihada.fanme.modules.shop.models.ItemType
import jp.co.torihada.fanme.modules.shop.usecases.TestResourceLifecycleManager
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class GetAmountsTest {
    @Inject private lateinit var getAmounts: GetAmounts

    @Test
    @TestTransaction
    fun `test get amounts with digital gacha`() {
        val shop = ShopFactory.new()
        shop.persist()
        val item =
            ItemFactory.new(
                shopId = shop.id!!,
                itemType = ItemType.DIGITAL_GACHA,
                price = 1000,
                marginRate = 0.265f,
            )
        item.persist()

        val result =
            getAmounts
                .execute(GetAmounts.Input(itemId = item.id!!, quantity = 1, tip = 0))
                .getOrThrow()

        assertEquals(1000, result.total)
        assertEquals(1000, result.itemAmount)
        assertEquals(265, result.fee)
        assertEquals(735, result.profit)
    }

    @Test
    @TestTransaction
    fun `test get amounts with digital gacha and tip`() {
        val shop = ShopFactory.new()
        shop.persist()
        val item =
            ItemFactory.new(
                shopId = shop.id!!,
                itemType = ItemType.DIGITAL_GACHA,
                price = 1000,
                marginRate = 0.265f,
            )
        item.persist()

        val result =
            getAmounts
                .execute(GetAmounts.Input(itemId = item.id!!, quantity = 1, tip = 200))
                .getOrThrow()

        assertEquals(1200, result.total)
        assertEquals(1000, result.itemAmount)
        assertEquals(285, result.fee)
        assertEquals(915, result.profit)
    }
}
