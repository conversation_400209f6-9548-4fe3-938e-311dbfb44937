package jp.co.torihada.fanme.modules.shop.usecases.item

import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.modules.shop.factories.ItemFactory
import jp.co.torihada.fanme.modules.shop.factories.ShopFactory
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.models.Shop
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class RejectItemTest {

    @Inject private lateinit var rejectItem: RejectItem

    private lateinit var shop: Shop
    private lateinit var item: Item

    @BeforeEach
    @Transactional
    fun setUp() {
        // テスト用のShopを作成
        shop =
            ShopFactory.new(
                creatorUid = "test-creator",
                name = "Test Shop",
                description = "Test Description",
                headerImageUri = "https://example.com/header.jpg",
            )
        shop.persistAndFlush()

        // テスト用のItemを作成
        item =
            ItemFactory.new(
                shopId = shop.id!!,
                name = "Test Item",
                description = "Test Item Description",
                thumbnailUri = "https://example.com/thumbnail.jpg",
                price = 1000,
                available = true,
            )
        item.persistAndFlush()
    }

    @AfterAll
    @Transactional
    fun cleanupAll() {
        Item.deleteAll()
        Shop.deleteAll()
    }

    @Test
    @DisplayName("商品が却下された時にDBに正しく保存されること")
    @Transactional
    fun testItemIsRejectedAndSavedToDatabase() {
        // Given
        val itemId = item.id!!
        val rejectComment = "商品が不適切な内容です"

        // 却下前の状態を確認
        val itemBefore = Item.findById(itemId)
        assertNotNull(itemBefore)
        assertTrue(itemBefore!!.available)

        // When
        val result = rejectItem.execute(itemId, rejectComment)

        // Then
        assertTrue(result.isOk)

        // DBから再取得して状態を確認
        val itemAfter = Item.findById(itemId)
        assertNotNull(itemAfter)
        assertFalse(itemAfter!!.available) // availableがfalseになっていることを確認

        // 他のフィールドは変更されていないことを確認
        assertEquals("Test Item", itemAfter.name)
        assertEquals("Test Item Description", itemAfter.description)
        assertEquals("https://example.com/thumbnail.jpg", itemAfter.thumbnailUri)
        assertEquals(1000, itemAfter.price)
    }

    @Test
    @DisplayName("却下時に通知が送信されること")
    @Transactional
    fun testNotificationIsSentWhenItemIsRejected() {
        // Given
        val itemId = item.id!!
        val rejectComment = "商品が不適切な内容です"

        // When
        rejectItem.execute(itemId, rejectComment)

        // Then
        // 通知コントローラーが呼び出されることを確認
        // 実際の通知送信は統合テストで検証
        val itemAfter = Item.findById(itemId)
        assertNotNull(itemAfter)
        assertFalse(itemAfter!!.available)
    }
}
