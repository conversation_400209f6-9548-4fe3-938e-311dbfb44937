package jp.co.torihada.fanme.endpoints.console

import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.junit.mockito.InjectMock
import io.quarkus.test.security.SecurityAttribute
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.transaction.Transactional
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.controllers.ShopLimitationController
import jp.co.torihada.fanme.modules.console.factories.AgencyFactory
import jp.co.torihada.fanme.modules.console.factories.ConsoleUserFactory
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.console.usecases.UpdateShopLimitation
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.shop.models.ShopLimitation
import org.hamcrest.CoreMatchers.equalTo
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.mockito.Mockito.`when`

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class UpdateShopLimitationEndpointTest {

    lateinit var testUser: User
    lateinit var agentUser: User
    lateinit var agency: Agency
    val mockShopId = 9999L

    @InjectMock lateinit var shopLimitationController: ShopLimitationController

    @BeforeAll
    @Transactional
    fun setup() {
        agency = AgencyFactory.new("Shop Limitation Test Agency").apply { persistAndFlush() }

        testUser =
            UserFactory.createTestUser(
                "shop-limitation-test-user",
                "Shop Limitation User",
                "shop-limitation-test-user",
            )!!

        ConsoleUserFactory.new(
                creatorId = testUser.id!!,
                agencyId = agency.id!!,
                role = UserRole.BIZ_VALUE,
            )
            .persistAndFlush()

        agentUser =
            UserFactory.createTestUser(
                "shop-limitation-agent-user",
                "Shop Limitation Agent User",
                "shop-limitation-agent-user",
            )!!

        ConsoleUserFactory.new(
                creatorId = agentUser.id!!,
                agencyId = agency.id!!,
                role = UserRole.AGENT_VALUE,
            )
            .persistAndFlush()
    }

    @AfterAll
    @Transactional
    fun cleanupAll() {
        ConsoleUser.deleteAll()
        Agency.deleteAll()
        User.delete("uid in (?1, ?2)", "shop-limitation-test-user", "shop-limitation-agent-user")
    }

    @Test
    @DisplayName("BIZ role user successfully updates shop limitation")
    @TestSecurity(
        user = "shop-limitation-test-user",
        roles = [UserRole.BIZ_VALUE],
        attributes =
            [SecurityAttribute(key = "login_user_uid", value = "shop-limitation-test-user")],
    )
    @TestTransaction
    fun testUpdateShopLimitation_BizRole_Success() {
        val body =
            """{
                "fileCapacity": 5,
                "fileQuantity": 10,
                "isChekiExhibitable": true
            }"""

        val params =
            UpdateShopLimitation.Input(
                accountIdentity = testUser.accountIdentity!!,
                fileCapacity = 5,
                fileQuantity = 10,
                isChekiExhibitable = true,
            )

        `when`(shopLimitationController.updateShopLimitation(params))
            .thenReturn(
                ShopLimitation().apply {
                    fileCapacity = 5
                    fileQuantity = 10
                    isChekiExhibitable = true
                }
            )

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(body)
            .`when`()
            .put("/console/users/${testUser.accountIdentity}/shop-limitation")
            .then()
            .statusCode(200)
            .body("data.file_capacity", equalTo(5))
            .body("data.file_quantity", equalTo(10))
            .body("data.is_cheki_exhibitable", equalTo(true))
    }

    @Test
    @DisplayName("AGENT role user fails with 403")
    @TestSecurity(
        user = "shop-limitation-agent-user",
        roles = [UserRole.AGENT_VALUE],
        attributes =
            [SecurityAttribute(key = "login_user_uid", value = "shop-limitation-agent-user")],
    )
    @TestTransaction
    fun testUpdateShopLimitation_AgentRole_Forbidden() {
        val body =
            """{
                "fileCapacity": 5,
                "fileQuantity": 10,
                "isChekiExhibitable": true
            }"""

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(body)
            .`when`()
            .put("/console/users/${testUser.accountIdentity}/shop-limitation/")
            .then()
            .statusCode(403)
    }

    @Test
    @DisplayName("Returns 404 when shop not found")
    @TestSecurity(
        user = "shop-limitation-test-user",
        roles = [UserRole.BIZ_VALUE],
        attributes =
            [SecurityAttribute(key = "login_user_uid", value = "shop-limitation-test-user")],
    )
    @TestTransaction
    fun testUpdateShopLimitation_ShopNotFound_Returns404() {
        val body =
            """{
                "fileCapacity": 5,
                "fileQuantity": 10,
                "isChekiExhibitable": true
            }"""

        val nonExistentAccountIdentity = "non-existent-user"
        val params =
            UpdateShopLimitation.Input(
                accountIdentity = nonExistentAccountIdentity,
                fileCapacity = 5,
                fileQuantity = 10,
                isChekiExhibitable = true,
            )

        `when`(shopLimitationController.updateShopLimitation(params)).thenAnswer {
            throw ResourceNotFoundException("Shop")
        }

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(body)
            .`when`()
            .put("/console/users/$nonExistentAccountIdentity/shop-limitation")
            .then()
            .statusCode(404)
    }
}
