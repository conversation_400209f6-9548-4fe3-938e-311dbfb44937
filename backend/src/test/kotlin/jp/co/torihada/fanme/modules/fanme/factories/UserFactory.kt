package jp.co.torihada.fanme.modules.fanme.factories

import java.time.Instant
import jp.co.torihada.fanme.modules.fanme.models.User

object UserFactory {
    fun new(
        uid: String? = null,
        name: String = "Test User",
        accountIdentity: String = "account-identity",
        gender: String = "MALE",
        birthday: Instant = Instant.now(),
        birthdayConfirmed: Boolean = false,
        isPublic: <PERSON>olean = true,
        allowPublicSharing: Boolean = false,
        filledProfile: <PERSON>olean = true,
        purpose: Int = 0,
        userAttributes: String? = null,
        deletedAt: Instant? = null,
        icon: String? = null,
        isBirthdayWeek: Int? = null,
    ): User {
        return User().apply {
            this.uid = uid
            this.name = name
            this.accountIdentity = accountIdentity
            this.gender = gender
            this.birthday = birthday
            this.birthdayConfirmed = birthdayConfirmed
            this.isPublic = isPublic
            this.allowPublicSharing = allowPublicSharing
            this.filledProfile = filledProfile
            this.purpose = purpose
            this.userAttributes = userAttributes
            this.deletedAt = deletedAt
            this.icon = icon
            this.isBirthdayWeek = isBirthdayWeek
        }
    }

    fun createTestUser(
        uid: String = "test-user-uid",
        name: String = "Test User",
        accountIdentity: String = "test-user-account-identity",
        purpose: Int = 0,
    ): User? {
        val count = User.count("uid = ?1", uid)
        if (count == 0L) {
            val accountExists = User.count("accountIdentity = ?1", accountIdentity) > 0
            if (accountExists) {
                val uniqueAccountIdentity = "$accountIdentity-${System.currentTimeMillis()}"
                val user =
                    new(
                        uid = uid,
                        name = name,
                        accountIdentity = uniqueAccountIdentity,
                        purpose = purpose,
                    )
                user.persistAndFlush()
                return user
            } else {
                val user =
                    new(
                        uid = uid,
                        name = name,
                        accountIdentity = accountIdentity,
                        purpose = purpose,
                    )
                user.persistAndFlush()
                return user
            }
        }

        return User.find("uid", uid).firstResult()
    }
}
