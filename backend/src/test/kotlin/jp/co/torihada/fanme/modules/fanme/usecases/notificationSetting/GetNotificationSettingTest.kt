package jp.co.torihada.fanme.modules.fanme.usecases.notificationSetting

import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.factories.NotificationSettingFactory
import jp.co.torihada.fanme.modules.fanme.models.NotificationSetting
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

@QuarkusTest
class GetNotificationSettingTest {

    @Inject lateinit var getNotificationSetting: GetNotificationSetting

    @Test
    @TestTransaction
    fun `test return enabled true when notification setting exists and is enabled`() {
        val creatorUid = "test-creator-uid-1"
        NotificationSettingFactory.createTestNotificationSetting(
            creatorUid = creatorUid,
            enabled = true,
        )

        val input = GetNotificationSetting.Input(creatorUid = creatorUid)

        val result = getNotificationSetting.execute(input)

        assertNotNull(result)
        val setting = NotificationSetting.find("creatorUid = ?1", creatorUid).firstResult()
        assertNotNull(setting)
        assertTrue(setting!!.enabled)
    }

    @Test
    @TestTransaction
    fun `test return enabled false when notification setting exists and is disabled`() {
        val creatorUid = "test-creator-uid-2"
        NotificationSettingFactory.createTestNotificationSetting(
            creatorUid = creatorUid,
            enabled = false,
        )

        val input = GetNotificationSetting.Input(creatorUid = creatorUid)

        val result = getNotificationSetting.execute(input)

        assertNotNull(result)
        val setting = NotificationSetting.find("creatorUid = ?1", creatorUid).firstResult()
        assertNotNull(setting)
        assertFalse(setting!!.enabled)
    }

    @Test
    @TestTransaction
    fun `test return null when notification setting does not exist`() {
        val creatorUid = "non-existent-creator-uid"
        val input = GetNotificationSetting.Input(creatorUid = creatorUid)

        val result = getNotificationSetting.execute(input)

        assertNotNull(result)
        val setting = NotificationSetting.find("creatorUid = ?1", creatorUid).firstResult()
        assertNull(setting)
    }
}
