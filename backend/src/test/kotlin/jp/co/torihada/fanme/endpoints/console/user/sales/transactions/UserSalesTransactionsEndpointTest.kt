package jp.co.torihada.fanme.endpoints.console.user.sales.transactions

import io.quarkus.test.InjectMock
import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.SecurityAttribute
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured
import io.restassured.http.ContentType
import jakarta.transaction.Transactional
import java.time.Instant
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.factories.AgencyFactory
import jp.co.torihada.fanme.modules.console.factories.ConsoleUserFactory
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.payment.controllers.TransactionController
import jp.co.torihada.fanme.modules.payment.models.Transaction
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.controllers.OrderController
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.models.Order
import jp.co.torihada.fanme.modules.shop.models.PurchasedItem
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.Matchers
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.mockito.ArgumentMatchers
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`

@QuarkusTest
@DisplayName("User Sales Transactions Endpoint Tests")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class UserSalesTransactionsEndpointTest {

    @InjectMock lateinit var transactionController: TransactionController

    @InjectMock lateinit var orderController: OrderController

    private var testUser: User? = null
    private var nonConsoleUser: User? = null
    private var testAgency: Agency? = null
    private var otherAgency: Agency? = null
    private var superAdmin: User? = null
    private var agentUser: User? = null
    private var otherAgencyUser: User? = null

    @BeforeEach
    @Transactional
    fun setUp() {

        testAgency = AgencyFactory.new(name = "テスト事務所").apply { persistAndFlush() }

        otherAgency = AgencyFactory.new(name = "他の事務所").apply { persistAndFlush() }

        UserFactory.createTestUser(uid = "purchaser-1", name = "購入者1")
        UserFactory.createTestUser(uid = "purchaser-2", name = "購入者2")
        UserFactory.createTestUser(uid = "purchaser-3", name = "購入者3")

        testUser =
            UserFactory.createTestUser(
                uid = "test-user-tx-1",
                name = "Test User 1",
                accountIdentity = "<EMAIL>",
            )
        nonConsoleUser =
            UserFactory.createTestUser(
                uid = "non-console-user-tx",
                name = "Non Console User",
                accountIdentity = "<EMAIL>",
            )

        val existingConsoleUser1 = ConsoleUser.find("user.id = ?1", testUser!!.id!!).firstResult()
        if (existingConsoleUser1 == null) {
            val consoleUser1 =
                ConsoleUserFactory.new(
                    creatorId = testUser!!.id!!,
                    agencyId = testAgency!!.id,
                    role = ConsoleUserRole.CREATOR.value,
                )
            consoleUser1.persist()
            testUser!!.consoleUser = consoleUser1
        } else {
            testUser!!.consoleUser = existingConsoleUser1
        }

        superAdmin =
            UserFactory.createTestUser(
                uid = "super-admin-test-tx",
                name = "Super Admin",
                accountIdentity = "<EMAIL>",
            )
        val existingSuperConsoleUser =
            ConsoleUser.find("user.id = ?1", superAdmin!!.id!!).firstResult()
        if (existingSuperConsoleUser == null) {
            val superConsoleUser =
                ConsoleUserFactory.new(
                    creatorId = superAdmin!!.id!!,
                    agencyId = null,
                    role = ConsoleUserRole.SUPER.value,
                )
            superConsoleUser.persist()
            superAdmin!!.consoleUser = superConsoleUser
        } else {
            superAdmin!!.consoleUser = existingSuperConsoleUser
        }

        agentUser =
            UserFactory.createTestUser(
                uid = "agent-test-403",
                name = "Agent User",
                accountIdentity = "<EMAIL>",
            )
        val existingAgentConsoleUser =
            ConsoleUser.find("user.id = ?1", agentUser!!.id!!).firstResult()
        if (existingAgentConsoleUser == null) {
            val agentConsoleUser =
                ConsoleUserFactory.new(
                    creatorId = agentUser!!.id!!,
                    agencyId = testAgency!!.id,
                    role = ConsoleUserRole.AGENT.value,
                )
            agentConsoleUser.persist()
            agentUser!!.consoleUser = agentConsoleUser
        } else {
            agentUser!!.consoleUser = existingAgentConsoleUser
        }

        otherAgencyUser =
            UserFactory.createTestUser(
                uid = "test-user-agent-403",
                name = "Test User Agent",
                accountIdentity = "<EMAIL>",
            )
        val existingOtherAgencyConsoleUser =
            ConsoleUser.find("user.id = ?1", otherAgencyUser!!.id!!).firstResult()
        if (existingOtherAgencyConsoleUser == null) {
            val otherAgencyConsoleUser =
                ConsoleUserFactory.new(
                    creatorId = otherAgencyUser!!.id!!,
                    agencyId = otherAgency!!.id,
                    role = ConsoleUserRole.CREATOR.value,
                )
            otherAgencyConsoleUser.persist()
            otherAgencyUser!!.consoleUser = otherAgencyConsoleUser
        } else {
            otherAgencyUser!!.consoleUser = existingOtherAgencyConsoleUser
        }
    }

    private fun setupMocks() {
        val transactions =
            listOf(
                createMockTransaction(
                    id = 1001,
                    sellerUserId = testUser!!.uid!!,
                    purchaserUserId = "purchaser-1",
                    amount = 5000,
                    createdAt = Instant.now().minusSeconds(3600),
                    paymentType = "credit_card",
                ),
                createMockTransaction(
                    id = 1002,
                    sellerUserId = testUser!!.uid!!,
                    purchaserUserId = "purchaser-2",
                    amount = 3000,
                    createdAt = Instant.now().minusSeconds(7200),
                    paymentType = "convenience",
                ),
                createMockTransaction(
                    id = 1003,
                    sellerUserId = testUser!!.uid!!,
                    purchaserUserId = "purchaser-3",
                    amount = 7000,
                    createdAt = Instant.now().minusSeconds(10800),
                    paymentType = "pay_pay",
                ),
            )

        `when`(
                transactionController.getSellerTransactions(
                    ArgumentMatchers.anyList(),
                    ArgumentMatchers.any(),
                    ArgumentMatchers.any(),
                    ArgumentMatchers.any(),
                    ArgumentMatchers.any(),
                )
            )
            .thenAnswer { invocation ->
                val sellerUserIds = invocation.getArgument<List<String>>(0)
                if (sellerUserIds.contains(testUser?.uid)) {
                    transactions
                } else {
                    emptyList<Transaction>()
                }
            }

        `when`(transactionController.countBySellerUserId(ArgumentMatchers.anyString()))
            .thenReturn(transactions.size.toLong())

        // Mock orders with purchased items for filtering test
        val mockOrder = mock(Order::class.java)
        val mockPurchasedItems = mutableSetOf<PurchasedItem>()

        // Create mock purchased items with different statuses
        val successItem1 = mock(PurchasedItem::class.java)
        val successItem1Item = mock(Item::class.java)
        `when`(successItem1Item.name).thenReturn("成功アイテム1")
        `when`(successItem1.item).thenReturn(successItem1Item)
        `when`(successItem1.status).thenReturn(Util.PurchasedItemStatus.PAYSUCCESS.value)

        val successItem2 = mock(PurchasedItem::class.java)
        val successItem2Item = mock(Item::class.java)
        `when`(successItem2Item.name).thenReturn("成功アイテム2")
        `when`(successItem2.item).thenReturn(successItem2Item)
        `when`(successItem2.status).thenReturn(Util.PurchasedItemStatus.PAYSUCCESS.value)

        val failedItem = mock(PurchasedItem::class.java)
        val failedItemItem = mock(Item::class.java)
        `when`(failedItemItem.name).thenReturn("失敗アイテム")
        `when`(failedItem.item).thenReturn(failedItemItem)
        `when`(failedItem.status).thenReturn(Util.PurchasedItemStatus.PAYFAILED.value)

        mockPurchasedItems.add(successItem1)
        mockPurchasedItems.add(successItem2)
        mockPurchasedItems.add(failedItem)

        `when`(mockOrder.purchasedItems).thenReturn(mockPurchasedItems)

        `when`(orderController.getOrders(ArgumentMatchers.anyLong())).thenAnswer { invocation ->
            val transactionId = invocation.getArgument<Long>(0)
            if (transactionId == 1001L) {
                listOf(mockOrder)
            } else {
                emptyList<Order>()
            }
        }
    }

    private fun createMockTransaction(
        id: Long,
        sellerUserId: String,
        purchaserUserId: String,
        amount: Int,
        createdAt: Instant = Instant.now(),
        paymentType: String? = null,
    ): Transaction {
        val transaction = mock(Transaction::class.java)
        `when`(transaction.id).thenReturn(id)
        `when`(transaction.sellerUserId).thenReturn(sellerUserId)
        `when`(transaction.purchaserUserId).thenReturn(purchaserUserId)
        `when`(transaction.amount).thenReturn(amount)
        `when`(transaction.createdAt).thenReturn(createdAt)
        `when`(transaction.status).thenReturn("SUCCESS")
        `when`(transaction.paymentType).thenReturn(paymentType)
        return transaction
    }

    @Test
    @TestSecurity(
        user = "super-admin-test-tx",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-admin-test-tx")],
    )
    @DisplayName("正常系：ユーザーの取引履歴データが正しく返ること")
    fun shouldReturnUserTransactionsDataCorrectly() {
        setupMocks()

        RestAssured.given()
            .contentType(ContentType.JSON)
            .`when`()
            .get("/console/users/${testUser!!.accountIdentity}/sales/transactions")
            .then()
            .statusCode(200)
            .body("data.purchaseHistories.size()", equalTo(3))
            .body("data.purchaseHistories[0].purchaserName", equalTo("購入者1"))
            .body("data.purchaseHistories[0].sellerSalesAmount", equalTo(5000))
            .body(
                "data.purchaseHistories[0].itemNames",
                Matchers.containsInAnyOrder("成功アイテム1", "成功アイテム2"),
            )
            .body("data.purchaseHistories[0].quantity", equalTo(2))
            .body("data.purchaseHistories[0].itemNames.size()", equalTo(2))
            .body("data.purchaseHistories[0].paymentType", equalTo("クレジットカード(一括)"))
            .body("data.purchaseHistories[1].purchaserName", equalTo("購入者2"))
            .body("data.purchaseHistories[1].sellerSalesAmount", equalTo(3000))
            .body("data.purchaseHistories[1].itemNames.size()", equalTo(1))
            .body("data.purchaseHistories[1].paymentType", equalTo("コンビニ支払い"))
            .body("data.purchaseHistories[2].purchaserName", equalTo("購入者3"))
            .body("data.purchaseHistories[2].sellerSalesAmount", equalTo(7000))
            .body("data.purchaseHistories[2].itemNames.size()", equalTo(1))
            .body("data.purchaseHistories[2].paymentType", equalTo("PayPay"))

        RestAssured.given()
            .contentType(ContentType.JSON)
            .`when`()
            .get("/console/users/${nonConsoleUser!!.accountIdentity}/sales/transactions")
            .then()
            .statusCode(200)
            .body("data.purchaseHistories.size()", equalTo(0))
    }

    @Test
    @TestSecurity(
        user = "agent-test-403",
        roles = [UserRole.AGENT_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "agent-test-403")],
    )
    @DisplayName("異常系：agentロールが他事務所のクリエイターにアクセスできないこと")
    fun shouldReturn403WhenAgentTriesToAccessCreatorFromDifferentAgency() {

        val otherAgencyUser =
            User.find("uid", "test-user-agent-403").firstResult()
                ?: throw IllegalStateException("Other agency user not found")

        setupMocks()

        RestAssured.given()
            .contentType(ContentType.JSON)
            .`when`()
            .get("/console/users/${otherAgencyUser.accountIdentity}/sales/transactions")
            .then()
            .statusCode(403)
    }
}
