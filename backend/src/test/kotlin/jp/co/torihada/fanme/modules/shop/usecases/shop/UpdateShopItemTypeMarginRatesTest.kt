package jp.co.torihada.fanme.modules.shop.usecases.shop

import com.github.michaelbull.result.getOrThrow
import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.shop.factories.ShopFactory
import jp.co.torihada.fanme.modules.shop.lib.TestResultLogger
import jp.co.torihada.fanme.modules.shop.models.ItemType
import jp.co.torihada.fanme.modules.shop.models.ShopItemTypeMarginRate
import jp.co.torihada.fanme.modules.shop.usecases.TestResourceLifecycleManager
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class UpdateShopItemTypeMarginRatesTest {
    @Inject lateinit var updateShopItemTypeMarginRates: UpdateShopItemTypeMarginRates

    @Test
    @TestTransaction
    fun `create and update margin rates in bulk`() {
        val shop = ShopFactory.new().apply { persist() }
        val shopId = shop.id!!
        ShopItemTypeMarginRate.create(shop, ItemType.DIGITAL_BUNDLE, 0.15f)

        val inputItems = listOf(ItemType.DIGITAL_BUNDLE to 0.20f, ItemType.CHEKI to 0.10f)

        val result =
            updateShopItemTypeMarginRates
                .execute(UpdateShopItemTypeMarginRates.Input(shopId = shopId, items = inputItems))
                .getOrThrow()

        assertEquals(2, result.size)
        val updatedBundle = result.first { it.itemType == ItemType.DIGITAL_BUNDLE }
        assertEquals(0.20f, updatedBundle.marginRate)
        val createdCheki = result.first { it.itemType == ItemType.CHEKI }
        assertEquals(0.10f, createdCheki.marginRate)
    }
}
