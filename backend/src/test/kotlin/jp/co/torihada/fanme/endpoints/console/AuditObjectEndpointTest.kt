package jp.co.torihada.fanme.endpoints.console

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.transaction.Transactional
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.fanme.factories.AuditGroupFactory
import jp.co.torihada.fanme.modules.fanme.factories.AuditObjectFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditType
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.OperationType
import jp.co.torihada.fanme.modules.fanme.models.AuditGroupMetadata
import jp.co.torihada.fanme.modules.fanme.models.AuditObject
import jp.co.torihada.fanme.modules.fanme.models.AuditObject.AssetType
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.CoreMatchers.hasItem
import org.junit.jupiter.api.*

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AuditObjectEndpointTest {
    private var groupId: Long = 0
    private var userUid: String = ""

    @BeforeAll
    @Transactional
    fun setupAll() {
        val user =
            UserFactory.createTestUser("audit-user-obj", "Audit User Obj", "<EMAIL>")!!
        userUid = user.uid!!
        val group =
            AuditGroupFactory.new(
                userUid = user.uid!!,
                auditType = AuditType.SHOP_ITEM,
                operationType = OperationType.INSERT,
                itemId = 100L,
                metadata = AuditGroupMetadata(shopId = 10L),
            )
        group.persistAndFlush()
        groupId = group.id!!
        AuditObjectFactory.new(groupId, "bucket-obj1", "path-obj1", AssetType.IMAGE)
            .persistAndFlush()
        AuditObjectFactory.new(groupId, "bucket-obj2", "path-obj2", AssetType.MOVIE)
            .persistAndFlush()
    }

    @AfterAll
    @Transactional
    fun cleanupAll() {
        AuditObject.deleteAll()
        AuditGroup.deleteAll()
    }

    @Test
    @DisplayName("SUPERロールでaudit_objectsが取得できること")
    @TestSecurity(user = "test-super", roles = [UserRole.SUPER_VALUE])
    fun superRoleCanGetAuditObjects() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/audit-groups/$groupId/audit-objects")
            .then()
            .statusCode(200)
            .body("data.auditObjects.size()", equalTo(2))
            .body("data.auditObjects.bucket", hasItem("bucket-obj1"))
            .body("data.auditObjects.bucket", hasItem("bucket-obj2"))
    }

    @Test
    @DisplayName("BIZロールでaudit_objectsが取得できること")
    @TestSecurity(user = "test-biz", roles = [UserRole.BIZ_VALUE])
    fun bizRoleCanGetAuditObjects() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/audit-groups/$groupId/audit-objects")
            .then()
            .statusCode(200)
            .body("data.auditObjects.size()", equalTo(2))
    }

    @Test
    @DisplayName("AGENTロールではaudit_objectsにアクセスできないこと")
    @TestSecurity(user = "test-agent", roles = [UserRole.AGENT_VALUE])
    fun agentRoleCannotAccessAuditObjects() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/audit-groups/$groupId/audit-objects")
            .then()
            .statusCode(403)
    }

    @Test
    @DisplayName("認証無しの場合は401エラーが返されること")
    fun unauthorizedRequestReturns401() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/audit-groups/$groupId/audit-objects")
            .then()
            .statusCode(401)
    }
}
