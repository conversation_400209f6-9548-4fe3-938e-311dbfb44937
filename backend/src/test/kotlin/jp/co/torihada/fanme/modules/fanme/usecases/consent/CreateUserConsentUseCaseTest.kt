package jp.co.torihada.fanme.modules.fanme.usecases.consent

import com.github.michaelbull.result.unwrap
import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.UserConsent
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

@QuarkusTest
class CreateUserConsentUseCaseTest {

    @Inject private lateinit var createUserConsentUseCase: CreateUserConsentUseCase

    @Test
    @TestTransaction
    fun `test create user consent successfully`() {
        val user =
            UserFactory.createTestUser(
                uid = "test-user-consent-create",
                name = "Test User for Consent",
                accountIdentity = "<EMAIL>",
            )!!

        val input =
            CreateUserConsentUseCase.Input(
                user = user,
                consentName = UserConsent.Consent.TERMS_20250801.name,
            )

        val result = createUserConsentUseCase.execute(input)

        assertTrue(result.isOk)
        val userConsent = result.unwrap()
        assertNotNull(userConsent)
        assertEquals(user.id, userConsent!!.user!!.id)
        assertEquals(UserConsent.Consent.TERMS_20250801, userConsent.consent)
        assertNotNull(userConsent.id)
        assertNotNull(userConsent.createdAt)
        assertNotNull(userConsent.updatedAt)
    }

    @Test
    @TestTransaction
    fun `test create user consent with invalid consent type returns error`() {
        val user =
            UserFactory.createTestUser(
                uid = "test-user-consent-invalid",
                name = "Test User for Invalid Consent",
                accountIdentity = "<EMAIL>",
            )!!

        val input =
            CreateUserConsentUseCase.Input(user = user, consentName = "INVALID_CONSENT_TYPE")

        val result = createUserConsentUseCase.execute(input)

        assertTrue(result.isErr)
    }
}
