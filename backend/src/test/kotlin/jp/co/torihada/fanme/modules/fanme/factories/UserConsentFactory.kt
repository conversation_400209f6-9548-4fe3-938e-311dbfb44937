package jp.co.torihada.fanme.modules.fanme.factories

import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.models.UserConsent

object UserConsentFactory {
    fun new(
        user: User,
        consent: UserConsent.Consent = UserConsent.Consent.TERMS_20250801,
    ): UserConsent {
        return UserConsent().apply {
            this.user = user
            this.consent = consent
        }
    }

    fun createTestUserConsent(
        user: User,
        consent: UserConsent.Consent = UserConsent.Consent.TERMS_20250801,
    ): UserConsent {
        val userConsent = new(user = user, consent = consent)
        userConsent.persistAndFlush()
        return userConsent
    }
}
