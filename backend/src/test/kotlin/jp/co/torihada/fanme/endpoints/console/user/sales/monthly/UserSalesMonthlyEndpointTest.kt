package jp.co.torihada.fanme.endpoints.console.user.sales.monthly

import io.quarkus.test.InjectMock
import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.SecurityAttribute
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured
import io.restassured.http.ContentType
import jakarta.transaction.Transactional
import java.time.Instant
import java.time.YearMonth
import java.time.format.DateTimeFormatter
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.factories.AgencyFactory
import jp.co.torihada.fanme.modules.console.factories.ConsoleUserFactory
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.payment.controllers.MonthlySellerSalesController
import jp.co.torihada.fanme.modules.payment.controllers.TransactionController
import jp.co.torihada.fanme.modules.payment.mock.CustomArgumentMatchers
import jp.co.torihada.fanme.modules.payment.models.Transaction
import jp.co.torihada.fanme.modules.payment.usecases.monthlysales.GetMonthlySellerSales
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.CoreMatchers.notNullValue
import org.hamcrest.CoreMatchers.nullValue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`

@QuarkusTest
@DisplayName("User Sales Monthly Endpoint Tests")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class UserSalesMonthlyEndpointTest {

    @InjectMock lateinit var monthlySellerSalesController: MonthlySellerSalesController

    @InjectMock lateinit var transactionController: TransactionController

    private var testAgency: Agency? = null
    private var otherAgency: Agency? = null
    private var testUser: User? = null
    private var otherAgencyUser: User? = null
    private var nonConsoleUser: User? = null
    private var superAdmin: User? = null
    private var bizAdmin: User? = null
    private var agentUser: User? = null
    private var creatorUser: User? = null

    private val formatter = DateTimeFormatter.ofPattern("yyyyMM")

    @BeforeEach
    @Transactional
    fun setUp() {

        testAgency = AgencyFactory.new(name = "テスト事務所").apply { persistAndFlush() }

        otherAgency = AgencyFactory.new(name = "他の事務所").apply { persistAndFlush() }

        testUser =
            UserFactory.createTestUser(
                uid = "test-user-1",
                name = "Test User 1",
                accountIdentity = "<EMAIL>",
            )
        otherAgencyUser =
            UserFactory.createTestUser(
                uid = "other-agency-user-1",
                name = "Other Agency User",
                accountIdentity = "<EMAIL>",
            )
        nonConsoleUser =
            UserFactory.createTestUser(
                uid = "non-console-user",
                name = "Non Console User",
                accountIdentity = "<EMAIL>",
            )

        val existingConsoleUser1 = ConsoleUser.find("user.id = ?1", testUser!!.id!!).firstResult()
        if (existingConsoleUser1 == null) {
            val consoleUser1 =
                ConsoleUserFactory.new(
                    creatorId = testUser!!.id!!,
                    agencyId = testAgency!!.id,
                    role = ConsoleUserRole.CREATOR.value,
                )
            consoleUser1.persist()
            testUser!!.consoleUser = consoleUser1
        } else {
            testUser!!.consoleUser = existingConsoleUser1
        }

        val existingConsoleUser2 =
            ConsoleUser.find("user.id = ?1", otherAgencyUser!!.id!!).firstResult()
        if (existingConsoleUser2 == null) {
            val consoleUser2 =
                ConsoleUserFactory.new(
                    creatorId = otherAgencyUser!!.id!!,
                    agencyId = otherAgency!!.id,
                    role = ConsoleUserRole.CREATOR.value,
                )
            consoleUser2.persist()
            otherAgencyUser!!.consoleUser = consoleUser2
        } else {
            otherAgencyUser!!.consoleUser = existingConsoleUser2
        }

        superAdmin =
            UserFactory.createTestUser(
                uid = "super-admin-test",
                name = "Super Admin",
                accountIdentity = "<EMAIL>",
            )
        val existingSuperConsoleUser =
            ConsoleUser.find("user.id = ?1", superAdmin!!.id!!).firstResult()
        if (existingSuperConsoleUser == null) {
            val superConsoleUser =
                ConsoleUserFactory.new(
                    creatorId = superAdmin!!.id!!,
                    agencyId = null,
                    role = ConsoleUserRole.SUPER.value,
                )
            superConsoleUser.persist()
            superAdmin!!.consoleUser = superConsoleUser
        } else {
            superAdmin!!.consoleUser = existingSuperConsoleUser
        }

        bizAdmin =
            UserFactory.createTestUser(
                uid = "biz-admin-test",
                name = "Biz Admin",
                accountIdentity = "<EMAIL>",
            )
        val existingBizConsoleUser = ConsoleUser.find("user.id = ?1", bizAdmin!!.id!!).firstResult()
        if (existingBizConsoleUser == null) {
            val bizConsoleUser =
                ConsoleUserFactory.new(
                    creatorId = bizAdmin!!.id!!,
                    agencyId = null,
                    role = ConsoleUserRole.BIZ.value,
                )
            bizConsoleUser.persist()
            bizAdmin!!.consoleUser = bizConsoleUser
        } else {
            bizAdmin!!.consoleUser = existingBizConsoleUser
        }

        agentUser =
            UserFactory.createTestUser(
                uid = "agent-test",
                name = "Agent User",
                accountIdentity = "<EMAIL>",
            )
        val existingAgentConsoleUser =
            ConsoleUser.find("user.id = ?1", agentUser!!.id!!).firstResult()
        if (existingAgentConsoleUser == null) {
            val agentConsoleUser =
                ConsoleUserFactory.new(
                    creatorId = agentUser!!.id!!,
                    agencyId = testAgency!!.id,
                    role = ConsoleUserRole.AGENT.value,
                )
            agentConsoleUser.persist()
            agentUser!!.consoleUser = agentConsoleUser
        } else {
            agentUser!!.consoleUser = existingAgentConsoleUser
        }

        creatorUser =
            UserFactory.createTestUser(
                uid = "creator-test",
                name = "Creator User",
                accountIdentity = "<EMAIL>",
            )
        val existingCreatorConsoleUser =
            ConsoleUser.find("user.id = ?1", creatorUser!!.id!!).firstResult()
        if (existingCreatorConsoleUser == null) {
            val creatorConsoleUser =
                ConsoleUserFactory.new(
                    creatorId = creatorUser!!.id!!,
                    agencyId = testAgency!!.id,
                    role = ConsoleUserRole.CREATOR.value,
                )
            creatorConsoleUser.persist()
            creatorUser!!.consoleUser = creatorConsoleUser
        } else {
            creatorUser!!.consoleUser = existingCreatorConsoleUser
        }
    }

    private fun createTestMonthlySalesData(
        userUid: String
    ): List<GetMonthlySellerSales.MonthlySellerSales> {
        val currentMonth = YearMonth.now()
        return listOf(
            createMonthlySalesData(
                sellerUserId = userUid,
                yearMonth = currentMonth.minusMonths(2).format(formatter),
                amount = 50000,
            ),
            createMonthlySalesData(
                sellerUserId = userUid,
                yearMonth = currentMonth.minusMonths(1).format(formatter),
                amount = 75000,
            ),
            createMonthlySalesData(
                sellerUserId = userUid,
                yearMonth = currentMonth.format(formatter),
                amount = 100000,
            ),
        )
    }

    private fun createMonthlySalesData(
        sellerUserId: String,
        yearMonth: String,
        amount: Int,
    ): GetMonthlySellerSales.MonthlySellerSales {
        return GetMonthlySellerSales.MonthlySellerSales(
            sellerUserId = sellerUserId,
            yearMonth = yearMonth,
            sellerSalesAmount = amount,
        )
    }

    @Test
    @TestSecurity(
        user = "super-admin-test",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-admin-test")],
    )
    @DisplayName("正常系：ユーザーの月次売上データが正しく返ること")
    fun shouldReturnUserMonthlySalesDataCorrectly() {
        val currentMonth = YearMonth.now()
        val from = currentMonth.minusMonths(2).format(formatter)
        val to = currentMonth.format(formatter)

        val monthlySalesData = createTestMonthlySalesData(testUser!!.uid!!)

        `when`(
                monthlySellerSalesController.getMonthlySellerSales(
                    CustomArgumentMatchers.eq(listOf(testUser!!.uid!!)),
                    CustomArgumentMatchers.eq(from),
                    CustomArgumentMatchers.eq(to),
                )
            )
            .thenReturn(monthlySalesData)

        val currentMonthInstant =
            Instant.parse(
                "${currentMonth.year}-${String.format("%02d", currentMonth.monthValue)}-15T00:00:00Z"
            )

        val mockTransaction1 = mock(Transaction::class.java)
        `when`(mockTransaction1.sellerUserId).thenReturn(testUser!!.uid)
        `when`(mockTransaction1.purchaserUserId).thenReturn("purchaser-1")
        `when`(mockTransaction1.createdAt).thenReturn(currentMonthInstant)

        val mockTransaction2 = mock(Transaction::class.java)
        `when`(mockTransaction2.sellerUserId).thenReturn(testUser!!.uid)
        `when`(mockTransaction2.purchaserUserId).thenReturn("purchaser-2")
        `when`(mockTransaction2.createdAt).thenReturn(currentMonthInstant.minusSeconds(3600))

        val transactions = listOf(mockTransaction1, mockTransaction2)

        `when`(
                transactionController.getSellerTransactions(
                    CustomArgumentMatchers.any(),
                    CustomArgumentMatchers.any(),
                    CustomArgumentMatchers.any(),
                    CustomArgumentMatchers.any(),
                    CustomArgumentMatchers.any(),
                )
            )
            .thenAnswer { invocation ->
                val sellerUserIds = invocation.getArgument<List<String>>(0)
                if (sellerUserIds.contains(testUser!!.uid)) {
                    transactions
                } else {
                    emptyList<Transaction>()
                }
            }

        val response =
            RestAssured.given()
                .contentType(ContentType.JSON)
                .queryParam("from", from)
                .queryParam("to", to)
                .`when`()
                .get("/console/users/${testUser!!.accountIdentity}/sales/monthly")
                .then()
                .statusCode(200)
                .extract()
                .response()

        println("Response body: ${response.body().asString()}")

        response
            .then()
            .body("data.userMonthlySalesList", notNullValue())
            .body("data.userMonthlySalesList.size()", equalTo(3))
            .body("data.userMonthlySalesList[0].yearMonth", equalTo(monthlySalesData[2].yearMonth))
            .body(
                "data.userMonthlySalesList[0].salesAmount",
                equalTo(monthlySalesData[2].sellerSalesAmount),
            )
            .body("data.userMonthlySalesList[0].purchaserCount", equalTo(2))
            .body("data.userMonthlySalesList[0].purchaseCount", equalTo(2))
            .body(
                "data.userMonthlySalesList[1].salesAmount",
                equalTo(monthlySalesData[1].sellerSalesAmount),
            )
            .body(
                "data.userMonthlySalesList[2].salesAmount",
                equalTo(monthlySalesData[0].sellerSalesAmount),
            )
            .body("data.userMonthlySalesList[2].purchaserCount", equalTo(0))
            .body("data.userMonthlySalesList[2].purchaseCount", equalTo(0))
    }

    @Test
    @TestSecurity(
        user = "super-admin-test",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-admin-test")],
    )
    @DisplayName("正常系：売上データが無い場合は0で補完された配列が返ること")
    fun shouldReturnArrayWithZerosWhenNoSalesDataExists() {
        val currentMonth = YearMonth.now()
        val from = currentMonth.minusMonths(2).format(formatter)
        val to = currentMonth.format(formatter)

        `when`(
                monthlySellerSalesController.getMonthlySellerSales(
                    CustomArgumentMatchers.eq(listOf(testUser!!.uid!!)),
                    CustomArgumentMatchers.eq(from),
                    CustomArgumentMatchers.eq(to),
                )
            )
            .thenReturn(emptyList())

        `when`(transactionController.getTransactions(CustomArgumentMatchers.any()))
            .thenReturn(emptyList())

        RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParam("from", from)
            .queryParam("to", to)
            .`when`()
            .get("/console/users/${testUser!!.accountIdentity}/sales/monthly")
            .then()
            .statusCode(200)
            .body("data.userMonthlySalesList.size()", equalTo(3))
            .body("data.userMonthlySalesList[0].salesAmount", equalTo(0))
            .body("data.userMonthlySalesList[0].purchaserCount", equalTo(0))
            .body("data.userMonthlySalesList[0].purchaseCount", equalTo(0))
            .body("data.userMonthlySalesList[0].growthRate", equalTo(null))
            .body("data.userMonthlySalesList[1].salesAmount", equalTo(0))
            .body("data.userMonthlySalesList[1].purchaserCount", equalTo(0))
            .body("data.userMonthlySalesList[1].purchaseCount", equalTo(0))
            .body("data.userMonthlySalesList[1].growthRate", equalTo(null))
            .body("data.userMonthlySalesList[2].salesAmount", equalTo(0))
            .body("data.userMonthlySalesList[2].purchaserCount", equalTo(0))
            .body("data.userMonthlySalesList[2].purchaseCount", equalTo(0))
            .body("data.userMonthlySalesList[2].growthRate", equalTo(null))
    }

    @Test
    @TestSecurity(
        user = "super-admin-test",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-admin-test")],
    )
    @DisplayName("正常系：日付パラメータを指定しない場合は過去3ヶ月のデータが返ること")
    fun shouldReturnPast3MonthsDataWhenDateParametersAreNotSpecified() {
        val currentMonth = YearMonth.now()
        val twoMonthsAgo = currentMonth.minusMonths(2)
        val fromStr = twoMonthsAgo.format(formatter)
        val toStr = currentMonth.format(formatter)

        val monthlySalesData =
            listOf(
                createMonthlySalesData(
                    sellerUserId = testUser!!.uid!!,
                    yearMonth = twoMonthsAgo.format(formatter),
                    amount = 50000,
                ),
                createMonthlySalesData(
                    sellerUserId = testUser!!.uid!!,
                    yearMonth = currentMonth.minusMonths(1).format(formatter),
                    amount = 75000,
                ),
                createMonthlySalesData(
                    sellerUserId = testUser!!.uid!!,
                    yearMonth = currentMonth.format(formatter),
                    amount = 100000,
                ),
            )

        `when`(
                monthlySellerSalesController.getMonthlySellerSales(
                    CustomArgumentMatchers.eq(listOf(testUser!!.uid!!)),
                    CustomArgumentMatchers.eq(fromStr),
                    CustomArgumentMatchers.eq(toStr),
                )
            )
            .thenReturn(monthlySalesData)

        RestAssured.given()
            .contentType(ContentType.JSON)
            .`when`()
            .get("/console/users/${testUser!!.accountIdentity}/sales/monthly")
            .then()
            .statusCode(200)
            .body("data.userMonthlySalesList.size()", equalTo(3))
            .body("data.userMonthlySalesList[0].yearMonth", equalTo(toStr))
            .body("data.userMonthlySalesList[0].salesAmount", equalTo(100000))
            .body("data.userMonthlySalesList[1].salesAmount", equalTo(75000))
            .body("data.userMonthlySalesList[2].salesAmount", equalTo(50000))
    }

    @Test
    @TestSecurity(
        user = "super-admin-test",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-admin-test")],
    )
    @DisplayName("成長率計算：前月比の成長率が正しく計算されること")
    fun shouldCalculateGrowthRateCorrectly() {
        val currentMonth = YearMonth.now()
        val from = currentMonth.minusMonths(2).format(formatter)
        val to = currentMonth.format(formatter)

        val monthlySalesData =
            listOf(
                createMonthlySalesData(
                    sellerUserId = testUser!!.uid!!,
                    yearMonth = currentMonth.minusMonths(2).format(formatter),
                    amount = 100000,
                ),
                createMonthlySalesData(
                    sellerUserId = testUser!!.uid!!,
                    yearMonth = currentMonth.minusMonths(1).format(formatter),
                    amount = 150000,
                ),
                createMonthlySalesData(
                    sellerUserId = testUser!!.uid!!,
                    yearMonth = currentMonth.format(formatter),
                    amount = 180000,
                ),
            )

        `when`(
                monthlySellerSalesController.getMonthlySellerSales(
                    CustomArgumentMatchers.eq(listOf(testUser!!.uid!!)),
                    CustomArgumentMatchers.eq(from),
                    CustomArgumentMatchers.eq(to),
                )
            )
            .thenReturn(monthlySalesData)

        RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParam("from", from)
            .queryParam("to", to)
            .`when`()
            .get("/console/users/${testUser!!.accountIdentity}/sales/monthly")
            .then()
            .statusCode(200)
            .body("data.userMonthlySalesList[0].growthRate", equalTo(20.0f))
            .body("data.userMonthlySalesList[1].growthRate", equalTo(50.0f))
            .body("data.userMonthlySalesList[2].growthRate", nullValue())
    }

    @Test
    @TestSecurity(
        user = "super-admin-test",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-admin-test")],
    )
    @DisplayName("成長率計算：前月の売上が0の場合はnullが返ること")
    fun shouldReturnNullGrowthRateWhenPreviousMonthAmountIsZero() {
        val currentMonth = YearMonth.now()
        val from = currentMonth.minusMonths(2).format(formatter)
        val to = currentMonth.format(formatter)

        val monthlySalesData =
            listOf(
                createMonthlySalesData(
                    sellerUserId = testUser!!.uid!!,
                    yearMonth = currentMonth.minusMonths(2).format(formatter),
                    amount = 100000,
                ),
                createMonthlySalesData(
                    sellerUserId = testUser!!.uid!!,
                    yearMonth = currentMonth.minusMonths(1).format(formatter),
                    amount = 0,
                ),
                createMonthlySalesData(
                    sellerUserId = testUser!!.uid!!,
                    yearMonth = currentMonth.format(formatter),
                    amount = 50000,
                ),
            )

        `when`(
                monthlySellerSalesController.getMonthlySellerSales(
                    CustomArgumentMatchers.eq(listOf(testUser!!.uid!!)),
                    CustomArgumentMatchers.eq(from),
                    CustomArgumentMatchers.eq(to),
                )
            )
            .thenReturn(monthlySalesData)

        RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParam("from", from)
            .queryParam("to", to)
            .`when`()
            .get("/console/users/${testUser!!.accountIdentity}/sales/monthly")
            .then()
            .statusCode(200)
            .body("data.userMonthlySalesList[1].growthRate", equalTo(-100.0f))
            .body("data.userMonthlySalesList[2].growthRate", nullValue())
    }

    @Test
    @TestSecurity(
        user = "super-admin-test",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-admin-test")],
    )
    @DisplayName("成長率計算：売上が減少した場合はマイナスの成長率が返ること")
    fun shouldReturnNegativeGrowthRateWhenSalesDecrease() {
        val currentMonth = YearMonth.now()
        val from = currentMonth.minusMonths(2).format(formatter)
        val to = currentMonth.format(formatter)

        val monthlySalesData =
            listOf(
                createMonthlySalesData(
                    sellerUserId = testUser!!.uid!!,
                    yearMonth = currentMonth.minusMonths(2).format(formatter),
                    amount = 200000,
                ),
                createMonthlySalesData(
                    sellerUserId = testUser!!.uid!!,
                    yearMonth = currentMonth.minusMonths(1).format(formatter),
                    amount = 150000,
                ),
                createMonthlySalesData(
                    sellerUserId = testUser!!.uid!!,
                    yearMonth = currentMonth.format(formatter),
                    amount = 100000,
                ),
            )

        `when`(
                monthlySellerSalesController.getMonthlySellerSales(
                    CustomArgumentMatchers.eq(listOf(testUser!!.uid!!)),
                    CustomArgumentMatchers.eq(from),
                    CustomArgumentMatchers.eq(to),
                )
            )
            .thenReturn(monthlySalesData)

        RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParam("from", from)
            .queryParam("to", to)
            .`when`()
            .get("/console/users/${testUser!!.accountIdentity}/sales/monthly")
            .then()
            .statusCode(200)
            .body("data.userMonthlySalesList[0].growthRate", equalTo(-33.3f))
            .body("data.userMonthlySalesList[1].growthRate", equalTo(-25.0f))
            .body("data.userMonthlySalesList[2].growthRate", nullValue())
    }

    @Test
    @TestSecurity(
        user = "super-admin-test",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-admin-test")],
    )
    @DisplayName("成長率計算：1ヶ月分のデータのみの場合は成長率がnullであること")
    fun shouldReturnNullGrowthRateForSingleMonthData() {
        val currentMonth = YearMonth.now()
        val currentMonthStr = currentMonth.format(formatter)

        val monthlySalesData =
            listOf(
                createMonthlySalesData(
                    sellerUserId = testUser!!.uid!!,
                    yearMonth = currentMonthStr,
                    amount = 100000,
                )
            )

        `when`(
                monthlySellerSalesController.getMonthlySellerSales(
                    CustomArgumentMatchers.eq(listOf(testUser!!.uid!!)),
                    CustomArgumentMatchers.eq(currentMonthStr),
                    CustomArgumentMatchers.eq(currentMonthStr),
                )
            )
            .thenReturn(monthlySalesData)

        RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParam("from", currentMonthStr)
            .queryParam("to", currentMonthStr)
            .`when`()
            .get("/console/users/${testUser!!.accountIdentity}/sales/monthly")
            .then()
            .statusCode(200)
            .body("data.userMonthlySalesList.size()", equalTo(1))
            .body("data.userMonthlySalesList[0].growthRate", nullValue())
    }
}
