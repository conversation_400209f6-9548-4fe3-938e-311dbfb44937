package jp.co.torihada.fanme.modules.shop.usecases.shopLimitation

import com.github.michaelbull.result.getOrThrow
import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.shop.factories.ShopFactory
import jp.co.torihada.fanme.modules.shop.lib.TestResultLogger
import jp.co.torihada.fanme.modules.shop.models.ShopLimitation
import jp.co.torihada.fanme.modules.shop.usecases.TestResourceLifecycleManager
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class UpdateShopLimitationTest {
    @Inject lateinit var updateShopLimitation: UpdateShopLimitation

    @Test
    @TestTransaction
    fun `update shop limitation`() {
        val shop = ShopFactory.new().apply { persist() }
        val shopId = shop.id!!
        ShopLimitation().apply {
            this.shop = shop
            this.fileCapacity = 0
            this.fileQuantity = 0
            this.isChekiExhibitable = false
            persist()
        }

        val input =
            UpdateShopLimitation.InputUpdateShopLimitation(
                shopId = shopId,
                fileCapacity = 5,
                fileQuantity = 10,
                isChekiExhibitable = true,
            )

        val result = updateShopLimitation.execute(input).getOrThrow()

        assertEquals(5, result.fileCapacity)
        assertEquals(10, result.fileQuantity)
        assertEquals(true, result.isChekiExhibitable)
    }
}
