package jp.co.torihada.fanme.modules.shop.usecases.shop

import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.modules.fanme.controllers.NotificationController
import jp.co.torihada.fanme.modules.shop.factories.ShopFactory
import jp.co.torihada.fanme.modules.shop.models.Shop
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class RejectShopTest {

    @Inject private lateinit var rejectShop: RejectShop

    @Inject private lateinit var notificationController: NotificationController

    private lateinit var shop: Shop

    @AfterAll
    @Transactional
    fun cleanupAll() {
        Shop.deleteAll()
    }

    @BeforeEach
    @Transactional
    fun setUp() {
        // テスト用のShopを作成
        shop =
            ShopFactory.new(
                creatorUid = "test-creator",
                name = "Test Shop",
                description = "Test Shop Description",
                headerImageUri = "https://example.com/header.jpg",
            )
        shop.persistAndFlush()
    }

    @Test
    @DisplayName("ショップが却下された時にDBに正しく保存されること")
    @Transactional
    fun testShopIsRejectedAndSavedToDatabase() {
        // Given
        val shopId = shop.id!!
        val rejectComment = "ショップが不適切な内容です"

        // 却下前の状態を確認
        val shopBefore = Shop.findById(shopId)
        assertNotNull(shopBefore)
        assertEquals("Test Shop Description", shopBefore!!.description)
        assertEquals("https://example.com/header.jpg", shopBefore.headerImageUri)

        // When
        val result = rejectShop.execute(shopId, rejectComment)

        // Then
        assertTrue(result.isOk)

        // DBから再取得して状態を確認
        val shopAfter = Shop.findById(shopId)
        assertNotNull(shopAfter)
        assertEquals("", shopAfter!!.description) // descriptionが空文字になっていることを確認
        assertEquals("", shopAfter.headerImageUri) // headerImageUriが空文字になっていることを確認

        // 他のフィールドは変更されていないことを確認
        assertEquals("Test Shop", shopAfter.name)
        assertEquals("test-creator", shopAfter.creatorUid)
        assertTrue(shopAfter.isOpen)
    }

    @Test
    @DisplayName("却下時に通知が送信されること")
    @Transactional
    fun testNotificationIsSentWhenShopIsRejected() {
        // Given
        val shopId = shop.id!!
        val rejectComment = "ショップが不適切な内容です"

        // When
        rejectShop.execute(shopId, rejectComment)

        // Then
        // 通知コントローラーが呼び出されることを確認
        // 実際の通知送信は統合テストで検証
        val shopAfter = Shop.findById(shopId)
        assertNotNull(shopAfter)
        assertEquals("", shopAfter!!.description)
        assertEquals("", shopAfter.headerImageUri)
    }

    @Test
    @DisplayName("空の却下コメントでも正しく処理されること")
    @Transactional
    fun testRejectShopWithEmptyComment() {
        // Given
        val shopId = shop.id!!
        val emptyComment = ""

        // When
        val result = rejectShop.execute(shopId, emptyComment)

        // Then
        assertTrue(result.isOk)

        val shopAfter = Shop.findById(shopId)
        assertNotNull(shopAfter)
        assertEquals("", shopAfter!!.description)
        assertEquals("", shopAfter.headerImageUri)
    }
}
