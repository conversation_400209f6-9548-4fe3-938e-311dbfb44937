package jp.co.torihada.fanme.modules.fanme.usecases.notificationSetting

import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.factories.NotificationSettingFactory
import jp.co.torihada.fanme.modules.fanme.models.NotificationSetting
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

@QuarkusTest
class UpsertNotificationSettingTest {

    @Inject lateinit var upsertNotificationSetting: UpsertNotificationSetting

    @Test
    @TestTransaction
    fun `test create new notification setting`() {
        val creatorUid = "test-creator-uid-1"
        val enabled = true
        val input = UpsertNotificationSetting.Input(creatorUid = creatorUid, enabled = enabled)

        val result = upsertNotificationSetting.execute(input)

        assertNotNull(result)
        val savedSetting = NotificationSetting.find("creatorUid = ?1", creatorUid).firstResult()
        assertNotNull(savedSetting)
        assertEquals(creatorUid, savedSetting!!.creatorUid)
        assertEquals(enabled, savedSetting.enabled)
    }

    @Test
    @TestTransaction
    fun `test update existing notification setting`() {
        val creatorUid = "test-creator-uid-2"
        val initialEnabled = false
        NotificationSettingFactory.createTestNotificationSetting(
            creatorUid = creatorUid,
            enabled = initialEnabled,
        )

        val updatedEnabled = true
        val updateInput =
            UpsertNotificationSetting.Input(creatorUid = creatorUid, enabled = updatedEnabled)
        val result = upsertNotificationSetting.execute(updateInput)

        assertNotNull(result)
        val updatedSetting = NotificationSetting.find("creatorUid = ?1", creatorUid).firstResult()
        assertNotNull(updatedSetting)
        assertEquals(creatorUid, updatedSetting!!.creatorUid)
        assertEquals(updatedEnabled, updatedSetting.enabled)
    }

    @Test
    @TestTransaction
    fun `test should handle enabled false correctly`() {
        val creatorUid = "test-creator-uid-3"
        val enabled = false
        val input = UpsertNotificationSetting.Input(creatorUid = creatorUid, enabled = enabled)

        val result = upsertNotificationSetting.execute(input)

        assertNotNull(result)
        val savedSetting = NotificationSetting.find("creatorUid = ?1", creatorUid).firstResult()
        assertNotNull(savedSetting)
        assertEquals(creatorUid, savedSetting!!.creatorUid)
        assertEquals(enabled, savedSetting.enabled)
        assertFalse(savedSetting.enabled)
    }
}
