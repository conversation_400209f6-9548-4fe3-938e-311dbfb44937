package jp.co.torihada.fanme.endpoints.console

import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.junit.mockito.InjectMock
import io.quarkus.test.security.SecurityAttribute
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.transaction.Transactional
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.endpoints.console.request.ShopItemTypeMarginRateParam
import jp.co.torihada.fanme.exception.ForbiddenAccessException
import jp.co.torihada.fanme.exception.InvalidParameterException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.controllers.CommissionControlController
import jp.co.torihada.fanme.modules.console.factories.AgencyFactory
import jp.co.torihada.fanme.modules.console.factories.ConsoleUserFactory
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.console.usecases.GetAllShopItems.AllShopItemsResponse
import jp.co.torihada.fanme.modules.console.usecases.UpdateShopItemTypeMarginRate.ShopItemTypeMarginRateResult
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.usecases.shop.GetShop
import jp.co.torihada.fanme.utils.NumberCloseTo
import org.hamcrest.CoreMatchers.equalTo
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CommissionControlEndpointTest {

    lateinit var testUser: User
    val dummyShopId = 9999L

    @InjectMock lateinit var commissionControlController: CommissionControlController

    @BeforeAll
    @Transactional
    fun setup() {
        val agency = AgencyFactory.new("Commission Test Agency").apply { persistAndFlush() }
        testUser =
            UserFactory.createTestUser(
                "commission-test-user",
                "Commission User",
                "commission-test-user",
            )!!
        ConsoleUserFactory.new(
                creatorId = testUser.id!!,
                agencyId = agency.id!!,
                role = UserRole.BIZ_VALUE,
            )
            .persistAndFlush()
    }

    @AfterAll
    @Transactional
    fun cleanupAll() {
        ConsoleUser.delete("user.id = ?1", testUser.id!!)
        Agency.delete("name = ?1", "Commission Test Agency")
        User.delete("uid = ?1", "commission-test-user")
    }

    @Test
    @DisplayName("BIZ user updates shop margin rate via endpoint")
    @TestSecurity(
        user = "commission-test-user",
        roles = [UserRole.BIZ_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "commission-test-user")],
    )
    @TestTransaction
    fun testUpdateShopMarginRateEndpoint() {
        val newRate = 0.22f
        val body =
            "{" +
                "\"marginRates\":[{" +
                "\"itemType\":\"DIGITAL_BUNDLE\",\"marginRate\":$newRate" +
                "}]" +
                "}"

        val marginItems = listOf(ShopItemTypeMarginRateParam("DIGITAL_BUNDLE", newRate))

        `when`(
                commissionControlController.updateShopItemTypeMarginRates(
                    testUser.accountIdentity!!,
                    dummyShopId,
                    marginItems,
                    "commission-test-user",
                    ConsoleUserRole.BIZ,
                )
            )
            .thenReturn(
                listOf(ShopItemTypeMarginRateResult(dummyShopId, "DIGITAL_BUNDLE", newRate))
            )

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(body)
            .`when`()
            .put(
                "/console/commission-control/creator/${testUser.accountIdentity}/shop/$dummyShopId"
            )
            .then()
            .statusCode(200)
            .body("data[0].shopId", equalTo(dummyShopId.toInt()))
            .body("data[0].itemType", equalTo("DIGITAL_BUNDLE"))
            .body("data[0].marginRate", NumberCloseTo(newRate.toDouble(), 1e-4))
    }

    @Test
    @DisplayName("Returns 404 when login_user_uid attribute is missing")
    @TestSecurity(user = "commission-test-user", roles = [UserRole.BIZ_VALUE])
    @TestTransaction
    fun testUpdateShopMarginRate_NoUid_Returns404() {
        val body = "{\"marginRates\":[{\"itemType\":\"DIGITAL_BUNDLE\",\"marginRate\":0.2}]}"

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(body)
            .`when`()
            .put(
                "/console/commission-control/creator/${testUser.accountIdentity}/shop/$dummyShopId"
            )
            .then()
            .statusCode(404)
    }

    @Test
    @DisplayName("Returns 400 when itemType is invalid")
    @TestSecurity(
        user = "commission-test-user",
        roles = [UserRole.BIZ_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "commission-test-user")],
    )
    @TestTransaction
    fun testUpdateShopMarginRate_InvalidItemType_Returns400() {
        val body = "{\"marginRates\":[{\"itemType\":\"UNKNOWN\",\"marginRate\":0.2}]}"

        `when`(
                commissionControlController.updateShopItemTypeMarginRates(
                    testUser.accountIdentity!!,
                    dummyShopId,
                    listOf(ShopItemTypeMarginRateParam("UNKNOWN", 0.2f)),
                    "commission-test-user",
                    ConsoleUserRole.BIZ,
                )
            )
            .thenAnswer { throw InvalidParameterException("Invalid itemType") }

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(body)
            .`when`()
            .put(
                "/console/commission-control/creator/${testUser.accountIdentity}/shop/$dummyShopId"
            )
            .then()
            .statusCode(400)
    }

    @Test
    @DisplayName("SUPER can get all shop items")
    @TestSecurity(
        user = "super",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super")],
    )
    fun testGetAllShopItems_Super() {
        val dto =
            AllShopItemsResponse(
                shop = mock(GetShop.ShopForGetShop::class.java),
                items = emptyList<Item>(),
            )
        `when`(commissionControlController.getAllShopItems("creator")).thenReturn(dto)

        given()
            .`when`()
            .get("/console/commission-control/creator/creator/shop/items")
            .then()
            .statusCode(200)
    }

    @Test
    @DisplayName("Returns 403 when unauthorized")
    @TestSecurity(
        user = "agent1",
        roles = [UserRole.AGENT_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "agent1")],
    )
    fun testGetAllShopItems_Unauthorized() {
        `when`(commissionControlController.getAllShopItems("creator")).thenAnswer {
            throw ForbiddenAccessException()
        }

        given()
            .`when`()
            .get("/console/commission-control/creator/creator/shop/items")
            .then()
            .statusCode(403)
    }

    @Test
    @DisplayName("Returns 404 when creator not found")
    @TestSecurity(
        user = "super",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super")],
    )
    fun testGetAllShopItems_NotFound() {
        `when`(commissionControlController.getAllShopItems("nonexist")).thenAnswer {
            throw ResourceNotFoundException("Creator")
        }

        given()
            .`when`()
            .get("/console/commission-control/creator/nonexist/shop/items")
            .then()
            .statusCode(404)
    }
}

// matcher moved to shared test util
