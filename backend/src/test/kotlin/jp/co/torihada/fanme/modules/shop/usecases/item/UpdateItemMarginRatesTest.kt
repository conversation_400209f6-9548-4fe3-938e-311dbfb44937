package jp.co.torihada.fanme.modules.shop.usecases.item

import com.github.michaelbull.result.getOrThrow
import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.shop.factories.ItemFactory
import jp.co.torihada.fanme.modules.shop.factories.ShopFactory
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.usecases.TestResourceLifecycleManager
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

@QuarkusTest
@QuarkusTestResource(TestResourceLifecycleManager::class)
class UpdateItemMarginRatesTest {

    @Inject lateinit var updateItemMarginRates: UpdateItemMarginRates

    @Test
    @DisplayName("should update margin rates for multiple items")
    @TestTransaction
    fun testUpdateItemMarginRates() {
        val shop = ShopFactory.new(creatorUid = "bulk-rate-test").apply { persistAndFlush() }
        val item1: Item = ItemFactory.new(shop.id!!, marginRate = 0.1f).apply { persistAndFlush() }
        val item2: Item = ItemFactory.new(shop.id!!, marginRate = 0.15f).apply { persistAndFlush() }

        val newRates = listOf(item1.id!! to 0.25f, item2.id!! to 0.3f)

        val updated =
            updateItemMarginRates.execute(UpdateItemMarginRates.Input(newRates)).getOrThrow()

        val updated1 = updated.first { it.itemId == item1.id }
        val updated2 = updated.first { it.itemId == item2.id }

        assertEquals(0.25f, updated1.marginRate)
        assertEquals(0.3f, updated2.marginRate)
    }

    @Test
    @DisplayName("should throw InvalidParameterException for rate out of range")
    @TestTransaction
    fun testUpdateItemMarginRates_InvalidRate() {
        val exception =
            assertThrows(Exception::class.java) {
                updateItemMarginRates
                    .execute(UpdateItemMarginRates.Input(listOf(1L to 1.5f)))
                    .getOrThrow()
            }
        assertEquals(
            "marginRate must be between 0.0 and 1.0",
            exception.message?.substringAfter(": "),
        )
    }
}
