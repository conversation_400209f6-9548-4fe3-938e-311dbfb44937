package jp.co.torihada.fanme.endpoints.console

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.SecurityAttribute
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.transaction.Transactional
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.factories.ConsoleUserFactory
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.fanme.factories.AuditGroupFactory
import jp.co.torihada.fanme.modules.fanme.factories.AuditObjectFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.*
import jp.co.torihada.fanme.modules.fanme.models.AuditGroupMetadata
import jp.co.torihada.fanme.modules.fanme.models.AuditObject
import jp.co.torihada.fanme.modules.fanme.models.AuditObject.AssetType
import jp.co.torihada.fanme.modules.fanme.models.User
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.*

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AuditStatusEndpointTest {
    private var auditGroupId: Long = 0
    private var shopId: Long = 0

    @BeforeAll
    @Transactional
    fun setupAll() {
        val user = UserFactory.createTestUser("audit-user-1", "Audit User", "<EMAIL>")!!
        val group =
            AuditGroupFactory.new(
                userUid = user.uid!!,
                auditType = AuditType.SHOP,
                operationType = OperationType.INSERT,
                metadata = AuditGroupMetadata(shopId = 1L),
            )
        group.persistAndFlush()
        auditGroupId = group.id!!
        AuditObjectFactory.new(group.id!!, "bucket", "path", AssetType.IMAGE).persistAndFlush()

        val testUserDatas =
            listOf(
                Triple("test-super-audit-status", "Test Super User", UserRole.SUPER_VALUE),
                Triple("test-biz-audit-status", "Test Biz User", UserRole.BIZ_VALUE),
                Triple("test-agent-audit-status", "Test Agent User", UserRole.AGENT_VALUE),
            )

        val testUsers =
            testUserDatas.map { (uid, name, role) ->
                val user = UserFactory.createTestUser(uid, name, "$<EMAIL>")!!
                user.persistAndFlush()
                ConsoleUserFactory.new(user.id!!, null, role).persistAndFlush()
                user
            }
    }

    @BeforeEach
    @Transactional
    fun resetAuditGroupStatus() {
        val auditGroup = AuditGroup.findById(auditGroupId)
        auditGroup?.apply {
            status = AuditStatus.UNAUDITED
            comment = null
            auditedUserUid = null
            auditedAt = null
            persistAndFlush()
        }
    }

    @AfterAll
    @Transactional
    fun cleanupAll() {
        AuditObject.deleteAll()
        AuditGroup.deleteAll()
        ConsoleUser.deleteAll()
        User.deleteAll()
    }

    @Test
    @DisplayName("監査ステータスを更新できること")
    @TestSecurity(
        user = "test-super-audit-status",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "test-super-audit-status")],
    )
    fun auditStatusCanBeUpdated() {
        val requestBody =
            mapOf(
                "status" to AuditStatus.APPROVED.value,
                "comment" to "ok",
                "auditedUserUid" to "admin",
            )

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(requestBody)
            .`when`()
            .put("/console/audit-groups/${auditGroupId}/status")
            .then()
            .statusCode(200)
            .body("data.result", equalTo(true))

        val updated = AuditGroup.findById(auditGroupId)
        assertThat(updated?.status, equalTo(AuditStatus.APPROVED))
    }

    @Test
    @DisplayName("監査ステータスを-1で更新できること")
    @TestSecurity(
        user = "test-super-audit-status",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "test-super-audit-status")],
    )
    fun auditStatusCanBeUpdatedToRejected() {
        val requestBody =
            mapOf(
                "status" to AuditStatus.REJECTED.value,
                "comment" to "rejected",
                "auditedUserUid" to "admin",
            )

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(requestBody)
            .`when`()
            .put("/console/audit-groups/${auditGroupId}/status")
            .then()
            .statusCode(200)
            .body("data.result", equalTo(true))

        val updated = AuditGroup.findById(auditGroupId)
        assertThat(updated?.status, equalTo(AuditStatus.REJECTED))
    }

    @Test
    @DisplayName("BIZロールでステータスを更新できること")
    @TestSecurity(
        user = "test-biz-audit-status",
        roles = [UserRole.BIZ_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "test-biz-audit-status")],
    )
    fun bizRoleCanUpdateStatus() {
        val requestBody =
            mapOf(
                "status" to AuditStatus.APPROVED.value,
                "comment" to "ok from biz",
                "auditedUserUid" to "biz-user",
            )

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(requestBody)
            .`when`()
            .put("/console/audit-groups/${auditGroupId}/status")
            .then()
            .statusCode(200)
            .body("data.result", equalTo(true))

        val updated = AuditGroup.findById(auditGroupId)
        assertThat(updated?.status, equalTo(AuditStatus.APPROVED))
        assertThat(updated?.comment, equalTo("ok from biz"))
    }

    @Test
    @DisplayName("AGENTロールではステータスを更新できないこと")
    @TestSecurity(
        user = "test-agent-audit-status",
        roles = [UserRole.AGENT_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "test-agent-audit-status")],
    )
    fun agentRoleCannotUpdateStatus() {
        val requestBody =
            mapOf(
                "status" to AuditStatus.APPROVED.value,
                "comment" to "attempt from agent",
                "auditedUserUid" to "agent-user",
            )

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(requestBody)
            .`when`()
            .put("/console/audit-groups/${auditGroupId}/status")
            .then()
            .statusCode(403)

        val notUpdated = AuditGroup.findById(auditGroupId)
        assertThat(notUpdated?.status, equalTo(AuditStatus.UNAUDITED)) // ステータスが変更されていないことを確認
    }

    @Test
    @DisplayName("認証無しの場合は401が返ること")
    fun unauthorizedRequestReturns401() {
        val requestBody = mapOf("status" to 9)
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(requestBody)
            .`when`()
            .put("/console/audit-groups/${auditGroupId}/status")
            .then()
            .statusCode(401)
    }
}
