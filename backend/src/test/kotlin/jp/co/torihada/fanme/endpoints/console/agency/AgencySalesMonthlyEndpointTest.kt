package jp.co.torihada.fanme.endpoints.console.agency

import io.quarkus.test.InjectMock
import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.SecurityAttribute
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured
import io.restassured.http.ContentType
import jakarta.transaction.Transactional
import java.time.Instant
import java.time.YearMonth
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.factories.AgencyFactory
import jp.co.torihada.fanme.modules.console.factories.ConsoleUserFactory
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.payment.controllers.MonthlySellerSalesController
import jp.co.torihada.fanme.modules.payment.controllers.TransactionController
import jp.co.torihada.fanme.modules.payment.mock.CustomArgumentMatchers
import jp.co.torihada.fanme.modules.payment.models.Transaction
import jp.co.torihada.fanme.modules.payment.usecases.monthlysales.GetMonthlySellerSales
import org.hamcrest.CoreMatchers
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.mockito.Mockito

@QuarkusTest
@DisplayName("Agency Monthly Sales Data Tests")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AgencySalesMonthlyEndpointTest {

    @InjectMock lateinit var monthlySellerSalesController: MonthlySellerSalesController

    @InjectMock lateinit var transactionController: TransactionController

    private var testAgency: Agency? = null
    private var otherAgency: Agency? = null

    @BeforeEach
    @Transactional
    fun setUp() {

        testAgency = AgencyFactory.new(name = "Integration Test Agency").apply { persistAndFlush() }

        otherAgency =
            AgencyFactory.new(name = "Other Agency for Forbidden Test").apply { persistAndFlush() }

        val user1 =
            UserFactory.createTestUser(
                uid = "test-user-1",
                name = "Test User 1",
                accountIdentity = "<EMAIL>",
            )!!
        val user2 =
            UserFactory.createTestUser(
                uid = "test-user-2",
                name = "Test User 2",
                accountIdentity = "<EMAIL>",
            )!!
        val user3 =
            UserFactory.createTestUser(
                uid = "test-user-3",
                name = "Test User 3",
                accountIdentity = "<EMAIL>",
            )!!

        listOf(user1, user2, user3).forEach { user ->
            val existingConsoleUser =
                ConsoleUser.Companion.find("user.id = ?1", user.id!!).firstResult()
            if (existingConsoleUser == null) {
                val consoleUser =
                    ConsoleUserFactory.new(
                        creatorId = user.id!!,
                        agencyId = testAgency!!.id,
                        role = ConsoleUserRole.CREATOR.value,
                    )
                consoleUser.persist()
            }
        }

        val superAdmin =
            UserFactory.createTestUser(
                uid = "super-admin-test",
                name = "Super Admin Test",
                accountIdentity = "<EMAIL>",
            )!!
        val existingSuperConsoleUser =
            ConsoleUser.Companion.find("user.id = ?1", superAdmin.id!!).firstResult()
        if (existingSuperConsoleUser == null) {
            val superConsoleUser =
                ConsoleUserFactory.new(
                    creatorId = superAdmin.id!!,
                    agencyId = null,
                    role = ConsoleUserRole.SUPER.value,
                )
            superConsoleUser.persist()
        }

        val agentUser =
            UserFactory.createTestUser(
                uid = "agent-test",
                name = "Agent Test",
                accountIdentity = "<EMAIL>",
            )!!
        val existingAgentConsoleUser =
            ConsoleUser.Companion.find("user.id = ?1", agentUser.id!!).firstResult()
        if (existingAgentConsoleUser == null) {
            val agentConsoleUser =
                ConsoleUserFactory.new(
                    creatorId = agentUser.id!!,
                    agencyId = testAgency!!.id,
                    role = ConsoleUserRole.AGENT.value,
                )
            agentConsoleUser.persist()
        }

        Mockito.`when`(
                monthlySellerSalesController.getMonthlySellerSales(
                    CustomArgumentMatchers.any(),
                    CustomArgumentMatchers.any(),
                    CustomArgumentMatchers.any(),
                )
            )
            .thenReturn(emptyList())

        Mockito.`when`(transactionController.getTransactions(CustomArgumentMatchers.any()))
            .thenReturn(emptyList())
    }

    @Test
    @TestSecurity(
        user = "super-admin-test",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-admin-test")],
    )
    @DisplayName("正常系：エージェンシーに所属するクリエイターの月次売上データが正しく返ること")
    fun shouldReturnMonthlySalesDataViaEndpointWithMockedPaymentController() {
        val currentMonth = YearMonth.now().toString().replace("-", "")
        val lastMonth = YearMonth.now().minusMonths(1).toString().replace("-", "")

        val mockMonthlySales =
            listOf(
                GetMonthlySellerSales.MonthlySellerSales(
                    sellerUserId = "test-user-1",
                    yearMonth = currentMonth,
                    sellerSalesAmount = 50000,
                ),
                GetMonthlySellerSales.MonthlySellerSales(
                    sellerUserId = "test-user-2",
                    yearMonth = currentMonth,
                    sellerSalesAmount = 70000,
                ),
                GetMonthlySellerSales.MonthlySellerSales(
                    sellerUserId = "test-user-1",
                    yearMonth = lastMonth,
                    sellerSalesAmount = 35000,
                ),
                GetMonthlySellerSales.MonthlySellerSales(
                    sellerUserId = "test-user-2",
                    yearMonth = lastMonth,
                    sellerSalesAmount = 40000,
                ),
            )

        val currentMonthInstant =
            Instant.parse(
                "${currentMonth.substring(0,4)}-${currentMonth.substring(4,6)}-15T00:00:00Z"
            )

        val mockTransaction1 = Mockito.mock(Transaction::class.java)
        Mockito.`when`(mockTransaction1.sellerUserId).thenReturn("test-user-1")
        Mockito.`when`(mockTransaction1.purchaserUserId).thenReturn("buyer-001")
        Mockito.`when`(mockTransaction1.createdAt).thenReturn(currentMonthInstant)

        val mockTransaction2 = Mockito.mock(Transaction::class.java)
        Mockito.`when`(mockTransaction2.sellerUserId).thenReturn("test-user-1")
        Mockito.`when`(mockTransaction2.purchaserUserId).thenReturn("buyer-002")
        Mockito.`when`(mockTransaction2.createdAt).thenReturn(currentMonthInstant)

        val mockTransaction3 = Mockito.mock(Transaction::class.java)
        Mockito.`when`(mockTransaction3.sellerUserId).thenReturn("test-user-2")
        Mockito.`when`(mockTransaction3.purchaserUserId).thenReturn("buyer-001")
        Mockito.`when`(mockTransaction3.createdAt).thenReturn(currentMonthInstant)

        val mockTransactions = listOf(mockTransaction1, mockTransaction2, mockTransaction3)

        Mockito.`when`(
                monthlySellerSalesController.getMonthlySellerSales(
                    CustomArgumentMatchers.any(),
                    CustomArgumentMatchers.any(),
                    CustomArgumentMatchers.any(),
                )
            )
            .thenReturn(mockMonthlySales)

        Mockito.`when`(
                transactionController.getSellerTransactions(
                    CustomArgumentMatchers.any(),
                    CustomArgumentMatchers.any(),
                    CustomArgumentMatchers.any(),
                    CustomArgumentMatchers.any(),
                    CustomArgumentMatchers.any(),
                )
            )
            .thenAnswer { invocation ->
                val fromYearMonth = invocation.getArgument<String?>(1)
                val toYearMonth = invocation.getArgument<String?>(2)
                if (fromYearMonth == lastMonth && toYearMonth == currentMonth) {
                    mockTransactions
                } else {
                    emptyList<Transaction>()
                }
            }

        val currentTestAgency = testAgency!!

        val response =
            RestAssured.given()
                .contentType(ContentType.JSON)
                .queryParam("from", lastMonth)
                .queryParam("to", currentMonth)
                .`when`()
                .get("/console/agencies/${currentTestAgency.id}/sales/monthly")

        response
            .then()
            .statusCode(200)
            .body("data", CoreMatchers.notNullValue())
            .body("data.agencyMonthlySalesList", CoreMatchers.notNullValue())
            .body("data.agencyMonthlySalesList.size()", CoreMatchers.equalTo(2))
            .body("data.agencyMonthlySalesList[0].yearMonth", CoreMatchers.equalTo(currentMonth))
            .body("data.agencyMonthlySalesList[0].totalSalesAmount", CoreMatchers.equalTo(120000))
            .body("data.agencyMonthlySalesList[0].totalPurchaserCount", CoreMatchers.equalTo(2))
            .body("data.agencyMonthlySalesList[0].totalPurchaseCount", CoreMatchers.equalTo(3))
            .body("data.agencyMonthlySalesList[1].yearMonth", CoreMatchers.equalTo(lastMonth))
            .body("data.agencyMonthlySalesList[1].totalSalesAmount", CoreMatchers.equalTo(75000))
            .body("data.agencyMonthlySalesList[1].totalPurchaserCount", CoreMatchers.equalTo(0))
            .body("data.agencyMonthlySalesList[1].totalPurchaseCount", CoreMatchers.equalTo(0))
    }

    @Test
    @TestSecurity(
        user = "agent-test",
        roles = [UserRole.AGENT_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "agent-test")],
    )
    @DisplayName("Agencyロールで、自分の事務所に所属していないCREATORロールの月次売上は取得できないこと")
    fun shouldNotAllowAgencyRoleToAccessOtherAgencyMonthlySales() {
        val currentOtherAgency = otherAgency!!
        val currentMonth = YearMonth.now().toString().replace("-", "")

        val response =
            RestAssured.given()
                .contentType(ContentType.JSON)
                .queryParam("from", currentMonth)
                .queryParam("to", currentMonth)
                .`when`()
                .get("/console/agencies/${currentOtherAgency.id}/sales/monthly")

        response.then().statusCode(403)
    }
}
