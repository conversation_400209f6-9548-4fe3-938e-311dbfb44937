package jp.co.torihada.fanme.endpoints.console

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.transaction.Transactional
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.fanme.factories.AuditGroupFactory
import jp.co.torihada.fanme.modules.fanme.factories.AuditObjectFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditType
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.OperationType
import jp.co.torihada.fanme.modules.fanme.models.AuditGroupMetadata
import jp.co.torihada.fanme.modules.fanme.models.AuditObject
import jp.co.torihada.fanme.modules.fanme.models.AuditObject.AssetType
import org.hamcrest.CoreMatchers.equalTo
import org.junit.jupiter.api.*

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AuditGroupEndpointTest {
    @BeforeAll
    @Transactional
    fun setupAll() {
        val user1 =
            UserFactory.createTestUser("audit-user-1", "Audit User 1", "<EMAIL>")!!
        val group1 =
            AuditGroupFactory.new(
                userUid = user1.uid!!,
                auditType = AuditType.SHOP,
                operationType = OperationType.INSERT,
                metadata = AuditGroupMetadata(shopId = 1L),
            )
        group1.persistAndFlush()
        AuditObjectFactory.new(group1.id!!, "bucket1", "path1", AssetType.IMAGE).persistAndFlush()

        val user2 =
            UserFactory.createTestUser("audit-user-2", "Audit User 2", "<EMAIL>")!!
        val group2 =
            AuditGroupFactory.new(
                userUid = user2.uid!!,
                auditType = AuditType.SHOP_ITEM,
                operationType = OperationType.INSERT,
                itemId = 1L,
                metadata = AuditGroupMetadata(shopId = 2L),
            )
        group2.persistAndFlush()
        AuditObjectFactory.new(group2.id!!, "bucket2", "path2", AssetType.MOVIE).persistAndFlush()

        val group3 =
            AuditGroupFactory.new(
                userUid = user2.uid!!,
                auditType = AuditType.SHOP_ITEM,
                operationType = OperationType.INSERT,
                itemId = 2L,
                metadata = AuditGroupMetadata(shopId = 2L),
            )
        group3.persistAndFlush()
        AuditObjectFactory.new(group3.id!!, "bucket3", "path3", AssetType.VOICE).persistAndFlush()

        // group２の更新
        val group4 =
            AuditGroupFactory.new(
                userUid = user2.uid!!,
                auditType = AuditType.SHOP_ITEM,
                operationType = OperationType.UPDATE,
                itemId = 1L,
                metadata = AuditGroupMetadata(shopId = 2L),
            )
        group4.persistAndFlush()
        AuditObjectFactory.new(group4.id!!, "bucket4", "path4", AssetType.MOVIE).persistAndFlush()

        // group1の更新
        val group5 =
            AuditGroupFactory.new(
                userUid = user1.uid!!,
                auditType = AuditType.SHOP,
                operationType = OperationType.UPDATE,
                metadata = AuditGroupMetadata(shopId = 1L),
            )
        group5.persistAndFlush()
        AuditObjectFactory.new(group5.id!!, "bucket5", "path5", AssetType.IMAGE).persistAndFlush()

        val user3 =
            UserFactory.createTestUser("audit-user-3", "Audit User 3", "<EMAIL>")!!
        val group6 =
            AuditGroupFactory.new(
                userUid = user3.uid!!,
                auditType = AuditType.SHOP_ITEM,
                operationType = OperationType.INSERT,
                itemId = 3L,
                metadata = AuditGroupMetadata(shopId = 3L),
            )
        group6.persistAndFlush()
        AuditObjectFactory.new(group6.id!!, "bucket6", "path6", AssetType.MOVIE).persistAndFlush()
    }

    @AfterAll
    @Transactional
    fun cleanupAll() {
        AuditObject.deleteAll()
        AuditGroup.deleteAll()
    }

    @Test
    @DisplayName("SUPERロールで監査グループ一覧を取得できること")
    @TestSecurity(user = "test-super", roles = [UserRole.SUPER_VALUE])
    fun superRoleCanGetAuditGroupList() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/audit-groups")
            .then()
            .statusCode(200)
            .body("data.auditGroups.size()", equalTo(4))
            .body("data.auditGroups[0].user.name", equalTo("Audit User 3"))
            .body("data.auditGroups[1].user.name", equalTo("Audit User 1"))
            .body("data.auditGroups[2].user.name", equalTo("Audit User 2"))
            .body("data.auditGroups[2].item_id", equalTo(1))
            .body("data.auditGroups[3].user.name", equalTo("Audit User 2"))
            .body("data.auditGroups[3].item_id", equalTo(2))
    }

    @Test
    @DisplayName("クエリパラメータ top, skip, count を指定して監査グループ一覧を取得できること")
    @TestSecurity(user = "test-super", roles = [UserRole.SUPER_VALUE])
    fun getAuditGroupsWithTopSkipCount() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .queryParam("\$top", 3)
            .queryParam("\$skip", 3)
            .queryParam("\$count", true)
            .`when`()
            .get("/console/audit-groups")
            .then()
            .statusCode(200)
            .body("data.auditGroups.size()", equalTo(1))
            .body("data.totalCount", equalTo(4))
    }

    @Test
    @DisplayName("BIZロールで監査グループ一覧を取得できること")
    @TestSecurity(user = "test-biz", roles = [UserRole.BIZ_VALUE])
    fun bizRoleCanGetAuditGroupList() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/audit-groups")
            .then()
            .statusCode(200)
            .body("data.auditGroups.size()", equalTo(4))
    }

    @Test
    @DisplayName("AGENTロールでは監査グループ一覧にアクセスできないこと")
    @TestSecurity(user = "test-agent", roles = [UserRole.AGENT_VALUE])
    fun agentRoleCannotAccessAuditGroupList() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/audit-groups")
            .then()
            .statusCode(403)
    }

    @Test
    @DisplayName("認証無しの場合は401エラーが返されること")
    fun unauthorizedRequestReturns401() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/audit-groups")
            .then()
            .statusCode(401)
    }
}
