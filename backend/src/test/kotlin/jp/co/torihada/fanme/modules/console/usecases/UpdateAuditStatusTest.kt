package jp.co.torihada.fanme.modules.console.usecases

import io.quarkus.test.junit.QuarkusTest
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupController
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupControllerUpdateStatusInput
import jp.co.torihada.fanme.modules.fanme.factories.AuditGroupFactory
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.*
import jp.co.torihada.fanme.modules.fanme.models.AuditGroupMetadata
import jp.co.torihada.fanme.modules.shop.controllers.ItemController
import jp.co.torihada.fanme.modules.shop.controllers.ShopController
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class UpdateAuditStatusTest {
    @InjectMocks private lateinit var updateAuditStatus: UpdateAuditStatus

    @Mock private lateinit var auditGroupController: AuditGroupController
    @Mock private lateinit var itemController: ItemController
    @Mock private lateinit var shopController: ShopController

    @BeforeEach
    fun setUp() {
        MockitoAnnotations.openMocks(this)
    }

    @Test
    @DisplayName("SHOPタイプの監査グループが却下された時にshopController.rejectShopが呼び出されること")
    fun testShopControllerRejectShopIsCalledWhenShopAuditGroupIsRejected() {

        // AuditGroupFactoryを使ってAuditGroupを作成
        val auditGroup =
            AuditGroupFactory.new(
                userUid = "test-user",
                auditType = AuditType.SHOP,
                operationType = OperationType.INSERT,
                metadata = AuditGroupMetadata(shopId = 1L),
            )

        Mockito.`when`(
                auditGroupController.updateAuditStatus(
                    AuditGroupControllerUpdateStatusInput(
                        auditGroupId = 1L,
                        status = AuditStatus.REJECTED,
                        comment = "shop rejected",
                        auditedUserUid = "admin",
                    )
                )
            )
            .thenReturn(auditGroup)

        val input =
            UpdateAuditStatus.Input(
                auditGroupId = 1L,
                status = AuditStatus.REJECTED,
                comment = "shop rejected",
                auditedUserUid = "admin",
            )

        updateAuditStatus.execute(input)

        Mockito.verify(shopController).rejectShop(1L, "shop rejected")
    }

    @Test
    @DisplayName("SHOP_ITEMタイプの監査グループが却下された時にitemController.rejectItemが呼び出されること")
    fun testItemControllerRejectItemIsCalledWhenShopItemAuditGroupIsRejected() {
        // AuditGroupFactoryを使ってAuditGroupを作成
        val auditGroup =
            AuditGroupFactory.new(
                userUid = "test-user",
                auditType = AuditType.SHOP_ITEM,
                operationType = OperationType.INSERT,
                metadata = AuditGroupMetadata(),
                itemId = 1L,
            )

        Mockito.`when`(
                auditGroupController.updateAuditStatus(
                    AuditGroupControllerUpdateStatusInput(
                        auditGroupId = 1L,
                        status = AuditStatus.REJECTED,
                        comment = "item rejected",
                        auditedUserUid = "admin",
                    )
                )
            )
            .thenReturn(auditGroup)

        val input =
            UpdateAuditStatus.Input(
                auditGroupId = 1L,
                status = AuditStatus.REJECTED,
                comment = "item rejected",
                auditedUserUid = "admin",
            )

        updateAuditStatus.execute(input)

        Mockito.verify(itemController).rejectItem(1L, "item rejected")
    }
}
