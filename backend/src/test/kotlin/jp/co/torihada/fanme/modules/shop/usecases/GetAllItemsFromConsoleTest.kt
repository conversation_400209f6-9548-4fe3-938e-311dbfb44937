package jp.co.torihada.fanme.modules.shop.usecases

import com.github.michaelbull.result.getOrElse
import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.shop.factories.ItemFactory
import jp.co.torihada.fanme.modules.shop.factories.ShopFactory
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.usecases.item.GetAllItemsFromConsole
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

@QuarkusTest
@QuarkusTestResource(TestResourceLifecycleManager::class)
class GetAllItemsFromConsoleTest {

    @Inject lateinit var getAllItemsFromConsole: GetAllItemsFromConsole

    @Test
    @DisplayName("should return all items with their margin rates")
    @TestTransaction
    fun testGetAllItemsFromConsole() {
        val shop = ShopFactory.new(creatorUid = "margin-rate-test").apply { persistAndFlush() }
        val item1: Item = ItemFactory.new(shop.id!!, marginRate = 0.1f).apply { persistAndFlush() }
        val item2: Item = ItemFactory.new(shop.id!!, marginRate = 0.15f).apply { persistAndFlush() }
        val item3: Item = ItemFactory.new(shop.id!!, marginRate = 0.2f).apply { persistAndFlush() }

        val items = getAllItemsFromConsole.execute(shop.id!!).getOrElse { throw it }

        assertEquals(3, items.size)

        val retrievedItem1 = items.find { it.id == item1.id }
        val retrievedItem2 = items.find { it.id == item2.id }
        val retrievedItem3 = items.find { it.id == item3.id }

        assertEquals(0.1f, retrievedItem1?.marginRate)
        assertEquals(0.15f, retrievedItem2?.marginRate)
        assertEquals(0.2f, retrievedItem3?.marginRate)
    }

    @Test
    @DisplayName("should return empty list when no items exist for the creator")
    @TestTransaction
    fun testGetItemsMarginRate_NoItems() {
        val testCreatorUid = "test-creator-uid-empty"
        val testShop = ShopFactory.new(creatorUid = testCreatorUid).apply { persistAndFlush() }

        val items = getAllItemsFromConsole.execute(testShop.id!!).getOrElse { throw it }

        assertTrue(items.isEmpty())
    }
}
