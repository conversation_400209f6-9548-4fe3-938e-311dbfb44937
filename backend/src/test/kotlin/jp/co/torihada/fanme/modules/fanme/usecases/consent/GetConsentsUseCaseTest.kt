package jp.co.torihada.fanme.modules.fanme.usecases.consent

import com.github.michaelbull.result.unwrap
import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import java.time.Instant
import jp.co.torihada.fanme.modules.fanme.factories.UserConsentFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.UserConsent
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertNotNull

@QuarkusTest
class GetConsentsUseCaseTest {

    @Inject private lateinit var getConsentsUseCase: GetConsentsUseCase

    @Test
    @TestTransaction
    fun `test get required consents for user created before cutoff date`() {
        val user =
            UserFactory.new(
                    uid = "test-user-old",
                    name = "Test User Old",
                    accountIdentity = "<EMAIL>",
                )
                .apply {
                    createdAt = Instant.parse("2025-07-30T10:00:00Z")
                    persistAndFlush()
                }

        val input = GetConsentsUseCase.Input(user = user)

        val result = getConsentsUseCase.execute(input)

        assertTrue(result.isOk)
        val output = result.unwrap()
        assertNotNull(output)

        val termsConsent =
            output.consents.find { it.name == UserConsent.Consent.TERMS_20250801.name }
        assertNotNull(termsConsent)
        assertEquals(true, termsConsent.required)
    }

    @Test
    @TestTransaction
    fun `test get not required consents for user created after cutoff date`() {
        val user =
            UserFactory.new(
                    uid = "test-user-new",
                    name = "Test User New",
                    accountIdentity = "<EMAIL>",
                )
                .apply {
                    createdAt = Instant.parse("2025-08-01T10:00:00Z")
                    persistAndFlush()
                }

        val input = GetConsentsUseCase.Input(user = user)

        val result = getConsentsUseCase.execute(input)

        assertTrue(result.isOk)
        val output = result.unwrap()
        assertNotNull(output)

        val termsConsent =
            output.consents.find { it.name == UserConsent.Consent.TERMS_20250801.name }
        assertNotNull(termsConsent)
        assertEquals(false, termsConsent.required)
    }

    @Test
    @TestTransaction
    fun `test get required consents excludes already given consents`() {
        val user =
            UserFactory.new(
                    uid = "test-user-with-consent",
                    name = "Test User With Consent",
                    accountIdentity = "<EMAIL>",
                )
                .apply {
                    createdAt = Instant.parse("2025-07-30T10:00:00Z")
                    persistAndFlush()
                }

        UserConsentFactory.createTestUserConsent(
            user = user,
            consent = UserConsent.Consent.TERMS_20250801,
        )

        val input = GetConsentsUseCase.Input(user = user)

        val result = getConsentsUseCase.execute(input)

        assertTrue(result.isOk)
        val output = result.unwrap()
        assertNotNull(output)

        val termsConsent =
            output.consents.find { it.name == UserConsent.Consent.TERMS_20250801.name }
        assertNotNull(termsConsent)
        assertEquals(false, termsConsent.required)
    }
}
