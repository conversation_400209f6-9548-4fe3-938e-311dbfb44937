package jp.co.torihada.fanme.modules.fanme.factories

import jp.co.torihada.fanme.modules.fanme.models.NotificationSetting

object NotificationSettingFactory {

    fun new(creatorUid: String = "test-creator-uid", enabled: Boolean = true): NotificationSetting {
        return NotificationSetting().apply {
            this.creatorUid = creatorUid
            this.enabled = enabled
        }
    }

    fun createTestNotificationSetting(
        creatorUid: String = "test-creator-uid",
        enabled: Boolean = true,
    ): NotificationSetting {
        val notificationSetting = new(creatorUid, enabled)
        notificationSetting.persist()

        return notificationSetting
    }
}
