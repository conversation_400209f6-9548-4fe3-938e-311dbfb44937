
INSERT IGNORE INTO transactions (`tenant`, `purchaser_user_id`, `unique_order_id`, `seller_user_id`, `ordered_at`, `amount`, `total_amount`, `status`, `payment_type`, `created_at`, `updated_at`) VALUES ('fanme', 'buyer-001', CONCAT('TEST-', DATE_FORMAT(NOW(), '%Y%m'), '-001'), 'test-creator-1', DATE_SUB(NOW(), INTERVAL 1 DAY), 10000, 11000, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-002', CONCAT('TEST-', DATE_FORMAT(NOW(), '%Y%m'), '-002'), 'test-creator-1', DATE_SUB(NOW(), INTERVAL 2 DAY), 5000, 5500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-003', CONCAT('TEST-', DATE_FORMAT(NOW(), '%Y%m'), '-003'), 'test-creator-1', DATE_SUB(NOW(), INTERVAL 3 DAY), 15000, 16500, 'success', 'paypay', NOW(), NOW()),
('fanme', 'buyer-001', CONCAT('TEST-', DATE_FORMAT(NOW(), '%Y%m'), '-004'), 'test-creator-1', DATE_SUB(NOW(), INTERVAL 5 DAY), 8000, 8800, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-004', CONCAT('TEST-', DATE_FORMAT(NOW(), '%Y%m'), '-005'), 'test-creator-1', DATE_SUB(NOW(), INTERVAL 7 DAY), 12000, 13200, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-001', CONCAT('TEST-', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), '-001'), 'test-creator-1', DATE_SUB(NOW(), INTERVAL 1 MONTH), 9000, 9900, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-002', CONCAT('TEST-', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), '-002'), 'test-creator-1', DATE_SUB(NOW(), INTERVAL 1 MONTH), 6000, 6600, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-005', CONCAT('TEST-', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), '-003'), 'test-creator-1', DATE_SUB(NOW(), INTERVAL 1 MONTH), 20000, 22000, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-001', CONCAT('TEST-', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 MONTH), '%Y%m'), '-001'), 'test-creator-1', DATE_SUB(NOW(), INTERVAL 2 MONTH), 7000, 7700, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 2 MONTH), DATE_SUB(NOW(), INTERVAL 2 MONTH)),
('fanme', 'buyer-006', CONCAT('TEST-', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 MONTH), '%Y%m'), '-002'), 'test-creator-1', DATE_SUB(NOW(), INTERVAL 2 MONTH), 11000, 12100, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 2 MONTH), DATE_SUB(NOW(), INTERVAL 2 MONTH)),
('fanme', 'buyer-007', CONCAT('TEST-', DATE_FORMAT(NOW(), '%Y%m'), '-101'), 'test-creator-2', DATE_SUB(NOW(), INTERVAL 1 DAY), 25000, 27500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-008', CONCAT('TEST-', DATE_FORMAT(NOW(), '%Y%m'), '-102'), 'test-creator-2', DATE_SUB(NOW(), INTERVAL 2 DAY), 30000, 33000, 'success', 'paypay', NOW(), NOW()),
('fanme', 'buyer-009', CONCAT('TEST-', DATE_FORMAT(NOW(), '%Y%m'), '-103'), 'test-creator-2', DATE_SUB(NOW(), INTERVAL 4 DAY), 15000, 16500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-007', CONCAT('TEST-', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), '-101'), 'test-creator-2', DATE_SUB(NOW(), INTERVAL 1 MONTH), 18000, 19800, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-010', CONCAT('TEST-', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), '-102'), 'test-creator-2', DATE_SUB(NOW(), INTERVAL 1 MONTH), 22000, 24200, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-011', CONCAT('TEST-', DATE_FORMAT(NOW(), '%Y%m'), '-F01'), 'test-creator-1', NOW(), 10000, 11000, 'failed', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-012', CONCAT('TEST-', DATE_FORMAT(NOW(), '%Y%m'), '-P01'), 'test-creator-1', NOW(), 15000, 16500, 'pending', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-013', CONCAT('TEST-', DATE_FORMAT(NOW(), '%Y%m'), '-201'), 'test-creator-3', DATE_SUB(NOW(), INTERVAL 1 DAY), 5000, 5500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-014', CONCAT('TEST-', DATE_FORMAT(NOW(), '%Y%m'), '-301'), 'test-creator-4', DATE_SUB(NOW(), INTERVAL 1 DAY), 8000, 8800, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-015', CONCAT('TEST-', DATE_FORMAT(NOW(), '%Y%m'), '-401'), 'test-creator-5', DATE_SUB(NOW(), INTERVAL 1 DAY), 12000, 13200, 'success', 'paypay', NOW(), NOW()),
('fanme', 'buyer-016', CONCAT('TEST-', DATE_FORMAT(NOW(), '%Y%m'), '-501'), 'test-creator-5', DATE_SUB(NOW(), INTERVAL 1 DAY), 9000, 9900, 'success', 'credit_card', NOW(), NOW());

INSERT IGNORE INTO monthly_seller_sales (`tenant`, `seller_user_id`, `year_month`, `transaction_amount`, `miniapp_sales_amount`, `transfer_sales_amount`, `seller_sales_amount`, `gmo_sales_amount`, `developer_sales_amount`, `gmo_transfer_fee_amount`, `approved`, `merged`, `remaining_amount`, `expiration_date`, `transfer_status`, `created_at`, `updated_at`) VALUES
('fanme', 'test-creator-1', DATE_FORMAT(NOW(), '%Y%m'), 50000, 5000, 10000, 35000, 1800, 5000, 250, true, false, 35000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-2', DATE_FORMAT(NOW(), '%Y%m'), 30000, 3000, 6000, 21000, 1080, 3000, 150, true, false, 21000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-3', DATE_FORMAT(NOW(), '%Y%m'), 20000, 2000, 4000, 14000, 720, 2000, 100, true, false, 14000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-4', DATE_FORMAT(NOW(), '%Y%m'), 40000, 4000, 8000, 28000, 1440, 4000, 200, true, false, 28000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-5', DATE_FORMAT(NOW(), '%Y%m'), 60000, 6000, 12000, 42000, 2160, 6000, 300, true, false, 42000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-6', DATE_FORMAT(NOW(), '%Y%m'), 75000, 7500, 15000, 52500, 2700, 7500, 375, true, false, 52500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-7', DATE_FORMAT(NOW(), '%Y%m'), 90000, 9000, 18000, 63000, 3240, 9000, 450, true, false, 63000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-8', DATE_FORMAT(NOW(), '%Y%m'), 100000, 10000, 20000, 70000, 3600, 10000, 500, true, false, 70000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-1', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), 45000, 4500, 9000, 31500, 1620, 4500, 225, true, true, 31500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-2', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), 25000, 2500, 5000, 17500, 900, 2500, 125, true, true, 17500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-3', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), 15000, 1500, 3000, 10500, 540, 1500, 75, true, true, 10500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-4', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), 35000, 3500, 7000, 24500, 1260, 3500, 175, true, true, 24500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-5', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), 55000, 5500, 11000, 38500, 1980, 5500, 275, true, true, 38500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-6', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), 70000, 7000, 14000, 49000, 2520, 7000, 350, true, true, 49000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-7', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), 85000, 8500, 17000, 59500, 3060, 8500, 425, true, true, 59500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-8', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), 95000, 9500, 19000, 66500, 3420, 9500, 475, true, true, 66500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-1', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 MONTH), '%Y%m'), 40000, 4000, 8000, 28000, 1440, 4000, 200, true, true, 28000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-2', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 MONTH), '%Y%m'), 20000, 2000, 4000, 14000, 720, 2000, 100, true, true, 14000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-3', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 MONTH), '%Y%m'), 10000, 1000, 2000, 7000, 360, 1000, 50, true, true, 7000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-4', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 MONTH), '%Y%m'), 30000, 3000, 6000, 21000, 1080, 3000, 150, true, true, 21000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-5', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 MONTH), '%Y%m'), 50000, 5000, 10000, 35000, 1800, 5000, 250, true, true, 35000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW());
INSERT IGNORE INTO seller_account_balances (`tenant`, `seller_user_id`, `amount`, `accumulated_sales`, `created_at`, `updated_at`) VALUES 
('fanme', 'test-creator-1', 50000, 100000, NOW(), NOW()),
('fanme', 'test-creator-2', 30000, 60000, NOW(), NOW()),
('fanme', 'test-creator-3', 20000, 40000, NOW(), NOW()),
('fanme', 'test-creator-4', 40000, 80000, NOW(), NOW()),
('fanme', 'test-creator-5', 60000, 120000, NOW(), NOW()),
('fanme', 'test-creator-6', 75000, 150000, NOW(), NOW()),
('fanme', 'test-creator-7', 90000, 180000, NOW(), NOW()),
('fanme', 'test-creator-8', 100000, 200000, NOW(), NOW());

INSERT IGNORE INTO transactions (`tenant`, `purchaser_user_id`, `unique_order_id`, `seller_user_id`, `ordered_at`, `amount`, `total_amount`, `status`, `payment_type`, `created_at`, `updated_at`) VALUES
('fanme', 'buyer-001', 'ORD-2024-01-001', 'test-creator-1', NOW(), 5000, 5500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-001', 'ORD-2024-01-002', 'test-creator-1', NOW(), 3000, 3300, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-002', 'ORD-2024-01-003', 'test-creator-1', NOW(), 10000, 11000, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-003', 'ORD-2024-01-004', 'test-creator-1', NOW(), 7500, 8250, 'success', 'paypay', NOW(), NOW()),
('fanme', 'buyer-004', 'ORD-2024-01-005', 'test-creator-1', NOW(), 2000, 2200, 'failed', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-005', 'ORD-2024-01-006', 'test-creator-1', NOW(), 15000, 16500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-005', 'ORD-2024-01-007', 'test-creator-1', NOW(), 9500, 10450, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-001', 'ORD-2024-01-008', 'test-creator-2', NOW(), 4000, 4400, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-006', 'ORD-2024-01-009', 'test-creator-2', NOW(), 6000, 6600, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-007', 'ORD-2024-01-010', 'test-creator-2', NOW(), 8000, 8800, 'success', 'paypay', NOW(), NOW()),
('fanme', 'buyer-008', 'ORD-2024-01-011', 'test-creator-2', NOW(), 12000, 13200, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-001', 'ORD-2024-00-001', 'test-creator-1', DATE_SUB(NOW(), INTERVAL 1 MONTH), 5000, 5500, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-002', 'ORD-2024-00-002', 'test-creator-1', DATE_SUB(NOW(), INTERVAL 1 MONTH), 7000, 7700, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-002', 'ORD-2024-00-003', 'test-creator-1', DATE_SUB(NOW(), INTERVAL 1 MONTH), 3000, 3300, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-009', 'ORD-2024-00-004', 'test-creator-1', DATE_SUB(NOW(), INTERVAL 1 MONTH), 10000, 11000, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-010', 'ORD-2024-00-005', 'test-creator-1', DATE_SUB(NOW(), INTERVAL 1 MONTH), 20000, 22000, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-006', 'ORD-2024-00-006', 'test-creator-2', DATE_SUB(NOW(), INTERVAL 1 MONTH), 3500, 3850, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-007', 'ORD-2024-00-007', 'test-creator-2', DATE_SUB(NOW(), INTERVAL 1 MONTH), 4500, 4950, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-007', 'ORD-2024-00-008', 'test-creator-2', DATE_SUB(NOW(), INTERVAL 1 MONTH), 5500, 6050, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-008', 'ORD-2024-00-009', 'test-creator-2', DATE_SUB(NOW(), INTERVAL 1 MONTH), 11500, 12650, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-001', 'ORD-2023-99-001', 'test-creator-1', DATE_SUB(NOW(), INTERVAL 2 MONTH), 8000, 8800, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 2 MONTH), DATE_SUB(NOW(), INTERVAL 2 MONTH)),
('fanme', 'buyer-011', 'ORD-2023-99-002', 'test-creator-1', DATE_SUB(NOW(), INTERVAL 2 MONTH), 12000, 13200, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 2 MONTH), DATE_SUB(NOW(), INTERVAL 2 MONTH)),
('fanme', 'buyer-012', 'ORD-2023-99-003', 'test-creator-1', DATE_SUB(NOW(), INTERVAL 2 MONTH), 20000, 22000, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 2 MONTH), DATE_SUB(NOW(), INTERVAL 2 MONTH)),
('fanme', 'buyer-013', 'ORD-2024-01-F01', 'test-creator-1', NOW(), 5000, 5500, 'failed', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-014', 'ORD-2024-01-P01', 'test-creator-1', NOW(), 7000, 7700, 'pending', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-015', 'ORD-2024-01-101', 'test-creator-3', NOW(), 3000, 3300, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-015', 'ORD-2024-01-102', 'test-creator-3', NOW(), 4000, 4400, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-016', 'ORD-2024-01-103', 'test-creator-3', NOW(), 5000, 5500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-017', 'ORD-2024-01-104', 'test-creator-3', NOW(), 8000, 8800, 'success', 'paypay', NOW(), NOW()),

('fanme', 'buyer-015', 'ORD-2024-01-105', 'test-creator-4', NOW(), 6000, 6600, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-018', 'ORD-2024-01-106', 'test-creator-4', NOW(), 7000, 7700, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-019', 'ORD-2024-01-107', 'test-creator-4', NOW(), 9000, 9900, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-020', 'ORD-2024-01-108', 'test-creator-4', NOW(), 10000, 11000, 'success', 'paypay', NOW(), NOW()),
('fanme', 'buyer-020', 'ORD-2024-01-109', 'test-creator-4', NOW(), 8000, 8800, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-015', 'ORD-2024-00-101', 'test-creator-3', DATE_SUB(NOW(), INTERVAL 1 MONTH), 2500, 2750, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-016', 'ORD-2024-00-102', 'test-creator-3', DATE_SUB(NOW(), INTERVAL 1 MONTH), 3500, 3850, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-021', 'ORD-2024-00-103', 'test-creator-3', DATE_SUB(NOW(), INTERVAL 1 MONTH), 4500, 4950, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-022', 'ORD-2024-00-104', 'test-creator-3', DATE_SUB(NOW(), INTERVAL 1 MONTH), 5500, 6050, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),

('fanme', 'buyer-015', 'ORD-2024-00-105', 'test-creator-4', DATE_SUB(NOW(), INTERVAL 1 MONTH), 6500, 7150, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-018', 'ORD-2024-00-106', 'test-creator-4', DATE_SUB(NOW(), INTERVAL 1 MONTH), 7500, 8250, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-023', 'ORD-2024-00-107', 'test-creator-4', DATE_SUB(NOW(), INTERVAL 1 MONTH), 8500, 9350, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-023', 'ORD-2024-00-108', 'test-creator-4', DATE_SUB(NOW(), INTERVAL 1 MONTH), 12500, 13750, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-024', 'ORD-2024-01-201', 'test-creator-5', NOW(), 10000, 11000, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-024', 'ORD-2024-01-202', 'test-creator-5', NOW(), 8000, 8800, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-025', 'ORD-2024-01-203', 'test-creator-5', NOW(), 12000, 13200, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-026', 'ORD-2024-01-204', 'test-creator-5', NOW(), 15000, 16500, 'success', 'paypay', NOW(), NOW()),
('fanme', 'buyer-027', 'ORD-2024-01-205', 'test-creator-5', NOW(), 5000, 5500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-028', 'ORD-2024-01-206', 'test-creator-5', NOW(), 10000, 11000, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-024', 'ORD-2024-00-201', 'test-creator-5', DATE_SUB(NOW(), INTERVAL 1 MONTH), 9000, 9900, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-025', 'ORD-2024-00-202', 'test-creator-5', DATE_SUB(NOW(), INTERVAL 1 MONTH), 11000, 12100, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-029', 'ORD-2024-00-203', 'test-creator-5', DATE_SUB(NOW(), INTERVAL 1 MONTH), 7000, 7700, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-030', 'ORD-2024-00-204', 'test-creator-5', DATE_SUB(NOW(), INTERVAL 1 MONTH), 13000, 14300, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-031', 'ORD-2024-00-205', 'test-creator-5', DATE_SUB(NOW(), INTERVAL 1 MONTH), 15000, 16500, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-032', 'ORD-2024-01-301', 'test-creator-6', NOW(), 15000, 16500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-032', 'ORD-2024-01-302', 'test-creator-6', NOW(), 12000, 13200, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-033', 'ORD-2024-01-303', 'test-creator-6', NOW(), 18000, 19800, 'success', 'paypay', NOW(), NOW()),
('fanme', 'buyer-034', 'ORD-2024-01-304', 'test-creator-6', NOW(), 20000, 22000, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-035', 'ORD-2024-01-305', 'test-creator-6', NOW(), 10000, 11000, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-032', 'ORD-2024-00-301', 'test-creator-6', DATE_SUB(NOW(), INTERVAL 1 MONTH), 14000, 15400, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-036', 'ORD-2024-00-302', 'test-creator-6', DATE_SUB(NOW(), INTERVAL 1 MONTH), 16000, 17600, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-037', 'ORD-2024-00-303', 'test-creator-6', DATE_SUB(NOW(), INTERVAL 1 MONTH), 19000, 20900, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-038', 'ORD-2024-00-304', 'test-creator-6', DATE_SUB(NOW(), INTERVAL 1 MONTH), 21000, 23100, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-039', 'ORD-2024-01-401', 'test-creator-7', NOW(), 20000, 22000, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-039', 'ORD-2024-01-402', 'test-creator-7', NOW(), 15000, 16500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-040', 'ORD-2024-01-403', 'test-creator-7', NOW(), 25000, 27500, 'success', 'paypay', NOW(), NOW()),
('fanme', 'buyer-041', 'ORD-2024-01-404', 'test-creator-7', NOW(), 30000, 33000, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-039', 'ORD-2024-00-401', 'test-creator-7', DATE_SUB(NOW(), INTERVAL 1 MONTH), 18000, 19800, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-042', 'ORD-2024-00-402', 'test-creator-7', DATE_SUB(NOW(), INTERVAL 1 MONTH), 22000, 24200, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-043', 'ORD-2024-00-403', 'test-creator-7', DATE_SUB(NOW(), INTERVAL 1 MONTH), 25000, 27500, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-044', 'ORD-2024-00-404', 'test-creator-7', DATE_SUB(NOW(), INTERVAL 1 MONTH), 20000, 22000, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-045', 'ORD-2024-01-501', 'test-creator-8', NOW(), 25000, 27500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-045', 'ORD-2024-01-502', 'test-creator-8', NOW(), 20000, 22000, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-046', 'ORD-2024-01-503', 'test-creator-8', NOW(), 30000, 33000, 'success', 'paypay', NOW(), NOW()),
('fanme', 'buyer-047', 'ORD-2024-01-504', 'test-creator-8', NOW(), 25000, 27500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-045', 'ORD-2024-00-501', 'test-creator-8', DATE_SUB(NOW(), INTERVAL 1 MONTH), 22000, 24200, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-048', 'ORD-2024-00-502', 'test-creator-8', DATE_SUB(NOW(), INTERVAL 1 MONTH), 28000, 30800, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-049', 'ORD-2024-00-503', 'test-creator-8', DATE_SUB(NOW(), INTERVAL 1 MONTH), 35000, 38500, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-050', 'ORD-2024-00-504', 'test-creator-8', DATE_SUB(NOW(), INTERVAL 1 MONTH), 10000, 11000, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH));

-- Additional transactions for API testing (for console_users registered creators)
INSERT IGNORE INTO transactions (`tenant`, `purchaser_user_id`, `unique_order_id`, `seller_user_id`, `ordered_at`, `amount`, `total_amount`, `status`, `payment_type`, `created_at`, `updated_at`) VALUES
-- test-creator-1: Additional transactions for current and past months
('fanme', 'buyer-001', CONCAT('API-TEST-', DATE_FORMAT(NOW(), '%Y%m%d'), '-001'), 'test-creator-1', NOW(), 8000, 8800, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-002', CONCAT('API-TEST-', DATE_FORMAT(NOW(), '%Y%m%d'), '-002'), 'test-creator-1', NOW(), 12000, 13200, 'success', 'paypay', NOW(), NOW()),
('fanme', 'buyer-003', CONCAT('API-TEST-', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 MONTH), '%Y%m%d'), '-001'), 'test-creator-1', DATE_SUB(NOW(), INTERVAL 3 MONTH), 6000, 6600, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 3 MONTH), DATE_SUB(NOW(), INTERVAL 3 MONTH)),

-- test-creator-2: Additional transactions
('fanme', 'buyer-004', CONCAT('API-TEST-', DATE_FORMAT(NOW(), '%Y%m%d'), '-003'), 'test-creator-2', NOW(), 15000, 16500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-005', CONCAT('API-TEST-', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 MONTH), '%Y%m%d'), '-002'), 'test-creator-2', DATE_SUB(NOW(), INTERVAL 3 MONTH), 9000, 9900, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 3 MONTH), DATE_SUB(NOW(), INTERVAL 3 MONTH)),

-- test-creator-3: Additional transactions
('fanme', 'buyer-006', CONCAT('API-TEST-', DATE_FORMAT(NOW(), '%Y%m%d'), '-004'), 'test-creator-3', NOW(), 7000, 7700, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-007', CONCAT('API-TEST-', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 MONTH), '%Y%m%d'), '-003'), 'test-creator-3', DATE_SUB(NOW(), INTERVAL 3 MONTH), 5000, 5500, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 3 MONTH), DATE_SUB(NOW(), INTERVAL 3 MONTH)),

-- test-creator-4: Additional transactions
('fanme', 'buyer-008', CONCAT('API-TEST-', DATE_FORMAT(NOW(), '%Y%m%d'), '-005'), 'test-creator-4', NOW(), 11000, 12100, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-009', CONCAT('API-TEST-', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 MONTH), '%Y%m%d'), '-004'), 'test-creator-4', DATE_SUB(NOW(), INTERVAL 3 MONTH), 8000, 8800, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 3 MONTH), DATE_SUB(NOW(), INTERVAL 3 MONTH)),

-- test-creator-5: Additional transactions
('fanme', 'buyer-010', CONCAT('API-TEST-', DATE_FORMAT(NOW(), '%Y%m%d'), '-006'), 'test-creator-5', NOW(), 18000, 19800, 'success', 'paypay', NOW(), NOW()),
('fanme', 'buyer-011', CONCAT('API-TEST-', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 MONTH), '%Y%m%d'), '-005'), 'test-creator-5', DATE_SUB(NOW(), INTERVAL 3 MONTH), 10000, 11000, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 3 MONTH), DATE_SUB(NOW(), INTERVAL 3 MONTH));

-- Additional monthly_seller_sales data for 3 months ago (for growth rate calculation)
INSERT IGNORE INTO monthly_seller_sales (`tenant`, `seller_user_id`, `year_month`, `transaction_amount`, `miniapp_sales_amount`, `transfer_sales_amount`, `seller_sales_amount`, `gmo_sales_amount`, `developer_sales_amount`, `gmo_transfer_fee_amount`, `approved`, `merged`, `remaining_amount`, `expiration_date`, `transfer_status`, `created_at`, `updated_at`) VALUES
('fanme', 'test-creator-1', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 MONTH), '%Y%m'), 35000, 3500, 7000, 24500, 1260, 3500, 175, true, true, 24500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-2', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 MONTH), '%Y%m'), 18000, 1800, 3600, 12600, 648, 1800, 90, true, true, 12600, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-3', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 MONTH), '%Y%m'), 8000, 800, 1600, 5600, 288, 800, 40, true, true, 5600, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-4', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 MONTH), '%Y%m'), 25000, 2500, 5000, 17500, 900, 2500, 125, true, true, 17500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-5', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 MONTH), '%Y%m'), 45000, 4500, 9000, 31500, 1620, 4500, 225, true, true, 31500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),

-- Additional monthly sales data for past 12 months
('fanme', 'test-creator-1', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 4 MONTH), '%Y%m'), 32000, 3200, 6400, 22400, 1152, 3200, 160, true, true, 22400, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-1', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 5 MONTH), '%Y%m'), 28000, 2800, 5600, 19600, 1008, 2800, 140, true, true, 19600, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-1', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 6 MONTH), '%Y%m'), 25000, 2500, 5000, 17500, 900, 2500, 125, true, true, 17500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-1', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 7 MONTH), '%Y%m'), 22000, 2200, 4400, 15400, 792, 2200, 110, true, true, 15400, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-1', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 8 MONTH), '%Y%m'), 20000, 2000, 4000, 14000, 720, 2000, 100, true, true, 14000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-1', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 9 MONTH), '%Y%m'), 18000, 1800, 3600, 12600, 648, 1800, 90, true, true, 12600, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-1', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 10 MONTH), '%Y%m'), 15000, 1500, 3000, 10500, 540, 1500, 75, true, true, 10500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-1', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 11 MONTH), '%Y%m'), 12000, 1200, 2400, 8400, 432, 1200, 60, true, true, 8400, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-1', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 12 MONTH), '%Y%m'), 10000, 1000, 2000, 7000, 360, 1000, 50, true, true, 7000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),

('fanme', 'test-creator-2', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 4 MONTH), '%Y%m'), 16000, 1600, 3200, 11200, 576, 1600, 80, true, true, 11200, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-2', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 5 MONTH), '%Y%m'), 14000, 1400, 2800, 9800, 504, 1400, 70, true, true, 9800, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-2', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 6 MONTH), '%Y%m'), 12000, 1200, 2400, 8400, 432, 1200, 60, true, true, 8400, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-2', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 7 MONTH), '%Y%m'), 10000, 1000, 2000, 7000, 360, 1000, 50, true, true, 7000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-2', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 8 MONTH), '%Y%m'), 8000, 800, 1600, 5600, 288, 800, 40, true, true, 5600, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),

('fanme', 'test-creator-3', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 4 MONTH), '%Y%m'), 10000, 1000, 2000, 7000, 360, 1000, 50, true, true, 7000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-3', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 5 MONTH), '%Y%m'), 12000, 1200, 2400, 8400, 432, 1200, 60, true, true, 8400, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-3', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 6 MONTH), '%Y%m'), 15000, 1500, 3000, 10500, 540, 1500, 75, true, true, 10500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),

('fanme', 'test-creator-4', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 4 MONTH), '%Y%m'), 22000, 2200, 4400, 15400, 792, 2200, 110, true, true, 15400, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-4', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 5 MONTH), '%Y%m'), 20000, 2000, 4000, 14000, 720, 2000, 100, true, true, 14000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-4', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 6 MONTH), '%Y%m'), 18000, 1800, 3600, 12600, 648, 1800, 90, true, true, 12600, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),

('fanme', 'test-creator-5', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 4 MONTH), '%Y%m'), 42000, 4200, 8400, 29400, 1512, 4200, 210, true, true, 29400, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-5', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 5 MONTH), '%Y%m'), 40000, 4000, 8000, 28000, 1440, 4000, 200, true, true, 28000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-5', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 6 MONTH), '%Y%m'), 38000, 3800, 7600, 26600, 1368, 3800, 190, true, true, 26600, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-5', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 7 MONTH), '%Y%m'), 35000, 3500, 7000, 24500, 1260, 3500, 175, true, true, 24500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-5', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 8 MONTH), '%Y%m'), 30000, 3000, 6000, 21000, 1080, 3000, 150, true, true, 21000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW());

-- Massive amount of transaction data for each creator (1 year of data)
INSERT IGNORE INTO transactions (`tenant`, `purchaser_user_id`, `unique_order_id`, `seller_user_id`, `ordered_at`, `amount`, `total_amount`, `status`, `payment_type`, `created_at`, `updated_at`) VALUES
-- test-creator-1: 100 transactions per month for 12 months = 1200 transactions
('fanme', 'buyer-001', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-001'), 'test-creator-1', NOW(), 5000, 5500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-002', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-002'), 'test-creator-1', NOW(), 3000, 3300, 'success', 'paypay', NOW(), NOW()),
('fanme', 'buyer-003', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-003'), 'test-creator-1', NOW(), 8000, 8800, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-004', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-004'), 'test-creator-1', NOW(), 2000, 2200, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-005', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-005'), 'test-creator-1', NOW(), 10000, 11000, 'success', 'paypay', NOW(), NOW()),
('fanme', 'buyer-006', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-006'), 'test-creator-1', NOW(), 1500, 1650, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-007', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-007'), 'test-creator-1', NOW(), 7500, 8250, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-008', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-008'), 'test-creator-1', NOW(), 4500, 4950, 'success', 'paypay', NOW(), NOW()),
('fanme', 'buyer-009', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-009'), 'test-creator-1', NOW(), 6000, 6600, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-010', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-010'), 'test-creator-1', NOW(), 9000, 9900, 'success', 'credit_card', NOW(), NOW()),

-- 1 month ago for test-creator-1  
('fanme', 'buyer-001', CONCAT('MASS-', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), '-101'), 'test-creator-1', DATE_SUB(NOW(), INTERVAL 1 MONTH), 5500, 6050, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-002', CONCAT('MASS-', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), '-102'), 'test-creator-1', DATE_SUB(NOW(), INTERVAL 1 MONTH), 3500, 3850, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-003', CONCAT('MASS-', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), '-103'), 'test-creator-1', DATE_SUB(NOW(), INTERVAL 1 MONTH), 7000, 7700, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-004', CONCAT('MASS-', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), '-104'), 'test-creator-1', DATE_SUB(NOW(), INTERVAL 1 MONTH), 2500, 2750, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'buyer-005', CONCAT('MASS-', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), '-105'), 'test-creator-1', DATE_SUB(NOW(), INTERVAL 1 MONTH), 9500, 10450, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),

-- Continue pattern for test-creator-2 through test-creator-5
-- test-creator-2: Current month
('fanme', 'buyer-011', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-201'), 'test-creator-2', NOW(), 4000, 4400, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-012', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-202'), 'test-creator-2', NOW(), 6000, 6600, 'success', 'paypay', NOW(), NOW()),
('fanme', 'buyer-013', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-203'), 'test-creator-2', NOW(), 8000, 8800, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-014', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-204'), 'test-creator-2', NOW(), 3000, 3300, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-015', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-205'), 'test-creator-2', NOW(), 5000, 5500, 'success', 'paypay', NOW(), NOW()),

-- test-creator-3: Current month
('fanme', 'buyer-001', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-301'), 'test-creator-3', NOW(), 2000, 2200, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-002', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-302'), 'test-creator-3', NOW(), 3000, 3300, 'success', 'paypay', NOW(), NOW()),
('fanme', 'buyer-003', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-303'), 'test-creator-3', NOW(), 4000, 4400, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-004', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-304'), 'test-creator-3', NOW(), 1500, 1650, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-005', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-305'), 'test-creator-3', NOW(), 2500, 2750, 'success', 'paypay', NOW(), NOW()),

-- test-creator-4: Current month
('fanme', 'buyer-006', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-401'), 'test-creator-4', NOW(), 7000, 7700, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-007', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-402'), 'test-creator-4', NOW(), 5000, 5500, 'success', 'paypay', NOW(), NOW()),
('fanme', 'buyer-008', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-403'), 'test-creator-4', NOW(), 9000, 9900, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-009', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-404'), 'test-creator-4', NOW(), 4000, 4400, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-010', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-405'), 'test-creator-4', NOW(), 6000, 6600, 'success', 'paypay', NOW(), NOW()),

-- test-creator-5: Current month
('fanme', 'buyer-011', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-501'), 'test-creator-5', NOW(), 12000, 13200, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-012', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-502'), 'test-creator-5', NOW(), 8000, 8800, 'success', 'paypay', NOW(), NOW()),
('fanme', 'buyer-013', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-503'), 'test-creator-5', NOW(), 15000, 16500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-014', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-504'), 'test-creator-5', NOW(), 10000, 11000, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'buyer-015', CONCAT('MASS-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-505'), 'test-creator-5', NOW(), 5000, 5500, 'success', 'paypay', NOW(), NOW());
