INSERT INTO shops (id, tenant, creator_uid, name, description, margin_rate, tip_margin_rate, created_at, updated_at) VALUES
(1, 'fanme', 'test-creator-1', 'テストクリエイター1のショップ', 'テストショップ1の説明', 0.1, 0.15, NOW(), NOW()),
(2, 'fanme', 'test-creator-2', 'テストクリエイター2のショップ', 'テストショップ2の説明', 0.1, 0.15, NOW(), NOW()),
(3, 'fanme', 'test-creator-3', 'テストクリエイター3のショップ', 'テストショップ3の説明', 0.1, 0.15, NOW(), NOW()),
(4, 'fanme', 'test-creator-4', 'テストクリエイター4のショップ', 'テストショップ4の説明', 0.1, 0.15, NOW(), NOW()),
(5, 'fanme', 'test-creator-5', 'テストクリエイター5のショップ', 'テストショップ5の説明', 0.1, 0.15, NOW(), NOW())
ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    description = VALUES(description),
    margin_rate = VALUES(margin_rate),
    tip_margin_rate = VALUES(tip_margin_rate),
    updated_at = NOW();

INSERT INTO items (id, shop_id, name, description, item_type, thumbnail_uri, thumbnail_from, price, file_type, available, margin_rate, sort_order, created_at, updated_at) VALUES
(1, 1, 'デジタル写真集A', 'クリエイター1の限定写真集', 0, 'https://example.com/thumb1.jpg', 0, 5000, 1, true, 0.1, 1, NOW(), NOW()),
(2, 1, 'サイン入りポストカード', 'クリエイター1の直筆サイン入り', 0, 'https://example.com/thumb2.jpg', 0, 3000, 1, true, 0.1, 2, NOW(), NOW()),
(3, 1, 'オンラインファンミーティング参加権', '限定20名のファンミーティング', 0, 'https://example.com/thumb3.jpg', 0, 10000, 4, true, 0.1, 3, NOW(), NOW()),
(4, 2, 'オリジナルTシャツ', 'クリエイター2デザインのTシャツ', 0, 'https://example.com/thumb4.jpg', 0, 7500, 1, true, 0.1, 1, NOW(), NOW()),
(5, 2, 'ステッカーセット', '限定ステッカー5枚セット', 0, 'https://example.com/thumb5.jpg', 0, 2000, 1, true, 0.1, 2, NOW(), NOW()),
(6, 3, 'デジタルアートブック', 'クリエイター3の作品集', 0, 'https://example.com/thumb6.jpg', 0, 15000, 1, true, 0.1, 1, NOW(), NOW()),
(7, 3, 'メイキング動画', '制作過程を収録した動画', 0, 'https://example.com/thumb7.jpg', 0, 5000, 4, true, 0.1, 2, NOW(), NOW()),
(8, 4, 'プレミアムフォトセット', '未公開写真10枚セット', 0, 'https://example.com/thumb8.jpg', 0, 8000, 1, true, 0.1, 1, NOW(), NOW()),
(9, 4, 'バースデーメッセージ動画', 'あなたへの特別メッセージ', 0, 'https://example.com/thumb9.jpg', 0, 20000, 4, true, 0.1, 2, NOW(), NOW()),
(10, 4, 'オリジナル壁紙セット', 'スマホ・PC用壁紙', 0, 'https://example.com/thumb10.jpg', 0, 1000, 1, true, 0.1, 3, NOW(), NOW()),
(11, 5, 'プレミアムメンバーシップ', '月額制の特別コンテンツ', 0, 'https://example.com/thumb11.jpg', 0, 12000, 4, true, 0.1, 1, NOW(), NOW()),
(12, 5, 'サイン入りポスター', 'A3サイズポスター', 0, 'https://example.com/thumb12.jpg', 0, 5000, 1, true, 0.1, 2, NOW(), NOW()),
(13, 5, 'オリジナルグッズセット', 'Tシャツ・マグカップセット', 0, 'https://example.com/thumb13.jpg', 0, 10000, 1, true, 0.1, 3, NOW(), NOW())
ON DUPLICATE KEY UPDATE
    shop_id = VALUES(shop_id),
    name = VALUES(name),
    description = VALUES(description),
    price = VALUES(price),
    available = VALUES(available),
    updated_at = NOW();

INSERT INTO orders (id, purchaser_uid, shop_id, transaction_id, created_at, updated_at) VALUES
(1, 'buyer-001', 1, 1, '2025-06-06 02:29:18', '2025-06-06 02:29:18'),
(2, 'buyer-001', 1, 2, '2025-06-06 02:29:18', '2025-06-06 02:29:18'),
(3, 'buyer-002', 1, 3, '2025-06-06 02:29:18', '2025-06-06 02:29:18'),
(4, 'buyer-003', 1, 4, '2025-06-06 02:29:18', '2025-06-06 02:29:18'),
(5, 'buyer-005', 1, 5, '2025-06-06 02:29:18', '2025-06-06 02:29:18'),
(6, 'buyer-005', 1, 6, '2025-06-06 02:29:18', '2025-06-06 02:29:18'),
(7, 'buyer-001', 1, 7, '2025-05-06 02:29:18', '2025-05-06 02:29:18'),
(8, 'buyer-002', 1, 8, '2025-05-06 02:29:18', '2025-05-06 02:29:18'),
(9, 'buyer-002', 1, 9, '2025-05-06 02:29:18', '2025-05-06 02:29:18'),
(10, 'buyer-009', 1, 10, '2025-05-06 02:29:18', '2025-05-06 02:29:18'),
(11, 'buyer-010', 1, 11, '2025-05-06 02:29:18', '2025-05-06 02:29:18'),
(12, 'buyer-001', 1, 12, '2025-04-06 02:29:18', '2025-04-06 02:29:18'),
(13, 'buyer-011', 1, 13, '2025-04-06 02:29:18', '2025-04-06 02:29:18'),
(14, 'buyer-012', 1, 14, '2025-04-06 02:29:18', '2025-04-06 02:29:18')
ON DUPLICATE KEY UPDATE
    purchaser_uid = VALUES(purchaser_uid),
    shop_id = VALUES(shop_id),
    transaction_id = VALUES(transaction_id),
    updated_at = NOW();

INSERT INTO order_items (id, order_id, item_id, quantity, created_at, updated_at) VALUES
(1, 1, 1, 1, '2025-06-06 02:29:18', '2025-06-06 02:29:18'), -- 5000円 デジタル写真集A
(2, 2, 2, 1, '2025-06-06 02:29:18', '2025-06-06 02:29:18'), -- 3000円 サイン入りポストカード
(3, 3, 3, 1, '2025-06-06 02:29:18', '2025-06-06 02:29:18'), -- 10000円 オンラインファンミーティング参加権
(4, 4, 4, 1, '2025-06-06 02:29:18', '2025-06-06 02:29:18'), -- 7500円 オリジナルTシャツ
(5, 5, 6, 1, '2025-06-06 02:29:18', '2025-06-06 02:29:18'), -- 15000円 デジタルアートブック
(6, 6, 1, 1, '2025-06-06 02:29:18', '2025-06-06 02:29:18'), -- 5000円 デジタル写真集A
(7, 6, 4, 1, '2025-06-06 02:29:18', '2025-06-06 02:29:18'), -- 7500円 オリジナルTシャツ
(8, 7, 1, 1, '2025-05-06 02:29:18', '2025-05-06 02:29:18'), -- 5000円 デジタル写真集A
(9, 8, 7, 1, '2025-05-06 02:29:18', '2025-05-06 02:29:18'), -- 5000円 メイキング動画
(10, 8, 5, 1, '2025-05-06 02:29:18', '2025-05-06 02:29:18'), -- 2000円 ステッカーセット
(11, 9, 2, 1, '2025-05-06 02:29:18', '2025-05-06 02:29:18'), -- 3000円 サイン入りポストカード
(12, 10, 3, 1, '2025-05-06 02:29:18', '2025-05-06 02:29:18'), -- 10000円 オンラインファンミーティング参加権
(13, 11, 9, 1, '2025-05-06 02:29:18', '2025-05-06 02:29:18'), -- 20000円 バースデーメッセージ動画
(14, 12, 8, 1, '2025-04-06 02:29:18', '2025-04-06 02:29:18'), -- 8000円 プレミアムフォトセット
(15, 13, 6, 1, '2025-04-06 02:29:18', '2025-04-06 02:29:18'), -- 15000円 デジタルアートブック
(16, 13, 2, 1, '2025-04-06 02:29:18', '2025-04-06 02:29:18'), -- 3000円 サイン入りポストカード
(17, 14, 9, 1, '2025-04-06 02:29:18', '2025-04-06 02:29:18') -- 20000円 バースデーメッセージ動画
ON DUPLICATE KEY UPDATE
    order_id = VALUES(order_id),
    item_id = VALUES(item_id),
    quantity = VALUES(quantity),
    updated_at = NOW();

INSERT INTO purchased_items (id, purchaser_uid, item_id, order_id, quantity, price, status, created_at, updated_at, purchased_at) VALUES
(1, 'buyer-001', 1, 1, 1, 5000, 'PAYSUCCESS', '2025-06-06 02:29:18', '2025-06-06 02:29:18', '2025-06-06 02:29:18'),
(2, 'buyer-001', 2, 2, 1, 3000, 'PAYSUCCESS', '2025-06-06 02:29:18', '2025-06-06 02:29:18', '2025-06-06 02:29:18'),
(3, 'buyer-002', 3, 3, 1, 10000, 'PAYSUCCESS', '2025-06-06 02:29:18', '2025-06-06 02:29:18', '2025-06-06 02:29:18'),
(4, 'buyer-003', 4, 4, 1, 7500, 'PAYSUCCESS', '2025-06-06 02:29:18', '2025-06-06 02:29:18', '2025-06-06 02:29:18'),
(5, 'buyer-005', 6, 5, 1, 15000, 'PAYSUCCESS', '2025-06-06 02:29:18', '2025-06-06 02:29:18', '2025-06-06 02:29:18'),
(6, 'buyer-005', 1, 6, 1, 5000, 'PAYSUCCESS', '2025-06-06 02:29:18', '2025-06-06 02:29:18', '2025-06-06 02:29:18'),
(7, 'buyer-005', 4, 6, 1, 7500, 'PAYSUCCESS', '2025-06-06 02:29:18', '2025-06-06 02:29:18', '2025-06-06 02:29:18'),
(8, 'buyer-001', 1, 7, 1, 5000, 'PAYSUCCESS', '2025-05-06 02:29:18', '2025-05-06 02:29:18', '2025-05-06 02:29:18'),
(9, 'buyer-002', 7, 8, 1, 5000, 'PAYSUCCESS', '2025-05-06 02:29:18', '2025-05-06 02:29:18', '2025-05-06 02:29:18'),
(10, 'buyer-002', 5, 8, 1, 2000, 'PAYSUCCESS', '2025-05-06 02:29:18', '2025-05-06 02:29:18', '2025-05-06 02:29:18'),
(11, 'buyer-002', 2, 9, 1, 3000, 'PAYSUCCESS', '2025-05-06 02:29:18', '2025-05-06 02:29:18', '2025-05-06 02:29:18'),
(12, 'buyer-009', 3, 10, 1, 10000, 'PAYSUCCESS', '2025-05-06 02:29:18', '2025-05-06 02:29:18', '2025-05-06 02:29:18'),
(13, 'buyer-010', 9, 11, 1, 20000, 'PAYSUCCESS', '2025-05-06 02:29:18', '2025-05-06 02:29:18', '2025-05-06 02:29:18'),
(14, 'buyer-001', 8, 12, 1, 8000, 'PAYSUCCESS', '2025-04-06 02:29:18', '2025-04-06 02:29:18', '2025-04-06 02:29:18'),
(15, 'buyer-011', 6, 13, 1, 15000, 'PAYSUCCESS', '2025-04-06 02:29:18', '2025-04-06 02:29:18', '2025-04-06 02:29:18'),
(16, 'buyer-012', 9, 14, 1, 20000, 'PAYSUCCESS', '2025-04-06 02:29:18', '2025-04-06 02:29:18', '2025-04-06 02:29:18')
ON DUPLICATE KEY UPDATE
    purchaser_uid = VALUES(purchaser_uid),
    item_id = VALUES(item_id),
    order_id = VALUES(order_id),
    quantity = VALUES(quantity),
    price = VALUES(price),
    status = VALUES(status),
    purchased_at = VALUES(purchased_at),
    updated_at = NOW();