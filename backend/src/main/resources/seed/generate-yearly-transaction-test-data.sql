-- Generate massive transaction data for testing (1 year worth of data)
-- This script generates:
-- - 50-100 transactions per month per creator
-- - Random amounts between 1000-20000 yen
-- - Mix of payment types (credit_card and paypay)
-- - All transactions are successful

DELIMITER $$

DROP PROCEDURE IF EXISTS generate_mass_transactions$$
CREATE PROCEDURE generate_mass_transactions()
BEGIN
    DECLARE v_month INT DEFAULT 0;
    DECLARE v_day INT;
    DECLARE v_trans INT;
    DECLARE v_creator_index INT;
    DECLARE v_creator_uid VARCHAR(255);
    DECLARE v_buyer_index INT;
    DECLARE v_amount INT;
    DECLARE v_payment_type VARCHAR(50);
    DECLARE v_order_date DATETIME;
    DECLARE v_unique_id VARCHAR(255);
    
    -- Loop through last 12 months
    WHILE v_month < 12 DO
        -- Loop through each creator
        SET v_creator_index = 1;
        WHILE v_creator_index <= 5 DO
            SET v_creator_uid = CONCAT('test-creator-', v_creator_index);
            
            -- Generate 50-100 transactions per month per creator
            SET v_trans = 0;
            WHILE v_trans < 50 + FLOOR(RAND() * 50) DO
                -- Random day within the month
                SET v_day = 1 + FLOOR(RAND() * 28);
                SET v_order_date = DATE_SUB(DATE_SUB(NOW(), INTERVAL v_month MONTH), INTERVAL v_day DAY);
                
                -- Use buyer with pattern: creator index affects buyer selection
                -- Creator 1 uses buyers 1-20, Creator 2 uses buyers 11-30, etc.
                SET v_buyer_index = ((v_creator_index - 1) * 10) + 1 + FLOOR(RAND() * 20);
                IF v_buyer_index > 50 THEN
                    SET v_buyer_index = v_buyer_index - 50;
                END IF;
                
                -- Random amount between 1000-20000
                SET v_amount = 1000 + FLOOR(RAND() * 19000);
                SET v_amount = FLOOR(v_amount / 100) * 100; -- Round to nearest 100
                
                -- Random payment type
                IF RAND() > 0.3 THEN
                    SET v_payment_type = 'credit_card';
                ELSE
                    SET v_payment_type = 'paypay';
                END IF;
                
                -- Generate unique ID
                SET v_unique_id = CONCAT('GEN-', DATE_FORMAT(v_order_date, '%Y%m%d'), '-', 
                                        LPAD(v_creator_index, 2, '0'), 
                                        LPAD(v_trans, 4, '0'));
                
                -- Insert transaction
                INSERT IGNORE INTO transactions (
                    `tenant`, `purchaser_user_id`, `unique_order_id`, `seller_user_id`, 
                    `ordered_at`, `amount`, `total_amount`, `status`, `payment_type`, 
                    `created_at`, `updated_at`
                ) VALUES (
                    'fanme', 
                    CONCAT('buyer-', LPAD(v_buyer_index, 3, '0')),
                    v_unique_id,
                    v_creator_uid,
                    v_order_date,
                    v_amount,
                    v_amount + (v_amount * 0.1), -- 10% fee
                    'success',
                    v_payment_type,
                    v_order_date,
                    v_order_date
                );
                
                SET v_trans = v_trans + 1;
            END WHILE;
            
            SET v_creator_index = v_creator_index + 1;
        END WHILE;
        
        SET v_month = v_month + 1;
    END WHILE;
END$$

DELIMITER ;

-- Execute the procedure
CALL generate_mass_transactions();

-- Clean up
DROP PROCEDURE generate_mass_transactions;

-- Generate complete monthly_seller_sales data for past 12 months for all creators
INSERT IGNORE INTO monthly_seller_sales (`tenant`, `seller_user_id`, `year_month`, `transaction_amount`, `miniapp_sales_amount`, `transfer_sales_amount`, `seller_sales_amount`, `gmo_sales_amount`, `developer_sales_amount`, `gmo_transfer_fee_amount`, `approved`, `merged`, `remaining_amount`, `expiration_date`, `transfer_status`, `created_at`, `updated_at`) VALUES
-- test-creator-1: Complete 12 months data
('fanme', 'test-creator-1', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 9 MONTH), '%Y%m'), 18000, 1800, 3600, 12600, 648, 1800, 90, true, true, 12600, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-1', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 10 MONTH), '%Y%m'), 15000, 1500, 3000, 10500, 540, 1500, 75, true, true, 10500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-1', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 11 MONTH), '%Y%m'), 12000, 1200, 2400, 8400, 432, 1200, 60, true, true, 8400, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),

-- test-creator-2: Complete 12 months data
('fanme', 'test-creator-2', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 9 MONTH), '%Y%m'), 7000, 700, 1400, 4900, 252, 700, 35, true, true, 4900, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-2', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 10 MONTH), '%Y%m'), 6000, 600, 1200, 4200, 216, 600, 30, true, true, 4200, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-2', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 11 MONTH), '%Y%m'), 5000, 500, 1000, 3500, 180, 500, 25, true, true, 3500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),

-- test-creator-3: Complete 12 months data
('fanme', 'test-creator-3', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 7 MONTH), '%Y%m'), 18000, 1800, 3600, 12600, 648, 1800, 90, true, true, 12600, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-3', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 8 MONTH), '%Y%m'), 20000, 2000, 4000, 14000, 720, 2000, 100, true, true, 14000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-3', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 9 MONTH), '%Y%m'), 22000, 2200, 4400, 15400, 792, 2200, 110, true, true, 15400, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-3', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 10 MONTH), '%Y%m'), 25000, 2500, 5000, 17500, 900, 2500, 125, true, true, 17500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-3', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 11 MONTH), '%Y%m'), 28000, 2800, 5600, 19600, 1008, 2800, 140, true, true, 19600, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),

-- test-creator-4: Complete 12 months data
('fanme', 'test-creator-4', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 7 MONTH), '%Y%m'), 16000, 1600, 3200, 11200, 576, 1600, 80, true, true, 11200, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-4', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 8 MONTH), '%Y%m'), 14000, 1400, 2800, 9800, 504, 1400, 70, true, true, 9800, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-4', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 9 MONTH), '%Y%m'), 12000, 1200, 2400, 8400, 432, 1200, 60, true, true, 8400, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-4', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 10 MONTH), '%Y%m'), 10000, 1000, 2000, 7000, 360, 1000, 50, true, true, 7000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-4', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 11 MONTH), '%Y%m'), 8000, 800, 1600, 5600, 288, 800, 40, true, true, 5600, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),

-- test-creator-5: Complete 12 months data
('fanme', 'test-creator-5', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 9 MONTH), '%Y%m'), 28000, 2800, 5600, 19600, 1008, 2800, 140, true, true, 19600, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-5', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 10 MONTH), '%Y%m'), 25000, 2500, 5000, 17500, 900, 2500, 125, true, true, 17500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-5', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 11 MONTH), '%Y%m'), 22000, 2200, 4400, 15400, 792, 2200, 110, true, true, 15400, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW());

-- Add more buyers to support the massive transactions
INSERT IGNORE INTO purchasers (`tenant`, `user_id`, `created_at`, `updated_at`) VALUES
('fanme', 'buyer-017', NOW(), NOW()),
('fanme', 'buyer-018', NOW(), NOW()),
('fanme', 'buyer-019', NOW(), NOW()),
('fanme', 'buyer-020', NOW(), NOW()),
('fanme', 'buyer-021', NOW(), NOW()),
('fanme', 'buyer-022', NOW(), NOW()),
('fanme', 'buyer-023', NOW(), NOW()),
('fanme', 'buyer-024', NOW(), NOW()),
('fanme', 'buyer-025', NOW(), NOW()),
('fanme', 'buyer-026', NOW(), NOW()),
('fanme', 'buyer-027', NOW(), NOW()),
('fanme', 'buyer-028', NOW(), NOW()),
('fanme', 'buyer-029', NOW(), NOW()),
('fanme', 'buyer-030', NOW(), NOW()),
('fanme', 'buyer-031', NOW(), NOW()),
('fanme', 'buyer-032', NOW(), NOW()),
('fanme', 'buyer-033', NOW(), NOW()),
('fanme', 'buyer-034', NOW(), NOW()),
('fanme', 'buyer-035', NOW(), NOW()),
('fanme', 'buyer-036', NOW(), NOW()),
('fanme', 'buyer-037', NOW(), NOW()),
('fanme', 'buyer-038', NOW(), NOW()),
('fanme', 'buyer-039', NOW(), NOW()),
('fanme', 'buyer-040', NOW(), NOW()),
('fanme', 'buyer-041', NOW(), NOW()),
('fanme', 'buyer-042', NOW(), NOW()),
('fanme', 'buyer-043', NOW(), NOW()),
('fanme', 'buyer-044', NOW(), NOW()),
('fanme', 'buyer-045', NOW(), NOW()),
('fanme', 'buyer-046', NOW(), NOW()),
('fanme', 'buyer-047', NOW(), NOW()),
('fanme', 'buyer-048', NOW(), NOW()),
('fanme', 'buyer-049', NOW(), NOW()),
('fanme', 'buyer-050', NOW(), NOW());