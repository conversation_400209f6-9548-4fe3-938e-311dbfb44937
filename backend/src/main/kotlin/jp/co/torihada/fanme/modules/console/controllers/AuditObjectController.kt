package jp.co.torihada.fanme.modules.console.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.console.usecases.GetAuditObjects
import jp.co.torihada.fanme.modules.fanme.models.AuditObject

@ApplicationScoped
class AuditObjectController {
    @Inject private lateinit var getAuditObjects: GetAuditObjects

    fun getAuditObjects(auditGroupId: Long): List<AuditObject> {
        return getAuditObjects.execute(auditGroupId).getOrThrow()
    }
}
