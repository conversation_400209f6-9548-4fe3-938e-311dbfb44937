package jp.co.torihada.fanme.modules.shop.controllers

import com.github.michaelbull.result.getOrElse
import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import jp.co.torihada.fanme.modules.shop.Const.USER_UID_MAX_LENGTH
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.CREATOR_UID_IS_REQUIRED
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.CREATOR_UID_TOO_MANY_LENGTH
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.USER_ID_TOO_MANY_LENGTH
import jp.co.torihada.fanme.modules.shop.Util.Companion.stringToInstantOnJST
import jp.co.torihada.fanme.modules.shop.controllers.requests.ItemRequest
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.models.ItemType
import jp.co.torihada.fanme.modules.shop.usecases.item.*
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class ItemController {

    @Inject private lateinit var getItems: GetItems
    @Inject private lateinit var createItem: CreateItem
    @Inject private lateinit var updateItem: UpdateItem
    @Inject private lateinit var sortItems: SortItems
    @Inject private lateinit var getItem: GetItem
    @Inject private lateinit var getCurrentItem: GetCurrentItem
    @Inject private lateinit var getItemCost: GetItemCost
    @Inject private lateinit var getAllItemsFromConsole: GetAllItemsFromConsole
    @Inject private lateinit var getDeliveryFee: GetDeliveryFee
    @Inject private lateinit var rejectItem: RejectItem

    fun getItems(
        @NotBlank(message = CREATOR_UID_IS_REQUIRED)
        @Size(max = USER_UID_MAX_LENGTH, message = CREATOR_UID_TOO_MANY_LENGTH)
        creatorUid: String,
        @Size(max = USER_UID_MAX_LENGTH, message = USER_ID_TOO_MANY_LENGTH) userId: String?,
        available: Boolean?,
        tag: String?,
        odata: OData?,
    ): List<GetItems.ItemForGetItems> {
        return getItems
            .execute(GetItems.Input(creatorUid, userId, available, tag, odata))
            .getOrElse {
                return emptyList()
            }
    }

    @Transactional
    fun createItem(@Valid item: ItemRequest.CreateItem): Item {
        return createItem
            .execute(
                CreateItem.Input(
                    itemType = item.itemType,
                    creatorUid = item.creatorUid,
                    name = item.name,
                    description = item.description,
                    thumbnailUri = item.thumbnailUri,
                    thumbnailFrom = item.thumbnailFrom,
                    thumbnailBlurLevel = item.thumbnailBlurLevel,
                    thumbnailWatermarkLevel = item.thumbnailWatermarkLevel,
                    price = item.price,
                    available = item.available,
                    files =
                        item.files.map {
                            CreateItem.File(
                                name = it.name,
                                objectUri = it.objectUri.substringBefore("?"),
                                thumbnailUri = it.thumbnailUri,
                                price = it.price,
                                fileType = it.fileType,
                                size = it.size,
                                duration = it.duration,
                                itemThumbnailSelected = it.itemThumbnailSelected ?: false,
                                sortOrder = it.sortOrder ?: 0,
                            )
                        },
                    samples =
                        item.samples?.map {
                            CreateItem.SampleFile(
                                name = it.name,
                                objectUri = it.objectUri.substringBefore("?"),
                                thumbnailUri = it.thumbnailUri,
                                fileType = it.fileType,
                                size = it.size,
                                duration = it.duration,
                            )
                        },
                    benefits =
                        item.benefits?.map { benefit ->
                            CreateItem.BenefitParam(
                                description = benefit.description,
                                conditionType = benefit.conditionType,
                                files =
                                    benefit.files.map {
                                        CreateItem.BenefitFile(
                                            name = it.name,
                                            objectUri = it.objectUri.substringBefore("?"),
                                            thumbnailUri = it.thumbnailUri,
                                            fileType = it.fileType,
                                            size = it.size,
                                            duration = it.duration,
                                        )
                                    },
                            )
                        },
                    tags = item.tags,
                    itemOption =
                        CreateItem.ItemOptionParam(
                            isSingleSales = item.itemOption.isSingleSales,
                            qtyTotal = item.itemOption.qtyTotal,
                            qtyPerUser = item.itemOption.qtyPerUser,
                            forSale =
                                CreateItem.ForSale(
                                    startAt =
                                        if (item.itemOption.forSale?.startAt == null) null
                                        else stringToInstantOnJST(item.itemOption.forSale.startAt),
                                    endAt =
                                        if (item.itemOption.forSale?.endAt == null) null
                                        else stringToInstantOnJST(item.itemOption.forSale.endAt),
                                ),
                            password = item.itemOption.password,
                            onSale =
                                if (item.itemOption.onSale != null) {
                                    CreateItem.OnSale(
                                        discountRate = item.itemOption.onSale.discountRate,
                                        startAt =
                                            if (item.itemOption.onSale.startAt == null) null
                                            else
                                                stringToInstantOnJST(
                                                    item.itemOption.onSale.startAt
                                                ),
                                        endAt =
                                            if (item.itemOption.onSale.endAt == null) null
                                            else stringToInstantOnJST(item.itemOption.onSale.endAt),
                                    )
                                } else {
                                    null
                                },
                        ),
                )
            )
            .getOrThrow()
    }

    @Transactional
    fun updateItem(@Valid item: ItemRequest.UpdateItem): Item {
        return updateItem
            .execute(
                UpdateItem.Input(
                    itemId = item.itemId,
                    creatorUid = item.creatorUid,
                    name = item.name,
                    description = item.description,
                    thumbnailUri = item.thumbnailUri,
                    thumbnailFrom = item.thumbnailFrom,
                    thumbnailBlurLevel = item.thumbnailBlurLevel,
                    thumbnailWatermarkLevel = item.thumbnailWatermarkLevel,
                    price = item.price,
                    available = item.available,
                    files =
                        item.files.map {
                            UpdateItem.File(
                                id = it.id,
                                name = it.name,
                                objectUri = it.objectUri.substringBefore("?"),
                                thumbnailUri = it.thumbnailUri,
                                price = it.price,
                                fileType = it.fileType,
                                size = it.size,
                                duration = it.duration,
                                itemThumbnailSelected = it.itemThumbnailSelected ?: false,
                                sortOrder = it.sortOrder ?: 0,
                            )
                        },
                    samples =
                        item.samples?.map {
                            UpdateItem.SampleFile(
                                id = it.id,
                                name = it.name,
                                objectUri = it.objectUri.substringBefore("?"),
                                thumbnailUri = it.thumbnailUri,
                                fileType = it.fileType,
                                size = it.size,
                                duration = it.duration,
                            )
                        },
                    benefits =
                        item.benefits?.map {
                            UpdateItem.BenefitParam(
                                id = it.id,
                                description = it.description,
                                conditionType = it.conditionType,
                                files =
                                    it.files.map {
                                        UpdateItem.BenefitFile(
                                            id = it.id,
                                            name = it.name,
                                            objectUri = it.objectUri.substringBefore("?"),
                                            thumbnailUri = it.thumbnailUri,
                                            fileType = it.fileType,
                                            size = it.size,
                                            duration = it.duration,
                                        )
                                    },
                            )
                        },
                    tags = item.tags,
                    itemOption =
                        UpdateItem.ItemOptionParam(
                            isSingleSales = item.itemOption.isSingleSales,
                            qtyTotal = item.itemOption.qtyTotal,
                            qtyPerUser = item.itemOption.qtyPerUser,
                            forSale =
                                if (item.itemOption.forSale != null) {
                                    UpdateItem.ForSale(
                                        startAt =
                                            if (item.itemOption.forSale.startAt == null) null
                                            else
                                                stringToInstantOnJST(
                                                    item.itemOption.forSale.startAt
                                                ),
                                        endAt =
                                            if (item.itemOption.forSale.endAt == null) null
                                            else stringToInstantOnJST(item.itemOption.forSale.endAt),
                                    )
                                } else {
                                    null
                                },
                            password = item.itemOption.password,
                            onSale =
                                if (item.itemOption.onSale != null) {
                                    UpdateItem.OnSale(
                                        discountRate = item.itemOption.onSale.discountRate,
                                        startAt =
                                            if (item.itemOption.onSale.startAt == null) null
                                            else
                                                stringToInstantOnJST(
                                                    item.itemOption.onSale.startAt
                                                ),
                                        endAt =
                                            if (item.itemOption.onSale.endAt == null) null
                                            else stringToInstantOnJST(item.itemOption.onSale.endAt),
                                    )
                                } else {
                                    null
                                },
                        ),
                )
            )
            .getOrThrow()
    }

    fun getCurrentItem(id: Long, creatorUid: String, userUid: String?): GetItem.ItemForGetItem {
        return getCurrentItem
            .execute(GetCurrentItem.Input(id, creatorUid, userUid ?: ""))
            .getOrThrow()
    }

    fun getItem(id: Long, creatorUid: String, userUid: String?): GetItem.ItemForGetItem {
        return getItem.execute(GetItem.Input(id, creatorUid, userUid ?: "")).getOrThrow()
    }

    @Transactional
    fun sortItems(@Valid priorities: ItemRequest.SortItems): Boolean {
        return sortItems
            .execute(
                SortItems.Input(
                    priorities.userUid,
                    priorities.items.map { SortItems.SortItem(it.id, it.sortOrder) },
                )
            )
            .getOrThrow()
    }

    fun getItemCost(itemType: Int): Int {
        return getItemCost.execute(itemType).getOrThrow().cost
    }

    fun getAllItemsFromConsole(shopId: Long): List<Item> {
        return getAllItemsFromConsole.execute(shopId).getOrThrow()
    }

    fun getDeliveryFee(itemType: ItemType): Int {
        return getDeliveryFee.execute(itemType).getOrThrow().deliveryFee ?: 0
    }

    @Transactional(Transactional.TxType.REQUIRES_NEW)
    fun rejectItem(itemId: Long, rejectComment: String): Item {
        return rejectItem.execute(itemId, rejectComment).getOrThrow()
    }
}
