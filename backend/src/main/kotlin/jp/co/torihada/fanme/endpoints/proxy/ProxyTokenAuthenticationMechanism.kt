package jp.co.torihada.fanme.endpoints.proxy

import io.netty.handler.codec.http.HttpHeaderNames
import io.netty.handler.codec.http.HttpResponseStatus
import io.quarkus.security.identity.IdentityProviderManager
import io.quarkus.security.identity.SecurityIdentity
import io.quarkus.security.identity.request.AuthenticationRequest
import io.quarkus.security.identity.request.TokenAuthenticationRequest
import io.quarkus.vertx.http.runtime.security.ChallengeData
import io.quarkus.vertx.http.runtime.security.HttpAuthenticationMechanism
import io.smallrye.mutiny.Uni
import io.vertx.ext.web.RoutingContext
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.console.services.ProxyAccessTokenService
import org.jboss.logging.Logger

@ApplicationScoped
class ProxyTokenAuthenticationMechanism : HttpAuthenticationMechanism {
    @Inject private lateinit var logger: Logger
    @Inject private lateinit var proxyTokenService: ProxyAccessTokenService

    override fun authenticate(
        context: RoutingContext,
        identityProviderManager: IdentityProviderManager,
    ): Uni<SecurityIdentity> {
        val proxyToken = context.request().headers().get("X-Proxy-Access-Token")

        return if (proxyToken != null) {
            authenticateViaProxy(proxyToken, identityProviderManager)
        } else {
            Uni.createFrom().nullItem()
        }
    }

    private fun authenticateViaProxy(
        proxyToken: String,
        identityProviderManager: IdentityProviderManager,
    ): Uni<SecurityIdentity> {
        return proxyTokenService.retrieveProxyAccessData(proxyToken).onItem().transformToUni {
            proxyData ->
            if (proxyData != null) {
                val proxyAuthRequest =
                    ProxyAuthenticationRequest(
                        targetUserAccountIdentity = proxyData.targetUserAccountIdentity,
                        targetUserFanmeToken = proxyData.targetUserFanmeToken,
                    )
                identityProviderManager.authenticate(proxyAuthRequest)
            } else {
                logger.warn("No proxy data found for token: ${proxyToken.take(10)}...")
                Uni.createFrom().nullItem()
            }
        }
    }

    override fun getChallenge(context: RoutingContext): Uni<ChallengeData> {
        return Uni.createFrom()
            .item(
                ChallengeData(
                    HttpResponseStatus.UNAUTHORIZED.code(),
                    HttpHeaderNames.WWW_AUTHENTICATE,
                    "Bearer",
                )
            )
    }

    override fun getCredentialTypes(): Set<Class<out AuthenticationRequest>> {
        return setOf(TokenAuthenticationRequest::class.java, ProxyAuthenticationRequest::class.java)
    }
}
