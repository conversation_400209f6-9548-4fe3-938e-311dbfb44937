package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupController as FanmeAuditGroupController
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupControllerUpdateStatusInput
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditStatus
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditType
import jp.co.torihada.fanme.modules.shop.controllers.ItemController
import jp.co.torihada.fanme.modules.shop.controllers.ShopController

@ApplicationScoped
class UpdateAuditStatus {
    @Inject private lateinit var auditGroupController: FanmeAuditGroupController
    @Inject private lateinit var itemController: ItemController
    @Inject private lateinit var shopController: ShopController

    data class Input(
        val auditGroupId: Long,
        val status: AuditStatus,
        val comment: String? = null,
        val auditedUserUid: String? = null,
    )

    fun execute(params: Input): Result<Unit, FanmeException> {
        // 監査ステータスの更新
        val auditGroup =
            auditGroupController.updateAuditStatus(
                AuditGroupControllerUpdateStatusInput(
                    auditGroupId = params.auditGroupId,
                    status = params.status,
                    comment = params.comment,
                    auditedUserUid = params.auditedUserUid,
                )
            )
        // 監査ステータスの更新後、却下の場合は商品非公開や画像を削除する処理を追加
        if (auditGroup != null && params.status == AuditStatus.REJECTED) {
            if (auditGroup.auditType == AuditType.SHOP_ITEM) {
                // 商品の非公開
                if (auditGroup.itemId != null) {
                    params.comment?.let { itemController.rejectItem(auditGroup.itemId!!, it) }
                }
            } else if (auditGroup.auditType == AuditType.SHOP) {
                // SHOP情報の削除
                val metadataObject = auditGroup.getMetadataAsObject()
                if (metadataObject?.shopId != null) {
                    params.comment?.let { shopController.rejectShop(metadataObject.shopId!!, it) }
                }
            }
        }
        return Ok(Unit)
    }
}
