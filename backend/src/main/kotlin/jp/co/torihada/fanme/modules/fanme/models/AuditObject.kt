package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonBackReference
import com.fasterxml.jackson.annotation.JsonProperty
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size
import jp.co.torihada.fanme.modules.fanme.services.audit.AuditGroupServiceAuditObject

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "audit_objects")
class AuditObject : BaseModel() {
    enum class AssetType(val value: String) {
        IMAGE("IMAGE"),
        VOICE("VOICE"),
        MOVIE("MOVIE"),
        ANY("ANY");

        companion object {
            fun fromValue(value: String): AssetType {
                return values().find { it.value == value }
                    ?: throw IllegalArgumentException("Invalid AssetType value: $value")
            }
        }
    }

    @JsonBackReference
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "audit_group_id", insertable = false, updatable = false)
    var auditGroup: AuditGroup? = null

    @NotNull @Column(name = "audit_group_id", nullable = false) var auditGroupId: Long = 0

    @Size(max = 50) @NotNull @Column(name = "bucket", nullable = false) var bucket: String = ""

    @Size(max = 255)
    @NotNull
    @Column(name = "file_path", nullable = false)
    var filePath: String = ""

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "asset_type", nullable = false)
    var assetType: AssetType = AssetType.IMAGE

    @get:JsonProperty("file_url") @Transient var fileUrl: String = ""

    companion object : PanacheCompanion<AuditObject> {
        fun findByAuditGroupId(auditGroupId: Long): List<AuditObject> {
            return find("auditGroupId", auditGroupId).list()
        }

        /** 監査オブジェクトを作成 */
        fun create(
            auditGroupId: Long,
            bucket: String,
            filePath: String,
            assetType: AssetType,
        ): AuditObject {
            return AuditObject()
                .apply {
                    this.auditGroupId = auditGroupId
                    this.bucket = bucket
                    this.filePath = filePath
                    this.assetType = assetType
                }
                .apply { persist() }
        }

        /** 監査グループIDに紐づく監査オブジェクトを一括作成 */
        fun createBatch(
            auditGroupId: Long,
            auditObjects: List<AuditGroupServiceAuditObject>,
        ): List<AuditObject> {
            return auditObjects.map { entity ->
                create(
                    auditGroupId = auditGroupId,
                    bucket = entity.bucket,
                    filePath = entity.filePath,
                    assetType = entity.assetType,
                )
            }
        }
    }
}
