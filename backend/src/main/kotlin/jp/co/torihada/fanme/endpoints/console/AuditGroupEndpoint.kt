package jp.co.torihada.fanme.endpoints.console

import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.Produces
import jakarta.ws.rs.QueryParam
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.dto.BaseResponseBody
import jp.co.torihada.fanme.exception.ErrorObject
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.controllers.AuditGroupController
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup
import jp.co.torihada.fanme.odata.OData
import org.eclipse.microprofile.openapi.annotations.tags.Tag

class AuditGroupsData(val auditGroups: List<AuditGroup>, val totalCount: Long? = null)

class AuditGroupsResponse(data: AuditGroupsData, errors: List<ErrorObject> = emptyList()) :
    BaseResponseBody<AuditGroupsData>(data, errors)

@Path("/console/audit-groups")
@Tag(name = "CONSOLE", description = "CONSOLE APIサーバー")
class AuditGroupEndpoint {

    @Inject private lateinit var handler: AuditGroupController

    @Inject private lateinit var requestContext: ContainerRequestContext

    @GET
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE)
    @Produces(MediaType.APPLICATION_JSON)
    fun getAuditGroups(
        @QueryParam("\$top") top: Int? = null,
        @QueryParam("\$skip") skip: Int? = null,
        @QueryParam("\$count") count: Boolean? = null,
    ): AuditGroupsResponse {
        val odata = requestContext.getProperty("odata") as OData?
        val result = handler.getAuditGroups(odata)
        val data =
            AuditGroupsData(auditGroups = result.auditGroupList, totalCount = result.totalCount)
        return AuditGroupsResponse(data = data)
    }
}
