package jp.co.torihada.fanme.modules.fanme.usecases.notificationSetting

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.fanme.models.NotificationSetting

@ApplicationScoped
class GetNotificationSetting {

    data class Input(val creatorUid: String)

    data class Output(val enabled: Boolean?)

    fun execute(input: Input): Result<Output, FanmeException> {
        val setting = NotificationSetting.findByCreatorUid(input.creatorUid)
        val response = Output(enabled = setting?.enabled)
        return Ok(response)
    }
}
