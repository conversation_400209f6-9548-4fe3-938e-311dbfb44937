package jp.co.torihada.fanme.modules.fanme.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.models.AuditObject
import jp.co.torihada.fanme.modules.fanme.usecases.audit.GetAuditObjects

@ApplicationScoped
class AuditObjectController {
    @Inject private lateinit var getAuditObjects: GetAuditObjects

    fun getAuditObjects(auditGroupId: Long): List<AuditObject> {
        return getAuditObjects.execute(auditGroupId).getOrThrow()
    }
}
