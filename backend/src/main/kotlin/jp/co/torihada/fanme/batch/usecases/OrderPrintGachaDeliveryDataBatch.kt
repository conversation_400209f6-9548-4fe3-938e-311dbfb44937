package jp.co.torihada.fanme.batch.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.io.ByteArrayInputStream
import java.io.StringWriter
import java.nio.file.Files
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.modules.fanme.controllers.FanmeCustomerController
import jp.co.torihada.fanme.modules.shop.Config as ShopConfig
import jp.co.torihada.fanme.modules.shop.external.GcpStorageClient
import jp.co.torihada.fanme.modules.shop.models.GachaReceivedFile
import jp.co.torihada.fanme.modules.shop.models.ItemFile
import jp.co.torihada.fanme.modules.shop.models.ItemType
import jp.co.torihada.fanme.modules.shop.models.PurchasedItem
import jp.co.torihada.fanme.modules.shop.services.SmapoFileUploadService
import jp.co.torihada.fanme.modules.shop.services.aws.S3
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVPrinter
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.jboss.logging.Logger

private typealias GachaFileCounts = List<Pair<GachaReceivedFile, Int>>

private typealias CustomerEntity =
    jp.co.torihada.fanme.modules.fanme.services.fanmeCustomer.FanmeCustomerEntity

/**
 * プリントガチャ配送データバッチ
 * 1. 指定日のプリントガチャ購入データをCSV形式で出力
 * 2. 購入された商品の画像をS3からGCPストレージに転送（初回購入時のみ）
 */
@ApplicationScoped
class OrderPrintGachaDeliveryDataBatch {

    companion object {
        private const val JST_ZONE_ID = "Asia/Tokyo"
        private const val DATE_FORMAT_PATTERN = "yyyyMMdd"
        private const val IMAGE_PATH_PREFIX = "fanme/images/items"
        private const val PNG_EXTENSION = ".png"
        private val CSV_HEADERS =
            arrayOf(
                "オーダーID",
                "購入日時",
                "商品名",
                "郵便番号（購入者）",
                "住所（購入者）",
                "名前（購入者）",
                "画像ID",
                "商品単価",
                "購入数",
                "画像URL",
            )
    }

    @Inject private lateinit var logger: Logger
    @Inject private lateinit var config: Config
    @Inject private lateinit var shopConfig: ShopConfig
    @Inject private lateinit var s3: S3
    @Inject private lateinit var fanmeCustomerController: FanmeCustomerController
    @Inject private lateinit var smapoFileUploadService: SmapoFileUploadService

    @RestClient private lateinit var gcpStorageClient: GcpStorageClient

    fun execute(targetDate: LocalDate) {
        logger.info("プリントガチャ配送データバッチ開始。対象日: $targetDate")

        val isEnabled = shopConfig.featureTogglePrintGacha()

        if (!isEnabled) {
            logger.info("プリントガチャバッチが無効化されているため、処理をスキップします")
            return
        }

        // プリントガチャの購入データを取得
        val purchasedItems = getPrintGachaPurchasedItems(targetDate)
        logger.info("購入データ件数: ${purchasedItems.size}")

        if (purchasedItems.isEmpty()) {
            logger.info("対象日の購入データが存在しないため、処理終了")
            return
        }

        // CSVデータを生成
        val csvData = generateCsvData(purchasedItems)
        logger.info("CSVデータ生成完了")

        // CSVファイルをS3とGCPストレージにアップロード
        uploadCsvToGcp(csvData, targetDate).getOrElse { e ->
            logger.error("CSVアップロードでエラー発生", e)
            throw e
        }

        // 画像をS3からGCPに転送
        val transferredCount = transferItemFileImagesToGcp(purchasedItems)
        logger.info("画像転送完了: $transferredCount 件")

        logger.info("プリントガチャ配送データバッチ完了")
    }

    private fun getPrintGachaPurchasedItems(targetDate: LocalDate): List<PurchasedItem> {
        val jstZone = ZoneId.of(JST_ZONE_ID)

        val startTimeJst = targetDate.atStartOfDay(jstZone)
        val endTimeJst = startTimeJst.plusDays(1)

        val startDateTimeUtc = startTimeJst.toInstant()
        val endDateTimeUtc = endTimeJst.toInstant()

        logger.info(
            "Search purchasedItems with successful payment status between $startDateTimeUtc and $endDateTimeUtc"
        )

        val items =
            PurchasedItem.findOrderedItemsByItemTypeAndTerms(
                ItemType.PRINT_GACHA,
                startDateTimeUtc,
                endDateTimeUtc,
            )

        logger.info("Found ${items.size} purchasedItems with successful payment status")
        return items
    }

    private fun generateCsvData(purchasedItems: List<PurchasedItem>): String {
        val purchaserUids = purchasedItems.map { it.purchaserUid }.distinct()
        val customers = fanmeCustomerController.listFanmeCustomers(purchaserUids)

        val csvFormat = CSVFormat.DEFAULT.builder().setHeader(*CSV_HEADERS).build()

        return StringWriter().use { writer ->
            csvFormat.print(writer).use { printer ->
                purchasedItems.forEach { item ->
                    val customer = customers.firstOrNull { it.creatorUid == item.purchaserUid }
                    val fileCounts = groupGachaFiles(item)

                    if (customer != null && item.gachaReceivedFile.isNotEmpty()) {
                        fileCounts.forEach { file -> printCsvRow(printer, item, customer, file) }
                    } else {
                        logger.warn(
                            "Customer information not found for purchaserUid: ${item.purchaserUid}"
                        )
                    }
                }
            }
            writer.toString()
        }
    }

    private fun groupGachaFiles(purchasedItem: PurchasedItem): GachaFileCounts {
        return purchasedItem.gachaReceivedFile
            .groupBy { it.itemFile.id }
            .map { (_, list) -> list.first() to list.size }
    }

    private fun printCsvRow(
        printer: CSVPrinter,
        item: PurchasedItem,
        customer: CustomerEntity,
        file: Pair<GachaReceivedFile, Int>,
    ) {
        printer.printRecord(
            item.order.id,
            item.purchasedAt?.let {
                jp.co.torihada.fanme.modules.shop.Util.toJSTLocalDateTimeString(it)
            } ?: "",
            item.item.name,
            customer.postalCode,
            "${customer.prefecture}${customer.city}${customer.street}${customer.building ?: ""}",
            "${customer.lastName} ${customer.firstName}",
            "${item.item.id}_${file.first.itemFile.id}",
            item.item.price,
            file.second,
            "${shopConfig.smapoGcpBucketName()}/$IMAGE_PATH_PREFIX/${item.item.id}/${file.first.itemFile.id}$PNG_EXTENSION",
        )
    }

    private data class CsvPaths(
        val fileName: String,
        val s3RemotePath: String,
        val gcpObjectPath: String,
    )

    private fun createCsvPaths(targetDate: LocalDate): CsvPaths {
        val dateStr = targetDate.format(DateTimeFormatter.ofPattern(DATE_FORMAT_PATTERN))
        val fileName = "print_gacha_purchase_data$dateStr.csv"
        val jstZone = ZoneId.of(JST_ZONE_ID)
        val startTimeJst = targetDate.atStartOfDay(jstZone)
        val dateDir = "${startTimeJst.year}/${startTimeJst.monthValue}/${startTimeJst.dayOfMonth}/"

        val s3RemotePath = "${config.envKind()}/${dateDir}$fileName"
        val gcpObjectPath = "fanme/${dateDir}$fileName"

        return CsvPaths(fileName, s3RemotePath, gcpObjectPath)
    }

    private fun uploadCsvToGcp(csvData: String, targetDate: LocalDate): Result<String, Exception> {
        val paths = createCsvPaths(targetDate)
        val tempFile = Files.createTempFile("print_gacha_purchase_data", ".csv")

        return try {
            Files.write(tempFile, csvData.toByteArray())

            smapoFileUploadService.uploadFileToS3(paths.s3RemotePath, tempFile).getOrElse { e ->
                logger.error("S3へのCSVアップロードでエラー", e)
                return Err(e)
            }

            smapoFileUploadService
                .uploadFileToSmapo(paths.gcpObjectPath, tempFile, false)
                .getOrElse { e ->
                    logger.error("GCPへのCSVアップロードでエラー", e)
                    return Err(e)
                }

            Ok(paths.gcpObjectPath)
        } catch (e: Exception) {
            logger.error("CSVファイルのアップロードでエラー", e)
            Err(e)
        } finally {
            try {
                Files.delete(tempFile)
            } catch (e: Exception) {
                logger.warn("一時ファイルの削除に失敗", e)
            }
        }
    }

    private fun transferItemFileImagesToGcp(purchasedItems: List<PurchasedItem>): Int {
        val processedItems = mutableSetOf<Long>()
        var totalTransferred = 0

        purchasedItems.forEach { purchasedItem ->
            val itemId = purchasedItem.item.id

            if (itemId != null && !processedItems.contains(itemId)) {
                processedItems.add(itemId)
                totalTransferred += transferItemFileImagesIfNeeded(itemId)
            }
        }

        return totalTransferred
    }

    private fun transferItemFileImagesIfNeeded(itemId: Long): Int {
        val itemFiles = ItemFile.findByItemId(itemId)
        if (itemFiles.isEmpty()) {
            return 0
        }

        val firstItemFile = itemFiles.first()
        val gcpPath = "$IMAGE_PATH_PREFIX/$itemId/${firstItemFile.id}$PNG_EXTENSION"

        val exists =
            try {
                val response =
                    gcpStorageClient.checkObjectExists(shopConfig.smapoGcpBucketName(), gcpPath)
                response.status == 200
            } catch (e: Exception) {
                false
            }

        if (exists) {
            return 0
        }

        var successCount = 0
        itemFiles.forEach { itemFile ->
            transferSingleImageFromS3ToGcp(itemFile).getOrElse { e ->
                logger.error("画像転送に失敗しました。商品ID: $itemId, ファイルID: ${itemFile.id}", e)
                return@forEach
            }
            successCount++
        }

        return successCount
    }

    private fun transferSingleImageFromS3ToGcp(itemFile: ItemFile): Result<Unit, Exception> {
        return try {
            val objectUri =
                itemFile.objectUri ?: return Err(RuntimeException("ItemFile.objectUriが設定されていません"))

            val s3Key = extractS3KeyFromUri(objectUri)
            val gcpPath = "$IMAGE_PATH_PREFIX/${itemFile.item.id}/${itemFile.id}$PNG_EXTENSION"

            val imageData =
                s3.getObject(shopConfig.s3BucketName(), s3Key)
                    ?: return Err(RuntimeException("S3から画像データを取得できませんでした: $s3Key"))

            ByteArrayInputStream(imageData).use { inputStream ->
                gcpStorageClient.uploadObject(
                    bucket = shopConfig.smapoGcpBucketName(),
                    name = gcpPath,
                    uploadType = "media",
                    content = inputStream,
                )

                Ok(Unit)
            }
        } catch (e: Exception) {
            Err(e)
        }
    }

    private fun extractS3KeyFromUri(objectUri: String): String {
        val bucketName = shopConfig.s3BucketName()
        val bucketIndex = objectUri.indexOf("/$bucketName/")
        if (bucketIndex == -1) {
            throw RuntimeException("objectUriからバケット名が見つかりません: $objectUri, bucketName: $bucketName")
        }
        return objectUri.substring(bucketIndex + bucketName.length + 2)
    }
}
