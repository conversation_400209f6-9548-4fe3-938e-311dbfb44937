package jp.co.torihada.fanme.modules.shop.services

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.nio.file.Files
import java.nio.file.Path
import jp.co.torihada.fanme.modules.shop.Config as ShopConfig
import jp.co.torihada.fanme.modules.shop.external.GcpStorageClient
import jp.co.torihada.fanme.modules.shop.services.aws.S3
import org.eclipse.microprofile.rest.client.inject.RestClient

@ApplicationScoped
class SmapoFileUploadService {

    @Inject private lateinit var s3: S3
    @Inject private lateinit var shopConfig: ShopConfig

    @RestClient private lateinit var gcpStorageClient: GcpStorageClient

    fun uploadFileToS3(remotePath: String, filePath: Path): Result<String, Exception> {
        s3.putObject(shopConfig.s3BucketNameForSmapo(), remotePath, "text/csv", filePath)
        return Ok(remotePath)
    }

    fun uploadFileToSmapo(
        fileName: String,
        filePath: Path,
        addFanmePrefix: Boolean = true,
    ): Result<String, Exception> {
        Files.newInputStream(filePath).use { inputStream ->
            val objectName = if (addFanmePrefix) "fanme/$fileName" else fileName
            gcpStorageClient.uploadObject(
                bucket = shopConfig.smapoGcpBucketName(),
                name = objectName,
                uploadType = "media",
                content = inputStream,
            )
        }
        return Ok(fileName)
    }
}
