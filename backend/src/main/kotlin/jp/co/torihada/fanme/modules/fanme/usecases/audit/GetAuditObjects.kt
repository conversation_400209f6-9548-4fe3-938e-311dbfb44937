package jp.co.torihada.fanme.modules.fanme.usecases.audit

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.fanme.models.AuditObject
import jp.co.torihada.fanme.modules.fanme.services.aws.S3

@ApplicationScoped
class GetAuditObjects {
    @Inject private lateinit var s3: S3

    fun execute(auditGroupId: Long): Result<List<AuditObject>, FanmeException> {
        val list = AuditObject.findByAuditGroupId(auditGroupId)
        list.forEach { it.fileUrl = s3.getObjectUri(it.bucket, it.filePath) }
        return Ok(list)
    }
}
