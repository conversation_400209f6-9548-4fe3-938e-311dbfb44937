package jp.co.torihada.fanme.modules.shop.usecases.order

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import java.math.BigDecimal
import java.math.RoundingMode
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ItemAmountExceedsCostException
import jp.co.torihada.fanme.modules.shop.Const.ChekiPhysicalItemCost
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.models.ItemType

abstract class BaseGetAmounts {
    fun calculateMargin(
        price: Int,
        quantity: Int,
        marginRate: BigDecimal,
        item: Item,
    ): Result<Int, FanmeException> {
        var itemCost = 0
        if (item.itemType == ItemType.CHEKI) {
            itemCost = getItemCostFromName(item.name)
        } else if (item.itemType == ItemType.PRINT_GACHA) {
            itemCost = ItemType.PRINT_GACHA.cost
        }

        if (price <= itemCost) {
            return Err(ItemAmountExceedsCostException())
        }

        val totalCost = BigDecimal(itemCost * quantity)
        val margin =
            BigDecimal(price * quantity)
                .minus(totalCost)
                .multiply(marginRate)
                .setScale(0, RoundingMode.CEILING)
                .plus(totalCost)
                .toInt()
        return Ok(margin)
    }

    private fun getItemCostFromName(itemName: String): Int {
        val prefixRegex = Regex("【(.+?)】")
        val matchResult = prefixRegex.find(itemName)

        if (matchResult != null) {
            val extractedPrefix = matchResult.groupValues[1]
            val matchedItemCost =
                ChekiPhysicalItemCost.entries.find { it.prefix == extractedPrefix }

            if (matchedItemCost != null) {
                return matchedItemCost.value
            }
        }
        return ChekiPhysicalItemCost.CHEKI.value
    }
}
