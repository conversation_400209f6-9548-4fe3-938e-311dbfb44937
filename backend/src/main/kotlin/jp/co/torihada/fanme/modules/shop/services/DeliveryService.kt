package jp.co.torihada.fanme.modules.shop.services

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.modules.shop.Const
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.models.ItemType

@ApplicationScoped
class DeliveryService {
    @Inject private lateinit var config: Config

    fun getDeliveryFee(items: List<Item>): Int? {
        val deliveryFee =
            if (items.any { it.itemType == ItemType.CHEKI }) Const.CHEKI_DELIVERY_FEE
            else if (items.any { it.itemType == ItemType.PRINT_GACHA })
                Const.PRINT_GACHA_DELIVERY_FEE
            else null

        return deliveryFee
    }

    fun getDeliveryFeeByItemType(itemType: ItemType): Int? {
        return when (itemType) {
            ItemType.CHEKI -> Const.CHEKI_DELIVERY_FEE
            ItemType.PRINT_GACHA -> Const.PRINT_GACHA_DELIVERY_FEE
            else -> null
        }
    }
}
