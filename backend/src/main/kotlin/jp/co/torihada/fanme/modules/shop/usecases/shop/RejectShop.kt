package jp.co.torihada.fanme.modules.shop.usecases.shop

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.controllers.NotificationController
import jp.co.torihada.fanme.modules.fanme.usecases.notification.CreateNotification
import jp.co.torihada.fanme.modules.payment.models.Notification.NotificationType
import jp.co.torihada.fanme.modules.shop.models.Shop
import org.jboss.logging.Logger

@ApplicationScoped
class RejectShop {
    @Inject private lateinit var logger: Logger
    @Inject private lateinit var notificationController: NotificationController

    fun execute(shopId: Long, rejectComment: String): Result<Shop, Exception> {
        val shop = Shop.reject(shopId)
        // 通知を送る
        val shopName = shop.name
        val shopNameeText = if (!shopName.isEmpty()) "「$shopName」" else ""
        val commentText = if (!rejectComment.isEmpty()) "（理由: $rejectComment）" else ""
        shop.creatorUid?.let {
            notificationController.createNotification(
                CreateNotification.Input(
                    type = NotificationType.REJECTION,
                    userUid = it,
                    content = "ガイドライン違反により" + shopNameeText + "情報の一部が削除されました" + commentText,
                )
            )
        }

        return Ok(shop)
    }
}
