package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonBackReference
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.enterprise.context.ApplicationScoped
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull

@Entity
@Table(name = "user_consents")
class UserConsent : BaseModel() {

    enum class Consent(val path: String) {
        TERMS_20250801("/consents/terms-20250801")
    }

    @ManyToOne
    @JsonBackReference
    @JoinColumn(name = "user_id", nullable = false)
    var user: User? = null

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "consent_name", nullable = false)
    var consent: Consent? = null

    @ApplicationScoped
    companion object : PanacheCompanion<UserConsent> {

        fun create(user: User, consent: Consent): UserConsent {
            val userConsent = UserConsent()
            userConsent.user = user
            userConsent.consent = consent
            userConsent.persist()
            return userConsent
        }

        fun findByUser(user: User): List<UserConsent> {
            return find("user", user).list()
        }
    }
}
