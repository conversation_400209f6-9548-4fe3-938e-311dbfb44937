package jp.co.torihada.fanme.modules.fanme.usecases.contentBlock

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.ContentBlock
import org.eclipse.microprofile.openapi.annotations.media.Schema

@ApplicationScoped
class ToggleContentBlockDisplayable {

    class Input(val contentBlockId: Long, val displayable: Boolean)

    @Schema(name = "ToggleContentBlockDisplayable_ContentBlock")
    class Output(val id: Long, val displayable: Boolean?)

    fun execute(input: Input): Result<Output, Exception> {
        val targetBlock =
            ContentBlock.findById(input.contentBlockId)
                ?: return Err(ResourceNotFoundException("ContentBlock"))

        val result =
            ContentBlock.toggleDisplayable(
                contentBlock = targetBlock,
                displayable = input.displayable,
            )

        return Ok(Output(id = result.id!!, displayable = result.displayable))
    }
}
