package jp.co.torihada.fanme.modules.fanme.controllers

import com.github.michaelbull.result.getOrThrow
import com.github.michaelbull.result.unwrap
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.validation.Valid
import jakarta.validation.constraints.NotNull
import jp.co.torihada.fanme.modules.fanme.models.SnsLink
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.usecases.snslink.CreateSnsLinks
import jp.co.torihada.fanme.modules.fanme.usecases.snslink.GetSnsLinks
import jp.co.torihada.fanme.modules.fanme.usecases.snslink.MoveSnsLinks
import jp.co.torihada.fanme.modules.fanme.usecases.snslink.UpdateSnsLinks

@ApplicationScoped
class SnsLinkController {

    @Inject private lateinit var getSnsLinks: GetSnsLinks
    @Inject private lateinit var createSnsLinks: CreateSnsLinks
    @Inject private lateinit var updateSnsLinks: UpdateSnsLinks
    @Inject private lateinit var moveSnsLinks: MoveSnsLinks

    fun getSnsLinks(userUuid: String): List<SnsLink> {
        return getSnsLinks.execute(GetSnsLinks.Input(userUuid = userUuid)).getOrThrow()
    }

    data class CreateSnsLinksRequest(
        @NotNull val user: User,
        @NotNull val snsLinks: List<SnsLinkRequest>,
    )

    data class SnsLinkRequest(
        @NotNull val type: String,
        @NotNull val accountIdentity: String,
        @NotNull val displayOrderNumber: Int,
        @NotNull val displayable: Boolean,
    )

    @Transactional
    fun createSnsLinks(@Valid request: CreateSnsLinksRequest): CreateSnsLinks.Output {
        val input =
            CreateSnsLinks.Input(
                userId = request.user.id!!,
                snsLinks =
                    request.snsLinks.map { snsLinkItem ->
                        CreateSnsLinks.Input.SnsLinkInput(
                            type = snsLinkItem.type,
                            accountIdentity = snsLinkItem.accountIdentity,
                            displayOrderNumber = snsLinkItem.displayOrderNumber,
                            displayable = snsLinkItem.displayable,
                        )
                    },
            )

        return createSnsLinks.execute(input).unwrap()
    }

    data class UpdateSnsLinksRequest(
        @NotNull val user: User,
        @NotNull val snsLinks: List<UpdateSnsLinkRequest>,
    )

    data class UpdateSnsLinkRequest(
        @NotNull val type: String,
        @NotNull val accountIdentity: String,
        @NotNull val displayable: Boolean,
    )

    @Transactional
    fun updateSnsLinks(@Valid request: UpdateSnsLinksRequest): CreateSnsLinks.Output {
        val input =
            UpdateSnsLinks.Input(
                userId = request.user.id!!,
                snsLinks =
                    request.snsLinks.map { snsLinkItem ->
                        UpdateSnsLinks.Input.SnsLinkInput(
                            type = snsLinkItem.type,
                            accountIdentity = snsLinkItem.accountIdentity,
                            displayable = snsLinkItem.displayable,
                        )
                    },
            )

        return updateSnsLinks.execute(input).unwrap()
    }

    data class MoveSnsLinksRequest(@NotNull val user: User, @NotNull val snsLinks: List<String>)

    @Transactional
    fun moveSnsLinks(request: MoveSnsLinksRequest): MoveSnsLinks.Output {
        val input = MoveSnsLinks.Input(userId = request.user.id!!, snsLinks = request.snsLinks)

        return moveSnsLinks.execute(input).unwrap()
    }
}
