package jp.co.torihada.fanme.modules.fanme.usecases.consent

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.InvalidParameterException
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.models.UserConsent
import org.hibernate.exception.ConstraintViolationException
import org.jboss.logging.Logger

@ApplicationScoped
class CreateUserConsentUseCase {

    @Inject private lateinit var logger: Logger

    data class Input(val user: User, val consentName: String)

    fun execute(params: Input): Result<UserConsent?, FanmeException> {
        val consent =
            try {
                UserConsent.Consent.valueOf(params.consentName)
            } catch (_: IllegalArgumentException) {
                return Err(InvalidParameterException("Invalid consentName: $params.consentName"))
            }

        val userConsent =
            try {
                UserConsent.create(user = params.user, consent = consent)
            } catch (_: ConstraintViolationException) {
                logger.warn(
                    "User consent already exists for user: ${params.user.id}, consent: ${params.consentName}"
                )
                return Ok(null)
            }
        return Ok(userConsent)
    }
}
