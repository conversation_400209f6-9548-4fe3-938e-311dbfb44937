package jp.co.torihada.fanme.modules.fanme.services.audit

import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.*
import jp.co.torihada.fanme.modules.fanme.models.AuditGroupMetadata
import jp.co.torihada.fanme.modules.fanme.models.AuditObject
import jp.co.torihada.fanme.modules.fanme.models.AuditObject.AssetType

data class AuditGroupServiceAuditObject(
    val bucket: String,
    val filePath: String,
    val assetType: AssetType,
)

@ApplicationScoped
class AuditGroupService {
    /**
     * 監査グループと監査オブジェクトを作成
     *
     * @param userUid ユーザーID
     * @param auditType 監査種別
     * @param metadata メタデータ
     * @param auditObjects 監査オブジェクトのリスト
     * @return 作成された監査グループのID
     */
    fun createAuditGroup(
        userUid: String,
        auditType: AuditType,
        operationType: OperationType,
        metadata: AuditGroupMetadata,
        auditObjects: List<AuditGroupServiceAuditObject>,
    ): Long {
        // 監査グループの作成
        val auditGroup =
            AuditGroup.create(
                userUid = userUid,
                auditType = auditType,
                operationType = operationType,
                metadata = metadata,
            )

        // 監査オブジェクトの作成
        AuditObject.createBatch(auditGroup.id!!, auditObjects)

        return auditGroup.id!!
    }

    /**
     * 監査グループのステータスを更新
     *
     * @param auditGroupId 監査グループID
     * @param status 新しいステータス
     * @param comment 監査コメント
     * @param auditedUserUid 監査実施者のユーザーID
     */
    fun updateAuditStatus(
        auditGroupId: Long,
        status: AuditStatus,
        comment: String? = null,
        auditedUserUid: String? = null,
    ): AuditGroup {
        return AuditGroup.updateStatus(
            id = auditGroupId,
            status = status,
            comment = comment,
            auditedUserUid = auditedUserUid,
        )
    }
}
