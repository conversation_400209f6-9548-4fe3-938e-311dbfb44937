package jp.co.torihada.fanme.endpoints.console

import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.validation.Valid
import jakarta.validation.constraints.NotNull
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.dto.BaseResponseBody
import jp.co.torihada.fanme.exception.ErrorObject
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.controllers.ShopLimitationController
import jp.co.torihada.fanme.modules.console.usecases.UpdateShopLimitation
import jp.co.torihada.fanme.modules.shop.models.ShopLimitation
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/console/users/{account-identity}/shop-limitation/")
@Tag(name = "CONSOLE", description = "CONSOLE APIサーバー")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
class ShopLimitationEndpoint {

    @Inject lateinit var handler: ShopLimitationController

    data class UpdateShopLimitationRequest(
        @field:NotNull val fileCapacity: Int,
        @field:NotNull val fileQuantity: Int,
        @field:NotNull val isChekiExhibitable: Boolean,
    )

    data class UpdateShopLimitationResponse(
        override val data: ShopLimitation,
        override val errors: List<ErrorObject>,
    ) : BaseResponseBody<ShopLimitation>(data = data, errors = errors)

    @PUT
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE)
    fun updateShopLimitation(
        @PathParam("account-identity") accountIdentity: String,
        @Valid request: UpdateShopLimitationRequest,
    ): UpdateShopLimitationResponse {
        val params =
            UpdateShopLimitation.Input(
                accountIdentity = accountIdentity,
                fileCapacity = request.fileCapacity,
                fileQuantity = request.fileQuantity,
                isChekiExhibitable = request.isChekiExhibitable,
            )

        val updateShopLimitationResult = handler.updateShopLimitation(params)

        return UpdateShopLimitationResponse(data = updateShopLimitationResult, errors = emptyList())
    }
}
