package jp.co.torihada.fanme.modules.fanme.usecases.notificationSetting

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.fanme.models.NotificationSetting

@ApplicationScoped
class UpsertNotificationSetting {

    data class Input(val creatorUid: String, val enabled: Boolean)

    data class Output(val notificationSetting: NotificationSetting)

    fun execute(input: Input): Result<Output, FanmeException> {
        val notificationSetting = NotificationSetting.upsert(input.creatorUid, input.enabled)
        return Ok(Output(notificationSetting = notificationSetting))
    }
}
