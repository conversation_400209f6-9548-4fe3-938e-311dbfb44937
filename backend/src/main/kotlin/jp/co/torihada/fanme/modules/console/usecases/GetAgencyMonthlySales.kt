package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.YearMonth
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.stream.Collectors
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.InvalidParameterException
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.console.usecases.utils.SecurityUtils
import jp.co.torihada.fanme.modules.payment.controllers.MonthlySellerSalesController
import jp.co.torihada.fanme.modules.payment.controllers.TransactionController
import jp.co.torihada.fanme.modules.payment.models.Transaction
import jp.co.torihada.fanme.modules.payment.usecases.monthlysales.GetMonthlySellerSales
import jp.co.torihada.fanme.odata.OData
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.runBlocking

@ApplicationScoped
class GetAgencyMonthlySales {

    @Inject private lateinit var monthlySellerSalesController: MonthlySellerSalesController

    @Inject private lateinit var transactionController: TransactionController

    @Inject private lateinit var securityUtils: SecurityUtils

    data class AgencyMonthlySales(
        val yearMonth: String,
        val totalSalesAmount: Int,
        val totalPurchaserCount: Int,
        val totalPurchaseCount: Int,
        val monthOverMonthGrowthRate: BigDecimal?,
    )

    data class AgencyMonthlySalesResult(
        val agencyMonthlySalesList: List<AgencyMonthlySales>,
        val totalCount: Int? = null,
    )

    fun execute(
        agencyId: Long,
        currentUserUid: String,
        currentUserRole: ConsoleUserRole,
        from: YearMonth?,
        to: YearMonth?,
        odata: OData? = null,
    ): Result<AgencyMonthlySalesResult, FanmeException> {
        return try {
            securityUtils.validateAgentAccess(agencyId, currentUserUid, currentUserRole.value)

            val effectiveFrom = from ?: YearMonth.now().minusMonths(2)
            val effectiveTo = to ?: YearMonth.now()

            if (effectiveFrom.isAfter(effectiveTo)) {
                throw InvalidParameterException("'from' date must be before or equal to 'to' date")
            }

            val consoleUsers = ConsoleUser.findByAgencyId(agencyId)
            val sellerUids = consoleUsers.mapNotNull { it.user.uid }

            if (sellerUids.isEmpty()) {
                return Ok(
                    AgencyMonthlySalesResult(
                        agencyMonthlySalesList = emptyList(),
                        totalCount = if (odata?.count == true) 0 else null,
                    )
                )
            }

            val (monthlySellerSalesList, transactions) =
                fetchSalesData(
                    sellerUids,
                    effectiveFrom.format(DateTimeFormatter.ofPattern("yyyyMM")),
                    effectiveTo.format(DateTimeFormatter.ofPattern("yyyyMM")),
                )

            val (sellerSalesAmountByMonth, transactionsByMonth) =
                createMonthlyMaps(monthlySellerSalesList, transactions)
            val agencyMonthlySalesList =
                calculateMonthlySalesList(
                    sellerSalesAmountByMonth,
                    transactionsByMonth,
                    effectiveFrom.format(DateTimeFormatter.ofPattern("yyyyMM")),
                    effectiveTo.format(DateTimeFormatter.ofPattern("yyyyMM")),
                )

            val totalCount = if (odata?.count == true) agencyMonthlySalesList.size else null
            val paginatedMonthlySalesList = applyPagination(agencyMonthlySalesList, odata)

            Ok(
                AgencyMonthlySalesResult(
                    agencyMonthlySalesList = paginatedMonthlySalesList,
                    totalCount = totalCount,
                )
            )
        } catch (e: FanmeException) {
            Err(e)
        } catch (e: Exception) {
            Err(FanmeException(0, "${e::class.simpleName}: ${e.message}"))
        }
    }

    private fun fetchSalesData(
        sellerIds: List<String>,
        fromYearMonth: String,
        toYearMonth: String,
    ): Pair<List<GetMonthlySellerSales.MonthlySellerSales>, List<Transaction>> = runBlocking {
        coroutineScope {
            val salesDeferred = async {
                monthlySellerSalesController.getMonthlySellerSales(
                    sellerUserIds = sellerIds,
                    fromYearMonth = fromYearMonth,
                    toYearMonth = toYearMonth,
                )
            }

            val transactionsDeferred = async {
                transactionController.getSellerTransactions(
                    sellerUserIds = sellerIds,
                    fromYearMonth = fromYearMonth,
                    toYearMonth = toYearMonth,
                )
            }

            salesDeferred.await() to transactionsDeferred.await()
        }
    }

    private fun createMonthlyMaps(
        monthlySellerSalesList: List<GetMonthlySellerSales.MonthlySellerSales>,
        transactions: List<Transaction>,
    ): Pair<Map<String, Int>, Map<String, List<Transaction>>> {
        val sellerSalesAmountByMonth =
            monthlySellerSalesList
                .groupBy { it.yearMonth }
                .mapValues { entry -> entry.value.sumOf { it.sellerSalesAmount } }

        val transactionsByMonth =
            transactions.groupBy { transaction ->
                transaction.createdAt
                    ?.atZone(ZoneOffset.UTC)
                    ?.format(DateTimeFormatter.ofPattern("yyyyMM")) ?: ""
            }

        return sellerSalesAmountByMonth to transactionsByMonth
    }

    private fun calculateMonthlySalesList(
        sellerSalesAmountByMonth: Map<String, Int>,
        transactionsByMonth: Map<String, List<Transaction>>,
        fromYearMonth: String,
        toYearMonth: String,
    ): List<AgencyMonthlySales> {
        val allMonths = generateMonthRange(fromYearMonth, toYearMonth)

        val monthlyData =
            allMonths
                .parallelStream()
                .map { yearMonth ->
                    val yearMonthStr = yearMonth.format(DateTimeFormatter.ofPattern("yyyyMM"))
                    val salesAmount = sellerSalesAmountByMonth[yearMonthStr] ?: 0
                    val transactions = transactionsByMonth[yearMonthStr] ?: emptyList()

                    Triple(
                        yearMonthStr,
                        salesAmount,
                        MonthlySalesData(
                            purchaserCount = transactions.distinctBy { it.purchaserUserId }.size,
                            purchaseCount = transactions.size,
                        ),
                    )
                }
                .collect(Collectors.toList())

        return calculateWithGrowthRates(monthlyData).reversed()
    }

    private data class MonthlySalesData(val purchaserCount: Int, val purchaseCount: Int)

    private fun generateMonthRange(fromYearMonth: String, toYearMonth: String): List<YearMonth> {
        val months = mutableListOf<YearMonth>()
        var currentYm =
            YearMonth.parse(fromYearMonth.substring(0, 4) + "-" + fromYearMonth.substring(4, 6))
        val endYm = YearMonth.parse(toYearMonth.substring(0, 4) + "-" + toYearMonth.substring(4, 6))

        while (!currentYm.isAfter(endYm)) {
            months.add(currentYm)
            currentYm = currentYm.plusMonths(1)
        }

        return months
    }

    private fun calculateWithGrowthRates(
        monthlyData: List<Triple<String, Int, MonthlySalesData>>
    ): List<AgencyMonthlySales> {
        val monthlySalesList = mutableListOf<AgencyMonthlySales>()
        var previousMonthTotal: Int? = null

        for ((yearMonth, salesAmount, salesData) in monthlyData) {
            val growthRate = calculateGrowthRate(salesAmount, previousMonthTotal)

            monthlySalesList.add(
                AgencyMonthlySales(
                    yearMonth = yearMonth,
                    totalSalesAmount = salesAmount,
                    totalPurchaserCount = salesData.purchaserCount,
                    totalPurchaseCount = salesData.purchaseCount,
                    monthOverMonthGrowthRate = growthRate,
                )
            )

            previousMonthTotal = salesAmount
        }

        return monthlySalesList
    }

    private fun calculateGrowthRate(currentAmount: Int, previousAmount: Int?): BigDecimal? {
        return if (previousAmount != null && previousAmount > 0) {
            val rate = ((currentAmount - previousAmount).toDouble() / previousAmount) * 100
            BigDecimal(rate).setScale(1, RoundingMode.HALF_UP)
        } else {
            null
        }
    }

    private fun applyPagination(
        monthlySales: List<AgencyMonthlySales>,
        odata: OData?,
    ): List<AgencyMonthlySales> {
        if (odata == null) return monthlySales

        var result = monthlySales
        odata.skip?.let { result = result.drop(it) }
        odata.top?.let { result = result.take(it) }
        return result
    }
}
