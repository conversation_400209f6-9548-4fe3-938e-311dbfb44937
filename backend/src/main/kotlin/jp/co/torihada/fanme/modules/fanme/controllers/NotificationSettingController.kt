package jp.co.torihada.fanme.modules.fanme.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.usecases.notificationSetting.GetNotificationSetting
import jp.co.torihada.fanme.modules.fanme.usecases.notificationSetting.UpsertNotificationSetting

@ApplicationScoped
class NotificationSettingController {

    @Inject private lateinit var getNotificationSetting: GetNotificationSetting
    @Inject private lateinit var updateNotificationSetting: UpsertNotificationSetting

    fun getNotificationSetting(user: User): GetNotificationSetting.Output {
        val input = GetNotificationSetting.Input(creatorUid = user.uid!!)
        return getNotificationSetting.execute(input).getOrThrow()
    }

    fun upsertNotificationSetting(user: User, enabled: Boolean): UpsertNotificationSetting.Output {
        val input = UpsertNotificationSetting.Input(creatorUid = user.uid!!, enabled = enabled)
        return updateNotificationSetting.execute(input).getOrThrow()
    }
}
