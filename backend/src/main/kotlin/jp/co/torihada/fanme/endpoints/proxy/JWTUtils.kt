package jp.co.torihada.fanme.endpoints.proxy

import io.smallrye.jwt.auth.principal.JWTParser
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import org.jboss.logging.Logger

@ApplicationScoped
class JWTUtils {
    @Inject lateinit var jwtParser: JWTParser
    @Inject lateinit var logger: Logger

    fun extractSubjectFromToken(token: String?): String? {
        if (token == null) return null

        return try {
            val bearerToken = token.removePrefix("Bearer ")
            val jwt = jwtParser.parse(bearerToken)
            jwt.subject
        } catch (e: Exception) {
            logger.error("Error while parsing JWT token", e)
            null
        }
    }
}
