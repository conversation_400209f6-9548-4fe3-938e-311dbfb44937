package jp.co.torihada.fanme.modules.fanme.usecases.notification

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.modules.payment.models.Notification
import jp.co.torihada.fanme.modules.payment.models.Notification.NotificationType
import jp.co.torihada.fanme.modules.payment.models.NotificationMetadata

@ApplicationScoped
class CreateNotification {
    class Input(
        val type: NotificationType,
        val userUid: String,
        val title: String? = null,
        val content: String? = null,
        val senderUid: String? = null,
        val metadata: NotificationMetadata? = null,
        val navigationUrl: String? = null,
    )

    fun execute(input: Input): Result<Notification, Exception> {
        return Ok(
            Notification.create(
                type = input.type,
                userUid = input.userUid,
                title = input.title,
                content = input.content,
                senderUid = input.senderUid,
                metadata = input.metadata,
                navigationUrl = input.navigationUrl,
            )
        )
    }
}
