package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupController
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditGroupsListResult
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class GetAuditGroups {
    @Inject private lateinit var auditGroupController: AuditGroupController

    fun execute(odata: OData?): Result<AuditGroupsListResult, Nothing> {
        val auditGroups = auditGroupController.getAuditGroups(odata)
        return Ok(auditGroups)
    }
}
