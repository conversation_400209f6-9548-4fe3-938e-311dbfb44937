package jp.co.torihada.fanme.modules.payment.usecases.monthlysales

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.payment.models.MonthlySellerSale

@ApplicationScoped
class GetMonthlySellerSalesTotalCount {

    fun execute(sellerUserIds: List<String>): Result<Int, FanmeException> {
        val totalCount = MonthlySellerSale.getMonthlySellerSalesTotalCount(sellerUserIds).toInt()

        return Ok(totalCount)
    }
}
