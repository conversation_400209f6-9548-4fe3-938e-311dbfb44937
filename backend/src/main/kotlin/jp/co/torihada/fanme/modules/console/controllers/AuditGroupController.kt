package jp.co.torihada.fanme.modules.console.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.console.usecases.GetAuditGroups
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditGroupsListResult
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class AuditGroupController {

    @Inject private lateinit var getAuditGroups: GetAuditGroups

    fun getAuditGroups(odata: OData?): AuditGroupsListResult {
        val auditGroups = getAuditGroups.execute(odata).getOrThrow()
        return auditGroups
    }
}
