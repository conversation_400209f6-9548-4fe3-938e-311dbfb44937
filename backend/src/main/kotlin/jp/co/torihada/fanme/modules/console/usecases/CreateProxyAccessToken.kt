package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.ConsoleResourceNotFoundException
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ForbiddenAccessException
import jp.co.torihada.fanme.exception.UnprocessableException
import jp.co.torihada.fanme.modules.console.services.ProxyAccessTokenService
import jp.co.torihada.fanme.modules.console.usecases.utils.SecurityUtils
import jp.co.torihada.fanme.modules.fanme.controllers.UserController

@ApplicationScoped
class CreateProxyAccessToken {
    @Inject lateinit var proxyAccessTokenService: ProxyAccessTokenService

    @Inject lateinit var securityUtils: SecurityUtils

    @Inject lateinit var userController: UserController

    data class Input(
        val targetUserAccountIdentity: String,
        val currentUserRole: String?,
        val currentUserUid: String,
    )

    data class CreateProxyAccessTokenResponse(val proxyAccessToken: String)

    fun execute(input: Input): Result<CreateProxyAccessTokenResponse, FanmeException> {
        val targetUser =
            userController.getUserByAccountIdentity(input.targetUserAccountIdentity)
                ?: return Err(ConsoleResourceNotFoundException("Target user not found"))

        val currentUser =
            runCatching { userController.getUser(input.currentUserUid) }.getOrNull()
                ?: return Err(ForbiddenAccessException("Current user not found"))

        securityUtils.validateAgentForUserAccess(
            targetUser,
            input.currentUserRole,
            input.currentUserUid,
        )

        securityUtils.validateCreatorForCreatorAccess(
            targetUser,
            input.currentUserRole,
            input.currentUserUid,
        )

        val targetUserToken =
            targetUser.token?.idToken
                ?: return Err(UnprocessableException("Target user has no token"))

        val proxyAccessTokenData =
            ProxyAccessTokenService.ProxyAccessData(
                proxyUserAccountIdentity = currentUser.accountIdentity!!,
                targetUserAccountIdentity = targetUser.accountIdentity!!,
                targetUserFanmeToken = targetUserToken,
            )

        val proxyAccessToken = proxyAccessTokenService.createProxyAccessToken(proxyAccessTokenData)

        return Ok(CreateProxyAccessTokenResponse(proxyAccessToken = proxyAccessToken))
    }
}
