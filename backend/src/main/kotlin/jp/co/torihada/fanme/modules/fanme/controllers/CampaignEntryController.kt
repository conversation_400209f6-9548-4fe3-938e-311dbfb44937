package jp.co.torihada.fanme.modules.fanme.controllers

import BaseController
import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.modules.fanme.models.Campaign
import jp.co.torihada.fanme.modules.fanme.usecases.campaign.EntryShopCreation
import jp.co.torihada.fanme.modules.fanme.usecases.campaign.HasActiveCampaignEntry

data class EntryShopCreationRequest(val userUid: String, val campaignIdentity: String)

@ApplicationScoped
class CampaignEntryController() : BaseController() {

    @Inject private lateinit var entryShopCreation: EntryShopCreation
    @Inject private lateinit var hasActiveCampaignEntry: HasActiveCampaignEntry

    @Transactional(Transactional.TxType.REQUIRES_NEW)
    fun entryShopCreation(request: EntryShopCreationRequest): Response {
        entryShopCreation.execute(request.userUid, request.campaignIdentity)
        return Response.ok().build()
    }

    @Transactional(Transactional.TxType.REQUIRES_NEW)
    fun hasActiveCampaignEntry(userUid: String, actionType: String): Boolean {
        val actionTypeEnum = Campaign.ActionType.fromValue(actionType)
        return hasActiveCampaignEntry.execute(userUid, actionTypeEnum).getOrThrow()
    }
}
