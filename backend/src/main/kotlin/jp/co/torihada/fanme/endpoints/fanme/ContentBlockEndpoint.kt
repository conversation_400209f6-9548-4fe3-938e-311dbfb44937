package jp.co.torihada.fanme.endpoints.fanme

import com.fasterxml.jackson.annotation.JsonProperty
import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.Consumes
import jakarta.ws.rs.DELETE
import jakarta.ws.rs.GET
import jakarta.ws.rs.POST
import jakarta.ws.rs.PUT
import jakarta.ws.rs.Path
import jakarta.ws.rs.PathParam
import jakarta.ws.rs.Produces
import jakarta.ws.rs.QueryParam
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.Const.ContentBlockType
import jp.co.torihada.fanme.dto.BaseResponseBody
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.endpoints.fanme.ContentBlockEndpoint.GetContentBlockResponse.GetContentBlockData
import jp.co.torihada.fanme.exception.ErrorObject
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.lib.MaskConfig
import jp.co.torihada.fanme.modules.fanme.controllers.ContentBlockController
import jp.co.torihada.fanme.modules.fanme.models.ContentBlock
import jp.co.torihada.fanme.modules.fanme.models.ContentBlockDetail
import jp.co.torihada.fanme.modules.fanme.usecases.contentBlock.GetContentBlocks
import jp.co.torihada.fanme.modules.fanme.usecases.contentBlock.MoveContentBlock
import jp.co.torihada.fanme.modules.fanme.usecases.contentBlock.MoveContentBlockDetail
import jp.co.torihada.fanme.modules.fanme.usecases.contentBlock.ToggleContentBlockDisplayable
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/fanme/content_blocks")
@Tag(name = "FANME", description = "FANME APIサーバー")
class ContentBlockEndpoint {

    @Inject lateinit var securityIdentity: SecurityIdentity
    @Inject lateinit var handler: ContentBlockController
    @Inject lateinit var util: Util
    @Inject lateinit var maskConfig: MaskConfig

    data class GetContentBlockResponse(
        override val data: GetContentBlockData,
        override val errors: List<ErrorObject>,
    ) : BaseResponseBody<GetContentBlockData>(data = data, errors = errors) {
        data class GetContentBlockData(val contentBlocks: List<GetContentBlocks.ContentBlock>)
    }

    @GET
    @Produces(MediaType.APPLICATION_JSON)
    fun getContentBlocks(
        /* TODO 本来はQueryParamではなくPathParamで取得する(/fanme/{creator_account_identity}/content_blocks)
         * しかし、createの方のパスも変わるためデグレを避けるためにQueryParamで暫定対応
         */
        @QueryParam("creator_account_identity") creatorAccountIdentity: String
    ): GetContentBlockResponse {
        val contentBlocks = handler.getUserContentBlocks(creatorAccountIdentity)
        maskConfig.enabled = true
        return GetContentBlockResponse(
            data = GetContentBlockData(contentBlocks = contentBlocks),
            errors = emptyList(),
        )
    }

    @Path("/current")
    @GET
    @RolesAllowed("LoginUser")
    @Produces(MediaType.APPLICATION_JSON)
    fun getCurrentUserContentBlocks(): GetContentBlockResponse {
        val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
        val contentBlocks = handler.getCurrentUserContentBlocks(userUid)
        return GetContentBlockResponse(
            data = GetContentBlockData(contentBlocks = contentBlocks),
            errors = emptyList(),
        )
    }

    data class CreateContentWithDetailRequest(
        @JsonProperty("content_block_type") val contentBlockType: Long,
        @JsonProperty("title") val title: String,
        @JsonProperty("description") val description: String?,
        @JsonProperty("app_description") val appDescription: String?,
        @JsonProperty("url") val url: String,
        @JsonProperty("icon_url") val iconUrl: String?,
    )

    @POST
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/create_with_detail")
    fun createWithDetail(requestBody: CreateContentWithDetailRequest): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()

            val blockType = ContentBlockType.fromId(requestBody.contentBlockType)
            if (blockType == null) throw FanmeException(0, "Invalid content block type")

            val result =
                handler.createContentBlockWithDetail(
                    ContentBlockController.CreateContentBlockWithDetailRequest(
                        creatorUid = userUid,
                        contentBlockType = blockType,
                        title = requestBody.title,
                        description = requestBody.description,
                        appDescription = requestBody.appDescription,
                        url = requestBody.url,
                        iconUrl = requestBody.iconUrl,
                    )
                )
            val entity = ResponseEntity(result, "content_block")
            return Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    data class CreateContentBlockRequest(val contentBlockType: String)

    data class CreateContentBlockResponse(
        override val data: CreateContentBlockData,
        override val errors: List<ErrorObject>,
    ) :
        BaseResponseBody<CreateContentBlockResponse.CreateContentBlockData>(
            data = data,
            errors = errors,
        ) {
        data class CreateContentBlockData(val contentBlock: ContentBlock)
    }

    @POST
    @Path("/create")
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun createContentBlock(requestBody: CreateContentBlockRequest): CreateContentBlockResponse {
        val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
        val blockType =
            ContentBlockType.fromStr(requestBody.contentBlockType)
                ?: throw FanmeException(0, "Invalid content block type")

        val result =
            handler.createContentBlock(
                ContentBlockController.CreateContentBlockRequest(
                    userUid = userUid,
                    contentBlockType = blockType,
                )
            )
        return CreateContentBlockResponse(
            data = CreateContentBlockResponse.CreateContentBlockData(contentBlock = result),
            errors = emptyList(),
        )
    }

    data class UpdateContentBlockDetailRequest(
        val contentBlockDetailId: Long,
        val title: String?,
        val description: String?,
        val appDescription: String?,
        val url: String?,
        val iconUrl: String?,
    )

    data class UpdateContentBlockDetailResponse(
        override val data: UpdateContentBlockDetailData,
        override val errors: List<ErrorObject>,
    ) :
        BaseResponseBody<UpdateContentBlockDetailResponse.UpdateContentBlockDetailData>(
            data = data,
            errors = errors,
        ) {
        data class UpdateContentBlockDetailData(val contentBlockDetail: ContentBlockDetail)
    }

    @PUT
    @Path("/detail")
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun updateContentBlockDetail(
        requestBody: UpdateContentBlockDetailRequest
    ): UpdateContentBlockDetailResponse {
        val result =
            handler.updateContentBlockDetail(
                ContentBlockController.UpdateContentBlockDetailRequest(
                    contentBlockDetailId = requestBody.contentBlockDetailId,
                    title = requestBody.title,
                    description = requestBody.description,
                    appDescription = requestBody.appDescription,
                    url = requestBody.url,
                    iconUrl = requestBody.iconUrl,
                )
            )
        return UpdateContentBlockDetailResponse(
            data =
                UpdateContentBlockDetailResponse.UpdateContentBlockDetailData(
                    contentBlockDetail = result
                ),
            errors = emptyList(),
        )
    }

    data class MoveContentBlockRequest(
        val displayOrderNum: Int? = null,
        val upDown: String? = null,
        val contentBlockIds: List<Long>? = null,
    )

    data class MoveContentBlockResponse(
        override val data: MoveContentBlockData,
        override val errors: List<ErrorObject>,
    ) :
        BaseResponseBody<MoveContentBlockResponse.MoveContentBlockData>(
            data = data,
            errors = errors,
        ) {
        data class MoveContentBlockData(val contentBlocks: MoveContentBlock.Output)
    }

    @PUT
    @Path("/move")
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun moveContentBlock(requestBody: MoveContentBlockRequest): MoveContentBlockResponse {
        val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
        val result =
            handler.moveContentBlock(
                userUid = userUid,
                displayOrderNum = requestBody.displayOrderNum,
                upDown = requestBody.upDown,
                contentBlockIds = requestBody.contentBlockIds,
            )
        return MoveContentBlockResponse(
            data = MoveContentBlockResponse.MoveContentBlockData(contentBlocks = result),
            errors = emptyList(),
        )
    }

    data class MoveContentBlockDetailResponse(
        override val data: MoveContentBlockDetailData,
        override val errors: List<ErrorObject>,
    ) :
        BaseResponseBody<MoveContentBlockDetailResponse.MoveContentBlockDetailData>(
            data = data,
            errors = errors,
        ) {
        data class MoveContentBlockDetailData(val contentBlock: MoveContentBlockDetail.Output)
    }

    data class MoveContentBlockDetailRequest(
        val fromContentBlockDetailId: Long? = null,
        val toContentBlockDetailId: Long? = null,
        val contentBlockDetailIds: List<Long>? = null,
    )

    @PUT
    @Path("/detail/move")
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun moveContentBlockDetail(
        requestBody: MoveContentBlockDetailRequest
    ): MoveContentBlockDetailResponse {
        val result =
            handler.moveContentBlockDetail(
                fromContentBlockDetailId = requestBody.fromContentBlockDetailId,
                toContentBlockDetailId = requestBody.toContentBlockDetailId,
                contentBlocksIds = requestBody.contentBlockDetailIds,
            )
        return MoveContentBlockDetailResponse(
            data = MoveContentBlockDetailResponse.MoveContentBlockDetailData(contentBlock = result),
            errors = emptyList(),
        )
    }

    data class ToggleContentBlockDisplayableResponse(
        override val data: ToggleContentBlockDisplayableData,
        override val errors: List<ErrorObject>,
    ) :
        BaseResponseBody<ToggleContentBlockDisplayableResponse.ToggleContentBlockDisplayableData>(
            data = data,
            errors = errors,
        ) {
        data class ToggleContentBlockDisplayableData(
            val contentBlock: ToggleContentBlockDisplayable.Output
        )
    }

    data class ToggleContentBlockDisplayableRequest(
        val displayOrderNum: Int? = null,
        val contentBlockId: Long? = null,
        val displayable: Boolean? = null,
    )

    @PUT
    @Path("/toggle_displayable")
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun toggleContentBlockDisplayable(
        requestBody: ToggleContentBlockDisplayableRequest
    ): ToggleContentBlockDisplayableResponse {
        val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
        val result =
            handler.toggleContentBlockDisplayable(
                userUid = userUid,
                displayOrderNum = requestBody.displayOrderNum,
                contentBlockId = requestBody.contentBlockId,
                displayable = requestBody.displayable,
            )
        return ToggleContentBlockDisplayableResponse(
            data =
                ToggleContentBlockDisplayableResponse.ToggleContentBlockDisplayableData(
                    contentBlock = result
                ),
            errors = emptyList(),
        )
    }

    data class DeleteContentBlockResponse(
        override val data: DeleteContentBlockData,
        override val errors: List<ErrorObject>,
    ) :
        BaseResponseBody<DeleteContentBlockResponse.DeleteContentBlockData>(
            data = data,
            errors = errors,
        ) {
        data class DeleteContentBlockData(val success: Boolean)
    }

    @DELETE
    @Path("/{content_block_id}")
    @RolesAllowed("LoginUser")
    @Produces(MediaType.APPLICATION_JSON)
    fun deleteContentBlock(
        @PathParam("content_block_id") contentBlockId: Long
    ): DeleteContentBlockResponse {
        handler.deleteContentBlock(contentBlockId)
        return DeleteContentBlockResponse(
            data = DeleteContentBlockResponse.DeleteContentBlockData(success = true),
            errors = emptyList(),
        )
    }
}
