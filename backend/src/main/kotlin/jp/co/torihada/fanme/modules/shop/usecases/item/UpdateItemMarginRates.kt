package jp.co.torihada.fanme.modules.shop.usecases.item

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.InvalidParameterException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.models.Item

@ApplicationScoped
class UpdateItemMarginRates {

    data class Input(val itemIdRates: List<Pair<Long, Float>>)

    // Response DTO for margin-rate update result
    data class ItemMarginRateResult(val itemId: Long, val marginRate: Float)

    fun execute(params: Input): Result<List<ItemMarginRateResult>, Exception> {
        if (params.itemIdRates.isEmpty()) {
            return Err(InvalidParameterException("items must not be empty"))
        }

        val invalidRateExists = params.itemIdRates.any { it.second < 0.0f || it.second > 1.0f }
        if (invalidRateExists) {
            return Err(InvalidParameterException("marginRate must be between 0.0 and 1.0"))
        }

        val itemIds = params.itemIdRates.map { it.first }
        val items = Item.findByIds(itemIds)

        if (items.size != itemIds.size) {
            return Err(ResourceNotFoundException("Item"))
        }

        val rateMap = params.itemIdRates.toMap()

        items.forEach { item ->
            val newRate = rateMap[item.id]
            if (newRate != null) {
                item.marginRate = newRate
            }
        }

        return Ok(items.map { ItemMarginRateResult(it.id!!, it.marginRate) })
    }
}
