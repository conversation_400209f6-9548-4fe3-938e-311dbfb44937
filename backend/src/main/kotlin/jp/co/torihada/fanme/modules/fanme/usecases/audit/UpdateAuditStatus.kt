package jp.co.torihada.fanme.modules.fanme.usecases.audit

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditStatus
import jp.co.torihada.fanme.modules.fanme.services.audit.AuditGroupService

@ApplicationScoped
class UpdateAuditStatus @Inject constructor(private val auditGroupService: AuditGroupService) {
    fun execute(
        auditGroupId: Long,
        status: AuditStatus,
        comment: String? = null,
        auditedUserUid: String? = null,
    ): Result<AuditGroup, Exception> {
        return try {
            val auditGroup =
                auditGroupService.updateAuditStatus(
                    auditGroupId = auditGroupId,
                    status = status,
                    comment = comment,
                    auditedUserUid = auditedUserUid,
                )
            Ok(auditGroup)
        } catch (e: Exception) {
            Err(e)
        }
    }
}
