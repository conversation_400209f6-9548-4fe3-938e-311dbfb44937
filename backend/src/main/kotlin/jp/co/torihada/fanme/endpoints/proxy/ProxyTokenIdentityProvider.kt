package jp.co.torihada.fanme.endpoints.proxy

import io.quarkus.security.identity.AuthenticationRequestContext
import io.quarkus.security.identity.IdentityProvider
import io.quarkus.security.identity.SecurityIdentity
import io.quarkus.security.identity.request.BaseAuthenticationRequest
import io.quarkus.security.runtime.QuarkusSecurityIdentity
import io.smallrye.mutiny.Uni
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import org.jboss.logging.Logger

data class ProxyAuthenticationRequest(
    val targetUserAccountIdentity: String,
    val targetUserFanmeToken: String? = null,
) : BaseAuthenticationRequest()

@ApplicationScoped
class ProxyTokenIdentityProvider : IdentityProvider<ProxyAuthenticationRequest> {
    @Inject lateinit var jwtUtils: JWTUtils
    @Inject lateinit var logger: Logger

    override fun getRequestType(): Class<ProxyAuthenticationRequest> {
        return ProxyAuthenticationRequest::class.java
    }

    override fun authenticate(
        request: ProxyAuthenticationRequest,
        context: AuthenticationRequestContext,
    ): Uni<SecurityIdentity> {

        return context.runBlocking {
            val builder = QuarkusSecurityIdentity.builder()

            val targetUid =
                jwtUtils.extractSubjectFromToken(request.targetUserFanmeToken)
                    ?: request.targetUserAccountIdentity

            if (request.targetUserFanmeToken == null) {
                logger.warn("No fanme token provided, using targetUserAccountIdentity: $targetUid")
            }

            val jwtPrincipal = CustomJWTPrincipal(userUid = targetUid)

            builder.setPrincipal(jwtPrincipal)

            builder.build()
        }
    }
}
