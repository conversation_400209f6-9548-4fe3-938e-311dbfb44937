package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.Const.DEFAULT_USER_NAME
import jp.co.torihada.fanme.exception.ConsoleResourceNotFoundException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.console.usecases.utils.SecurityUtils
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.payment.Const as PaymentConst
import jp.co.torihada.fanme.modules.payment.controllers.TransactionController
import jp.co.torihada.fanme.modules.payment.models.Transaction
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.controllers.OrderController
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class GetUserTransactions
@Inject
constructor(
    private val transactionController: TransactionController,
    private val orderController: OrderController,
    private val userController: UserController,
    private val securityUtils: SecurityUtils,
) {

    data class PurchaseHistory(
        val purchaseDate: String,
        val purchaserName: String,
        val itemNames: List<String>,
        val sellerSalesAmount: Int,
        val quantity: Int,
        val paymentType: String? = null,
    )

    data class Output(val purchaseHistories: List<PurchaseHistory>, val totalCount: Long? = null)

    fun execute(
        accountIdentity: String,
        currentUserUid: String,
        currentUserRole: ConsoleUserRole,
        odata: OData?,
    ): Result<Output, Exception> {
        return try {
            val targetUser =
                userController.getUserByAccountIdentity(accountIdentity)
                    ?: throw ResourceNotFoundException("User")
            val targetUserUid = targetUser.uid ?: throw ConsoleResourceNotFoundException("User UID")

            securityUtils.validateAgentForUserAccess(
                targetUser,
                currentUserRole.value,
                currentUserUid,
            )

            val transactions =
                transactionController.getSellerTransactions(
                    sellerUserIds = listOf(targetUserUid),
                    top = odata?.top,
                    skip = odata?.skip,
                )

            val purchaserUserIds = transactions.mapNotNull { it.purchaserUserId }.distinct()

            val purchaserUsers = userController.getUsers(purchaserUserIds).associateBy { it.uid }

            val purchaseHistories =
                transactions.map { transaction ->
                    val itemNames = getItemNamesForTransaction(transaction)

                    val sellerSalesAmount =
                        transaction.amount
                            ?: throw ConsoleResourceNotFoundException(
                                "Transaction amount for transaction ${transaction.id}"
                            )

                    val purchaserUserId =
                        transaction.purchaserUserId
                            ?: throw ConsoleResourceNotFoundException(
                                "Purchaser user ID for transaction ${transaction.id}"
                            )

                    val purchaserName = purchaserUsers[purchaserUserId]?.name ?: DEFAULT_USER_NAME

                    val paymentType =
                        transaction.paymentType?.let {
                            PaymentConst.PaymentType.fromValue(it)?.displayName ?: it
                        }

                    PurchaseHistory(
                        purchaseDate = transaction.createdAt.toString(),
                        purchaserName = purchaserName,
                        itemNames = itemNames,
                        sellerSalesAmount = sellerSalesAmount,
                        quantity = itemNames.size,
                        paymentType = paymentType,
                    )
                }
            val totalCount =
                if (odata?.count == true) {
                    transactionController.countBySellerUserId(targetUserUid)
                } else {
                    null
                }

            Ok(Output(purchaseHistories = purchaseHistories, totalCount = totalCount))
        } catch (e: Exception) {
            Err(e)
        }
    }

    private fun getItemNamesForTransaction(transaction: Transaction): List<String> {
        // ミニアプリの場合、transactionにappNameが設定されている
        if (transaction.appName != null) {
            return listOf(transaction.appName!!)
        }

        val transactionId = transaction.id ?: return listOf("Shop購入")
        val orders = orderController.getOrders(transactionId)
        val order = orders.firstOrNull() ?: return listOf("Shop購入")

        val items =
            order.purchasedItems
                .filter { it.status == Util.PurchasedItemStatus.PAYSUCCESS.value }
                .map { it.item.name }

        return items.ifEmpty { listOf("Shop購入") }
    }
}
