package jp.co.torihada.fanme.modules.shop.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.validation.Valid
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.NotNull
import jp.co.torihada.fanme.modules.shop.usecases.item.UpdateItemMarginRates

@ApplicationScoped
class ItemMarginRateController {

    @Inject private lateinit var updateItemMarginRates: UpdateItemMarginRates

    data class ItemRateParam(
        val itemId: Long,
        @field:NotNull @field:DecimalMin("0.0") @field:DecimalMax("1.0") val marginRate: Float,
    )

    @Transactional
    fun updateItemMarginRates(
        @Valid items: List<ItemRateParam>
    ): List<UpdateItemMarginRates.ItemMarginRateResult> {
        val pairs = items.map { it.itemId to it.marginRate }
        return updateItemMarginRates.execute(UpdateItemMarginRates.Input(pairs)).getOrThrow()
    }
}
