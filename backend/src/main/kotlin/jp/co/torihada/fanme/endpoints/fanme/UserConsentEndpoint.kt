package jp.co.torihada.fanme.endpoints.fanme

import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.validation.Valid
import jakarta.validation.constraints.NotNull
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.dto.BaseResponseBody
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.fanme.controllers.UserConsentController
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.models.UserConsent
import jp.co.torihada.fanme.modules.fanme.usecases.consent.GetConsentsUseCase
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/fanme/user-consents")
@Tag(name = "FANME", description = "FANME APIサーバー")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
class UserConsentEndpoint {

    @Inject private lateinit var securityIdentity: SecurityIdentity
    @Inject private lateinit var handler: UserConsentController
    @Inject lateinit var util: Util

    data class GetConsentsResponse(val consents: List<GetConsentsUseCase.Consent>)

    @GET
    @Path("/")
    @RolesAllowed("LoginUser")
    fun getConsents(): BaseResponseBody<GetConsentsResponse> {
        val userUuid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
        val user = User.findByUuid(userUuid) ?: throw ResourceNotFoundException("User")

        val result = handler.getConsents(user)

        return BaseResponseBody(
            data = GetConsentsResponse(consents = result.consents),
            errors = emptyList(),
        )
    }

    data class CreateUserConsentRequest(@field:NotNull val consentName: String)

    data class CreateUserConsentResponse(val userConsent: UserConsent?)

    @POST
    @RolesAllowed("LoginUser")
    fun createUserConsent(
        @Valid request: CreateUserConsentRequest
    ): BaseResponseBody<CreateUserConsentResponse> {
        val userUuid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
        val user = User.findByUuid(userUuid) ?: throw ResourceNotFoundException("User")

        val result = handler.createUserConsent(user, request.consentName)

        return BaseResponseBody(
            data = CreateUserConsentResponse(userConsent = result),
            errors = emptyList(),
        )
    }
}
