package jp.co.torihada.fanme.modules.fanme.usecases.user

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.models.UserAttributes

@ApplicationScoped
class UpdatePurposeAndUserAttributes {
    data class Input(val userUuid: String, val userAttributes: UserAttributes)

    fun execute(input: Input): Result<User, Exception> {
        val user = User.findByUuid(input.userUuid) ?: return Err(ResourceNotFoundException("User"))

        val result =
            User.updatePurposeAndUserAttributes(user = user, userAttributes = input.userAttributes)

        return Ok(result)
    }
}
