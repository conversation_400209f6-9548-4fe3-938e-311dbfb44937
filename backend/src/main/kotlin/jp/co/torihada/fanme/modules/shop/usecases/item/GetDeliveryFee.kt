package jp.co.torihada.fanme.modules.shop.usecases.item

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.shop.models.ItemType
import jp.co.torihada.fanme.modules.shop.services.DeliveryService

@ApplicationScoped
class GetDeliveryFee {
    @Inject private lateinit var deliveryService: DeliveryService

    data class DeliveryFee(val deliveryFee: Int?)

    fun execute(itemType: ItemType): Result<DeliveryFee, FanmeException> {
        val deliveryFee = deliveryService.getDeliveryFeeByItemType(itemType)
        val output = DeliveryFee(deliveryFee = deliveryFee)
        return Ok(output)
    }
}
