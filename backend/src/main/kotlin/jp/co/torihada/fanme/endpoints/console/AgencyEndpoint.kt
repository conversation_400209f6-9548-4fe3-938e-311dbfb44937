package jp.co.torihada.fanme.endpoints.console

import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.PathParam
import jakarta.ws.rs.Produces
import jakarta.ws.rs.QueryParam
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.dto.BaseResponseBody
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.ErrorObject
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.controllers.AgencyController
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.usecases.GetAgencyMonthlySales.AgencyMonthlySales
import jp.co.torihada.fanme.modules.console.usecases.GetAgencySales
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.odata.OData
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/console/agencies")
@Tag(name = "CONSOLE", description = "CONSOLE APIサーバー")
class AgencyEndpoint {

    @Inject private lateinit var handler: AgencyController

    @Inject private lateinit var requestContext: ContainerRequestContext

    @Inject private lateinit var securityIdentity: SecurityIdentity

    @Inject private lateinit var util: Util

    class GetAgenciesResponse(data: GetAgenciesData, errors: List<ErrorObject> = emptyList()) :
        BaseResponseBody<GetAgenciesResponse.GetAgenciesData>(data, errors) {
        data class GetAgenciesData(val agencies: List<Agency>)
    }

    @GET
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    @Produces(MediaType.APPLICATION_JSON)
    fun getAgencies(): GetAgenciesResponse {
        val odata = requestContext.getProperty("odata") as OData?
        val currentUserUid = util.getCurrentUserUid(securityIdentity)
        val currentUserRole = util.getCurrentUserRole(securityIdentity)

        if (currentUserUid == null || currentUserRole == null) {
            throw UnAuthorizedException()
        }

        val result = handler.getAgencies(currentUserUid, currentUserRole, odata)
        return GetAgenciesResponse(data = GetAgenciesResponse.GetAgenciesData(agencies = result))
    }

    class GetAgencyUsersResponse(
        data: GetAgencyUsersData,
        errors: List<ErrorObject> = emptyList(),
    ) : BaseResponseBody<GetAgencyUsersResponse.GetAgencyUsersData>(data, errors) {
        data class GetAgencyUsersData(val users: List<User>)
    }

    @GET
    @Path("/{agency-id}/users")
    @Produces(MediaType.APPLICATION_JSON)
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    fun getAgencyUsers(@PathParam("agency-id") agencyId: Long): GetAgencyUsersResponse {
        val currentUserUid = util.getCurrentUserUid(securityIdentity)
        val currentUserRole = util.getCurrentUserRole(securityIdentity)

        if (currentUserUid == null || currentUserRole == null) {
            throw UnAuthorizedException()
        }

        val users = handler.getAgencyUsers(agencyId, currentUserUid, currentUserRole)
        return GetAgencyUsersResponse(
            data = GetAgencyUsersResponse.GetAgencyUsersData(users = users)
        )
    }

    class GetAgencySalesResponse(
        data: GetAgencySalesData,
        errors: List<ErrorObject> = emptyList(),
    ) : BaseResponseBody<GetAgencySalesResponse.GetAgencySalesData>(data, errors) {
        data class GetAgencySalesData(val agencySales: GetAgencySales.AgencySales)
    }

    @GET
    @Path("/{agency-id}/sales")
    @Produces(MediaType.APPLICATION_JSON)
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    fun getAgencySales(@PathParam("agency-id") agencyId: Long): GetAgencySalesResponse {
        val currentUserUid = util.getCurrentUserUid(securityIdentity)
        val currentUserRole = util.getCurrentUserRole(securityIdentity)

        if (currentUserUid == null || currentUserRole == null) {
            throw UnAuthorizedException()
        }

        val agencySales = handler.getAgencySales(agencyId, currentUserUid, currentUserRole)
        return GetAgencySalesResponse(
            data = GetAgencySalesResponse.GetAgencySalesData(agencySales = agencySales)
        )
    }

    class GetAgencyMonthlySalesResponse(
        data: GetAgencyMonthlySalesData,
        errors: List<ErrorObject> = emptyList(),
        metadata: Map<String, Any>? = null,
    ) :
        BaseResponseBody<GetAgencyMonthlySalesResponse.GetAgencyMonthlySalesData>(
            data,
            errors,
            metadata,
        ) {
        data class GetAgencyMonthlySalesData(val agencyMonthlySalesList: List<AgencyMonthlySales>)
    }

    @GET
    @Path("/{agency-id}/sales/monthly")
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    @Produces(MediaType.APPLICATION_JSON)
    fun getAgencyMonthlySales(
        @PathParam("agency-id") agencyId: Long,
        @QueryParam("from") from: String?,
        @QueryParam("to") to: String?,
    ): GetAgencyMonthlySalesResponse {
        val currentUserUid = util.getCurrentUserUid(securityIdentity)
        val currentUserRole = util.getCurrentUserRole(securityIdentity)

        if (currentUserUid == null || currentUserRole == null) {
            throw UnAuthorizedException()
        }

        val odata = requestContext.getProperty("odata") as OData?
        val result =
            handler.getAgencyMonthlySales(
                agencyId,
                currentUserUid,
                currentUserRole,
                from,
                to,
                odata,
            )

        val metadata = result.totalCount?.let { mapOf("@odata.count" to it) }
        return GetAgencyMonthlySalesResponse(
            data =
                GetAgencyMonthlySalesResponse.GetAgencyMonthlySalesData(
                    agencyMonthlySalesList = result.agencyMonthlySalesList
                ),
            metadata = metadata,
        )
    }
}
