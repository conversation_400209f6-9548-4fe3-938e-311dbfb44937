package jp.co.torihada.fanme.modules.shop.usecases.item

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.controllers.NotificationController
import jp.co.torihada.fanme.modules.fanme.usecases.notification.CreateNotification
import jp.co.torihada.fanme.modules.payment.models.Notification.NotificationType
import jp.co.torihada.fanme.modules.shop.models.Item

@ApplicationScoped
class RejectItem {
    @Inject private lateinit var notificationController: NotificationController

    fun execute(itemId: Long, rejectComment: String): Result<Item, Exception> {
        val item = Item.reject(itemId)
        // 通知を送る
        val itemName = item.name
        val itemNameText = if (!itemName.isEmpty()) "「$itemName」" else ""
        val commentText = if (!rejectComment.isEmpty()) "（理由: $rejectComment）" else ""
        item.shop.creatorUid?.let {
            notificationController.createNotification(
                CreateNotification.Input(
                    type = NotificationType.REJECTION,
                    userUid = it,
                    content = "ガイドライン違反により" + itemNameText + "の公開が停止されました" + commentText,
                )
            )
        }
        return Ok(item)
    }
}
