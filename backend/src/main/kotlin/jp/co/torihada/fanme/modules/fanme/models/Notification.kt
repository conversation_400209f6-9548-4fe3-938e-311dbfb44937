package jp.co.torihada.fanme.modules.payment.models

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size
import jp.co.torihada.fanme.modules.fanme.utils.JsonUtil
import org.eclipse.microprofile.openapi.annotations.media.Schema

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class AppField(
    @field:Schema(description = "ID", example = "1") var id: Long = 0,
    @field:Schema(description = "名前", example = "appName") var name: String = "",
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class NotificationMetadata(
    @field:Schema(description = "アプリフィールド") var appField: AppField? = null,
    @field:Schema(description = "URL", example = "https://example.com") var url: String? = null,
)

@PersistenceUnit(name = "payment")
@Entity
@Table(name = "notifications")
class Notification : BaseModel() {
    enum class NotificationType(val value: Int) {
        REJECTION(-1), // 注意却下系
        MANAGEMENT(0), // 運営系
        ANY_COMPLETE(1), // 何か完了系
        MONEY(2), // お金系
        REQUEST(3), // 依頼系
        GENERAL(4); // 汎用的通知系

        companion object {
            fun fromValue(value: Int): NotificationType {
                return values().find { it.value == value }
                    ?: throw IllegalArgumentException("Invalid NotificationType value: $value")
            }
        }
    }

    @NotNull
    @Size(max = 10)
    @Column(name = "tenant", nullable = false)
    var tenant: String? = "fanme"

    // NOTE: notifications はコンテキスト上 fanme モジュールにあるべきである（つまり fanme データベースにあるべき）
    // しかし、物理的な配置としては、payment データベース上に存在する
    // そのためモジュールの配置としては fanme 上に置くが、接続先は payment となる
    // パッケージは payment.models としており、 @ManyToOne などは使えないことに注意が必要
    @Size(max = 50) @Column(name = "user_id") var userUid: String? = null

    @Size(max = 50) @Column(name = "sender_id") var senderUid: String? = null

    @NotNull
    @Convert(converter = NotificationTypeConverter::class)
    @Column(name = "type", nullable = false)
    var type: NotificationType? = null

    @Size(max = 255) @Column(name = "title") var title: String? = null

    @Column(name = "content", columnDefinition = "TEXT") var content: String? = null

    @Column(name = "metadata", columnDefinition = "json") var metadata: String? = "{}"

    @Size(max = 255) @Column(name = "navigation_url") var navigationUrl: String? = null

    // Jackson用のプロパティ - JSONシリアライズ時にはこれが使用される
    @JsonProperty("metadata_object")
    fun getMetadataAsObject(): NotificationMetadata? {
        val map = JsonUtil.parseJsonToMap(metadata)
        return if (map is Map<*, *>) {
            try {
                val mapper = jacksonObjectMapper()
                mapper.convertValue(map, NotificationMetadata::class.java)
            } catch (e: Exception) {
                null
            }
        } else {
            null
        }
    }

    companion object : PanacheCompanion<Notification> {
        fun create(
            type: NotificationType,
            userUid: String,
            title: String? = null,
            content: String? = null,
            senderUid: String? = null,
            metadata: NotificationMetadata? = null,
            navigationUrl: String? = null,
        ): Notification {
            val notification =
                Notification().apply {
                    this.type = type
                    this.userUid = userUid
                    this.title = title
                    this.content = content
                    this.senderUid = senderUid
                    this.metadata =
                        if (metadata != null) jacksonObjectMapper().writeValueAsString(metadata)
                        else "{}"
                    this.navigationUrl = navigationUrl
                }
            notification.persistAndFlush()
            return notification
        }
    }
}

@Converter(autoApply = true)
class NotificationTypeConverter : AttributeConverter<Notification.NotificationType, Int> {
    override fun convertToDatabaseColumn(attribute: Notification.NotificationType?): Int? {
        return attribute?.value
    }

    override fun convertToEntityAttribute(dbData: Int?): Notification.NotificationType? {
        return dbData?.let { value ->
            Notification.NotificationType.values().find { it.value == value }
        }
    }
}
