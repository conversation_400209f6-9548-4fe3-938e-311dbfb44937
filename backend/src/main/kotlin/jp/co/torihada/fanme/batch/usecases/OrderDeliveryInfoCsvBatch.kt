package jp.co.torihada.fanme.batch.usecases

import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.nio.file.Files
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.modules.fanme.controllers.FanmeCustomerController
import jp.co.torihada.fanme.modules.shop.Config as ShopConfig
import jp.co.torihada.fanme.modules.shop.Const
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.external.GcpStorageClient
import jp.co.torihada.fanme.modules.shop.models.ItemType
import jp.co.torihada.fanme.modules.shop.models.PurchasedItem
import jp.co.torihada.fanme.modules.shop.services.SmapoFileUploadService
import jp.co.torihada.fanme.modules.shop.services.aws.S3
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.jboss.logging.Logger

@ApplicationScoped
class OrderDeliveryInfoCsvBatch {

    @Inject private lateinit var logger: Logger
    @Inject private lateinit var config: Config
    @Inject private lateinit var shopConfig: ShopConfig
    @Inject private lateinit var s3: S3
    @Inject private lateinit var fanmeCustomerController: FanmeCustomerController
    @Inject private lateinit var smapoFileUploadService: SmapoFileUploadService

    @RestClient private lateinit var gcpStorageClient: GcpStorageClient

    fun execute(targetDate: LocalDate) {
        val jstZone = ZoneId.of("Asia/Tokyo")

        val startTimeJst = targetDate.atStartOfDay(jstZone)
        val endTimeJst = startTimeJst.plusDays(1)

        // DBにはUTCで保存されているため、JSTの0時をUTCに変換
        val startDateTimeUtc = startTimeJst.toInstant()
        val endDateTimeUtc = endTimeJst.toInstant()

        logger.info(
            "Search purchasedItems with successful payment status between $startDateTimeUtc and $endDateTimeUtc"
        )

        // 購入日時が昨日の0時から23時59分59秒までのチェキのPurchasedItemを取得
        val purchasedItems =
            PurchasedItem.findOrderedItemsByItemTypeAndTerms(
                ItemType.CHEKI,
                startDateTimeUtc,
                endDateTimeUtc,
            )

        logger.info("Found ${purchasedItems.size} purchasedItems with successful payment status")

        val dateStr = DateTimeFormatter.ofPattern("yyyyMMdd").format(startTimeJst)
        val dateDir = "${startTimeJst.year}/${startTimeJst.monthValue}/${startTimeJst.dayOfMonth}/"

        // 通常のチェキCSVを処理
        processChekiCsv(purchasedItems, dateStr, dateDir)

        // 【prefix】がついたアイテムのCSVを処理
        processOtherPhysicalItemCsv(purchasedItems, dateStr, dateDir)

        logger.info("OrderDeliveryInfoCsvBatch end")
    }

    private fun processChekiCsv(
        purchasedItems: List<PurchasedItem>,
        dateStr: String,
        dateDir: String,
    ) {
        // CSVに入れるデータを作成
        val csvContent = buildCsvContent(purchasedItems)

        val tempFile = Files.createTempFile("cheki_purchase_data", ".csv")
        Files.write(tempFile, csvContent.toByteArray())

        val remotePath = "${config.envKind()}/${dateDir}cheki_purchase_data$dateStr.csv"

        try {
            // S3へのアップロード
            smapoFileUploadService.uploadFileToS3(remotePath, tempFile).getOrElse { e ->
                logger.error("Failed to upload file to S3: $remotePath", e)
            }
            logger.info("Finish uploading regular cheki CSV to S3")

            // smapo GCPへアップロード
            val gcpPath = remotePath.substringAfter("${config.envKind()}/")
            smapoFileUploadService.uploadFileToSmapo(gcpPath, tempFile, true).getOrElse { e ->
                logger.error("Failed to upload file to GCP Storage: $gcpPath", e)
            }
            logger.info(
                "Finish uploading regular cheki CSV to GCP Storage for Smapo: ${shopConfig.smapoGcpBucketName()}/fanme/${dateDir}cheki_purchase_data$dateStr.csv"
            )
        } finally {
            try {
                Files.delete(tempFile)
            } catch (e: Exception) {
                logger.warn("Failed to delete temporary file", e)
            }
        }
    }

    private fun processOtherPhysicalItemCsv(
        purchasedItems: List<PurchasedItem>,
        dateStr: String,
        dateDir: String,
    ) {
        // 【prefix】がついたアイテムのCSVデータを作成
        val physicalItemCsvContent = buildPhysicalItemCsvContent(purchasedItems)

        val tempFile = Files.createTempFile("physical_item_purchase_data", ".csv")
        Files.write(tempFile, physicalItemCsvContent.toByteArray())

        val remotePath = "${config.envKind()}/${dateDir}physical_item_purchase_data$dateStr.csv"

        try {
            // S3へのアップロードのみ
            smapoFileUploadService.uploadFileToS3(remotePath, tempFile).getOrElse { e ->
                logger.error("Failed to upload physical item file to S3: $remotePath", e)
            }
            logger.info("Finish uploading physical item CSV to S3")
        } finally {
            try {
                Files.delete(tempFile)
            } catch (e: Exception) {
                logger.warn("Failed to delete temporary file for physical items", e)
            }
        }
    }

    private fun buildCsvContent(purchasedItems: List<PurchasedItem>): String {
        return buildCsvContent(purchasedItems) { item ->
            // 【prefix】がついたものは除外
            Const.ChekiPhysicalItemCost.entries
                .mapNotNull { it.prefix }
                .none { prefix -> item.item.name.contains("【$prefix】") }
        }
    }

    private fun buildPhysicalItemCsvContent(purchasedItems: List<PurchasedItem>): String {
        return buildCsvContent(purchasedItems) { item ->
            // 【prefix】がついたもののみ対象
            Const.ChekiPhysicalItemCost.entries
                .mapNotNull { it.prefix }
                .any { prefix -> item.item.name.contains("【$prefix】") }
        }
    }

    private fun buildCsvContent(
        purchasedItems: List<PurchasedItem>,
        itemFilter: (PurchasedItem) -> Boolean,
    ): String {
        val header = "オーダーID,商品ID,商品タイプ,商品名,購入数,商品単価,合計,購入日時,名前（購入者）,郵便番号（購入者）,住所（購入者）,電話番号（購入者）"
        val customers =
            fanmeCustomerController.listFanmeCustomers(
                purchasedItems.map { it.purchaserUid }.distinct()
            )

        val rows =
            purchasedItems.filter(itemFilter).mapNotNull { item ->
                val customer = customers.firstOrNull { it.creatorUid == item.purchaserUid }
                if (customer != null) {
                    listOf(
                            item.order.id,
                            item.item.id,
                            "チェキ",
                            item.item.name,
                            item.quantity,
                            item.item.price,
                            item.item.price * item.quantity,
                            item.purchasedAt?.let { Util.toJSTLocalDateTimeString(it) } ?: "",
                            "${customer.lastName} ${customer.firstName}",
                            customer.postalCode,
                            "${customer.prefecture}${customer.city}${customer.street}${customer.building ?: ""}",
                            "'${customer.phoneNumber}",
                        )
                        .joinToString(",")
                } else {
                    logger.warn(
                        "Customer information not found for purchaserUid: ${item.purchaserUid}"
                    )
                    null
                }
            }

        return listOf(header, rows.joinToString("\n")).joinToString("\n")
    }
}
