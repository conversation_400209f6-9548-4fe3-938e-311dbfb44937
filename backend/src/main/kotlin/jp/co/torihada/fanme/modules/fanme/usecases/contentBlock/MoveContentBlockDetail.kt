package jp.co.torihada.fanme.modules.fanme.usecases.contentBlock

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.ContentBlock
import jp.co.torihada.fanme.modules.fanme.models.ContentBlockGroup
import org.eclipse.microprofile.openapi.annotations.media.Schema

@ApplicationScoped
class MoveContentBlockDetail {
    class Input(val contentBlockDetailIds: List<Long>)

    @Schema(name = "MoveContentBlockDetail_ContentBlocks")
    class Output(val contentBlock: ContentBlock) {
        @Schema(name = "MoveContentBlockDetail_ContentBlock")
        class ContentBlock(val id: Long, val contentBlockDetails: List<ContentBlockDetail>) {
            @Schema(name = "MoveContentBlockDetail_ContentBlockDetail")
            class ContentBlockDetail(val id: Long, val contentGroupNumber: Int)
        }
    }

    fun execute(input: Input): Result<Output, Exception> {
        val targetContentGroup =
            ContentBlockGroup.findByContentBlockDetailId(input.contentBlockDetailIds.first())
        val targetContentBlock =
            targetContentGroup?.contentBlock
                ?: return Err(ResourceNotFoundException("ContentBlock"))

        val targetContentBlockGroups = ContentBlockGroup.findByContentBlock(targetContentBlock)
        if (targetContentBlockGroups.isEmpty()) {
            return Err(ResourceNotFoundException("ContentBlockGroup"))
        }
        // setで比較
        val existingDetailIds = targetContentBlockGroups.map { it.contentBlockDetail!!.id }.toSet()
        if (existingDetailIds.toSet() != input.contentBlockDetailIds.toSet()) {
            return Err(Exception("Invalid content block detail IDs"))
        }

        // 全てのContentBlockGroupのcontentGroupNumberを一時的な値に設定
        val tempOrderNum = 999_999
        targetContentBlockGroups.forEachIndexed { index, group ->
            ContentBlockGroup.updateContentGroupNumber(
                contentBlockGroup = group,
                contentGroupNumber = tempOrderNum - index,
            )
        }

        // 指定されたIDの順番に並べ替え
        input.contentBlockDetailIds.forEachIndexed { index, detailId ->
            val group =
                targetContentBlockGroups.find { it.contentBlockDetail!!.id == detailId }
                    ?: return Err(ResourceNotFoundException("ContentBlockGroup"))
            ContentBlockGroup.updateContentGroupNumber(
                contentBlockGroup = group,
                contentGroupNumber = index + 1,
            )
        }
        val updatedContentBlock =
            ContentBlock.findById(targetContentBlock.id!!)
                ?: return Err(ResourceNotFoundException("ContentBlock"))

        return Ok(
            Output(
                contentBlock =
                    Output.ContentBlock(
                        id = updatedContentBlock.id!!,
                        contentBlockDetails =
                            updatedContentBlock.contentBlockGroups
                                .sortedBy { it.contentGroupNumber }
                                .map { group ->
                                    Output.ContentBlock.ContentBlockDetail(
                                        id = group.contentBlockDetail!!.id!!,
                                        contentGroupNumber = group.contentGroupNumber!!,
                                    )
                                },
                    )
            )
        )
    }
}
