package jp.co.torihada.fanme

import io.quarkus.runtime.Quarkus
import io.quarkus.runtime.annotations.QuarkusMain
import jp.co.torihada.fanme.batch.main.*

@QuarkusMain
object Main {
    @JvmStatic
    fun main(args: Array<String>) {
        if (args.isNotEmpty()) {
            val batchClass = args[0]
            when (batchClass) {
                "MonthlySellerSalesBatchMain" ->
                    Quarkus.run(MonthlySellerSalesBatchMain::class.java, *args)
                "MonthlyTenantSalesBatchMain" ->
                    Quarkus.run(MonthlyTenantSalesBatchMain::class.java, *args)
                "MonthlySellerSalesMergeMain" ->
                    Quarkus.run(MonthlySellerSalesMergeMain::class.java, *args)
                "MonthlyTenantSalesMergeMain" ->
                    Quarkus.run(MonthlyTenantSalesMergeMain::class.java, *args)
                "SellerGmoTransferStatusUpdateMain" ->
                    Quarkus.run(SellerGmoTransferStatusUpdateMain::class.java, *args)
                "SellerSalesExpireMain" -> Quarkus.run(SellerSalesExpireMain::class.java, *args)
                "SendCvsPaymentReminderEmailBatchMain" ->
                    Quarkus.run(SendCvsPaymentReminderEmailBatchMain::class.java, *args)
                "TransferDeadLineContactBatchMain" ->
                    Quarkus.run(TransferDeadLineContactBatchMain::class.java, *args)
                "OrderDeliveryInfoCsvBatchMain" ->
                    Quarkus.run(OrderDeliveryInfoCsvBatchMain::class.java, *args)
                "OrderPrintGachaDeliveryDataBatchMain" ->
                    Quarkus.run(OrderPrintGachaDeliveryDataBatchMain::class.java, *args)
                else -> throw IllegalArgumentException("Invalid batch name: $batchClass")
            }
        } else {
            // 引数がない場合はAPIモードを実行
            Quarkus.run(*args)
        }
    }
}
