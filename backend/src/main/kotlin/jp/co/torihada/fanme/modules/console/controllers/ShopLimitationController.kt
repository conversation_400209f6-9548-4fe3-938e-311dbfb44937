package jp.co.torihada.fanme.modules.console.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.console.usecases.UpdateShopLimitation
import jp.co.torihada.fanme.modules.shop.models.ShopLimitation

@ApplicationScoped
class ShopLimitationController
@Inject
constructor(private val updateShopLimitation: UpdateShopLimitation) {

    fun updateShopLimitation(params: UpdateShopLimitation.Input): ShopLimitation {
        return updateShopLimitation.execute(params).getOrThrow()
    }
}
