package jp.co.torihada.fanme.modules.fanme.services.aws

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.time.Duration
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest

@ApplicationScoped
class S3 {
    @Inject private lateinit var s3Presigner: S3Presigner

    fun getObjectUri(bucket: String, fileUri: String): String {
        return getPresignedUrl(bucket, fileUri)
    }

    private fun getPresignedUrl(bucket: String, key: String): String {
        val request =
            GetObjectPresignRequest.builder()
                .signatureDuration(Duration.ofDays(1))
                .getObjectRequest(GetObjectRequest.builder().bucket(bucket).key(key).build())
                .build()
        return s3Presigner.presignGetObject(request).url().toString()
    }
}
