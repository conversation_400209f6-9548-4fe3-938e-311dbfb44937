package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size
import jp.co.torihada.fanme.modules.fanme.Const.USER_UID_MAX_LENGTH

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "notification_settings")
class NotificationSetting : BaseModel() {

    @Size(max = USER_UID_MAX_LENGTH)
    @NotNull
    @Column(name = "creator_uid", nullable = false, length = USER_UID_MAX_LENGTH, unique = true)
    var creatorUid: String? = null

    @NotNull @Column(nullable = false) var enabled: Boolean = false

    companion object : PanacheCompanion<NotificationSetting> {

        fun findByCreatorUid(creatorUid: String): NotificationSetting? {
            return find("creatorUid = ?1", creatorUid).firstResult()
        }

        fun upsert(creatorUid: String, enabled: Boolean): NotificationSetting {
            val record = findByCreatorUid(creatorUid)
            return if (record != null) {
                record.enabled = enabled
                record.persist()
                record
            } else {
                NotificationSetting().apply {
                    this.creatorUid = creatorUid
                    this.enabled = enabled
                    persist()
                }
            }
        }
    }
}
