package jp.co.torihada.fanme.modules.fanme.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.models.Profile
import jp.co.torihada.fanme.modules.fanme.models.ProfileCover
import jp.co.torihada.fanme.modules.fanme.models.ProfileCoverImage
import jp.co.torihada.fanme.modules.fanme.usecases.profile.GetProfile
import jp.co.torihada.fanme.modules.fanme.usecases.profile.UpdateProfile
import jp.co.torihada.fanme.modules.fanme.usecases.profile.UpdateProfileCover
import jp.co.torihada.fanme.modules.fanme.usecases.profile.UpdateProfileCoverImage
import jp.co.torihada.fanme.modules.fanme.usecases.user.GetCurrentUser

@ApplicationScoped
class ProfileController {
    @Inject private lateinit var getProfile: GetProfile
    @Inject private lateinit var updateProfile: UpdateProfile
    @Inject private lateinit var getCurrentUser: GetCurrentUser
    @Inject private lateinit var updateProfileCover: UpdateProfileCover

    @Inject private lateinit var updateProfileCoverImage: UpdateProfileCoverImage

    fun getUserProfile(userUuid: String): GetProfile.Profile {
        val user = getCurrentUser.execute(GetCurrentUser.Input(userUuid)).getOrThrow()
        return getProfile.execute(GetProfile.Input(userId = user.id!!)).getOrThrow()
    }

    fun updateUserProfile(
        userUuid: String,
        bio: String? = null,
        headerImage: String? = null,
        snsLinkColor: String? = null,
    ): Profile {
        val user = getCurrentUser.execute(GetCurrentUser.Input(userUuid)).getOrThrow()
        val input =
            UpdateProfile.Input(
                userId = user.id!!,
                bio = bio,
                headerImage = headerImage,
                snsLinkColor = snsLinkColor,
            )
        return updateProfile.execute(input).getOrThrow()
    }

    fun updateUserProfileCover(
        userUuid: String,
        coverImageVisibility: Boolean? = null,
        brightness: String? = null,
    ): ProfileCover {
        val user = getCurrentUser.execute(GetCurrentUser.Input(userUuid)).getOrThrow()
        val input =
            UpdateProfileCover.Input(
                userId = user.id!!,
                coverImageVisibility = coverImageVisibility,
                brightness = brightness,
            )
        return updateProfileCover.execute(input).getOrThrow()
    }

    fun updateUserProfileCoverImage(
        userUuid: String,
        coverImage: String,
        resourceType: String,
    ): ProfileCoverImage {
        val user = getCurrentUser.execute(GetCurrentUser.Input(userUuid)).getOrThrow()
        val input =
            UpdateProfileCoverImage.Input(
                userId = user.id!!,
                coverImage = coverImage,
                resourceType = resourceType,
            )
        return updateProfileCoverImage.execute(input).getOrThrow()
    }
}
