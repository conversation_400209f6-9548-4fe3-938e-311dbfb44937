package jp.co.torihada.fanme.endpoints.console

import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.validation.Valid
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.dto.BaseResponseBody
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.endpoints.console.request.UpdateShopItemTypeMarginRatesRequest
import jp.co.torihada.fanme.exception.ConsoleResourceNotFoundException
import jp.co.torihada.fanme.exception.ErrorObject
import jp.co.torihada.fanme.exception.ForbiddenAccessException
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.controllers.CommissionControlController
import jp.co.torihada.fanme.modules.console.usecases.GetAllShopItems.AllShopItemsResponse
import jp.co.torihada.fanme.modules.console.usecases.UpdateItemMarginRates.ItemMarginRateResult
import jp.co.torihada.fanme.modules.console.usecases.UpdateShopItemTypeMarginRate.ShopItemTypeMarginRateResult
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/console/commission-control/creator/{account-identity}")
@Tag(name = "CONSOLE", description = "CONSOLE APIサーバー")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
class CommissionControlEndpoint {
    @Inject lateinit var commissionControlController: CommissionControlController
    @Inject lateinit var securityIdentity: SecurityIdentity
    @Inject lateinit var util: Util

    data class GetAllShopItemsResponse(
        override val data: GetAllShopItemsData,
        override val errors: List<ErrorObject>,
    ) :
        BaseResponseBody<GetAllShopItemsResponse.GetAllShopItemsData>(
            data = data,
            errors = errors,
        ) {
        data class GetAllShopItemsData(val creatorShopItems: AllShopItemsResponse)
    }

    @PUT
    @Path("/shop/{shop-id}")
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE)
    fun updateShopItemTypeMarginRates(
        @PathParam("account-identity") accountIdentity: String,
        @PathParam("shop-id") shopId: Long,
        @Valid request: UpdateShopItemTypeMarginRatesRequest,
    ): BaseResponseBody<List<ShopItemTypeMarginRateResult>> {
        val currentUserUid =
            util.getCurrentUserUid(securityIdentity)
                ?: throw ConsoleResourceNotFoundException("User not found")
        val currentUserRole =
            util.getCurrentUserRole(securityIdentity)
                ?: throw ForbiddenAccessException("Role not found")

        val shopItemTypeMarginRateResult =
            commissionControlController.updateShopItemTypeMarginRates(
                accountIdentity,
                shopId,
                request.marginRates,
                currentUserUid,
                currentUserRole,
            )
        return BaseResponseBody(data = shopItemTypeMarginRateResult, errors = emptyList())
    }

    data class ItemMarginRateItem(val itemId: Long, val marginRate: Float)

    @PUT
    @Path("/items")
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE)
    fun updateItemMarginRates(
        @PathParam("account-identity") accountIdentity: String,
        @Valid items: List<ItemMarginRateItem>,
    ): BaseResponseBody<List<ItemMarginRateResult>> {
        val currentUserUid =
            util.getCurrentUserUid(securityIdentity)
                ?: throw ConsoleResourceNotFoundException("User not found")
        val currentUserRole =
            util.getCurrentUserRole(securityIdentity)
                ?: throw ForbiddenAccessException("Role not found")

        val results =
            commissionControlController.updateItemMarginRates(
                accountIdentity = accountIdentity,
                items = items,
                currentUserUid = currentUserUid,
                currentUserRole = currentUserRole,
            )

        return BaseResponseBody(data = results, errors = emptyList())
    }

    @GET
    @Path("/shop/items")
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE)
    @Produces(MediaType.APPLICATION_JSON)
    fun getAllShopItems(
        @PathParam("account-identity") accountIdentity: String
    ): GetAllShopItemsResponse {

        val allShopItems = commissionControlController.getAllShopItems(accountIdentity)
        return GetAllShopItemsResponse(
            data = GetAllShopItemsResponse.GetAllShopItemsData(creatorShopItems = allShopItems),
            errors = emptyList(),
        )
    }
}
