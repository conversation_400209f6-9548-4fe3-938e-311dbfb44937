package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.*
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.shop.controllers.ItemController
import jp.co.torihada.fanme.modules.shop.controllers.ShopController
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.usecases.shop.GetShop

@ApplicationScoped
class GetAllShopItems
@Inject
constructor(
    private val itemController: ItemController,
    private val shopController: ShopController,
    private val userController: UserController,
) {

    data class Input(val accountIdentity: String)

    fun execute(params: Input): Result<AllShopItemsResponse, ConsoleException> {
        return try {
            val user =
                userController.getUserByNotDeletedAccountIdentity(params.accountIdentity)
                    ?: throw ResourceNotFoundException("User")

            val shop = shopController.getShop(user.uid!!)

            val items = itemController.getAllItemsFromConsole(shop.id)

            val dto = AllShopItemsResponse(shop = shop, items = items)
            Ok(dto)
        } catch (e: ConsoleException) {
            Err(e)
        } catch (e: Exception) {
            Err(ConsoleException(0, "${e::class.simpleName}: ${e.message}"))
        }
    }

    data class AllShopItemsResponse(val shop: GetShop.ShopForGetShop, val items: List<Item>)
}
