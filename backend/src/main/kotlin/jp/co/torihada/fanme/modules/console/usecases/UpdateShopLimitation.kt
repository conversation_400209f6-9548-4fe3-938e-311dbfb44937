package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.ConsoleException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.shop.controllers.ShopController
import jp.co.torihada.fanme.modules.shop.controllers.ShopLimitationController
import jp.co.torihada.fanme.modules.shop.models.ShopLimitation
import jp.co.torihada.fanme.modules.shop.usecases.shopLimitation.UpdateShopLimitation as ShopUpdateShopLimitation

@ApplicationScoped
class UpdateShopLimitation
@Inject
constructor(
    private val userController: UserController,
    private val shopController: ShopController,
    private val shopLimitationController: ShopLimitationController,
) {

    data class Input(
        val accountIdentity: String,
        val fileCapacity: Int,
        val fileQuantity: Int,
        val isChekiExhibitable: Boolean,
    )

    fun execute(params: Input): Result<ShopLimitation, ConsoleException> {

        val user =
            userController.getUserByNotDeletedAccountIdentity(params.accountIdentity)
                ?: throw ResourceNotFoundException("User")

        val shop = shopController.getShop(user.uid!!)

        val updateInput =
            ShopUpdateShopLimitation.InputUpdateShopLimitation(
                shopId = shop.id,
                fileCapacity = params.fileCapacity,
                fileQuantity = params.fileQuantity,
                isChekiExhibitable = params.isChekiExhibitable,
            )
        val shopLimitation = shopLimitationController.updateShopLimitation(updateInput)
        return Ok(shopLimitation)
    }
}
