package jp.co.torihada.fanme.modules.shop.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import jp.co.torihada.fanme.Const.ContentBlockType
import jp.co.torihada.fanme.modules.fanme.controllers.ContentBlockController
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.shop.Config
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.CREATOR_UID_IS_REQUIRED
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.CREATOR_UID_TOO_MANY_LENGTH
import jp.co.torihada.fanme.modules.shop.controllers.requests.ShopRequest
import jp.co.torihada.fanme.modules.shop.models.Shop
import jp.co.torihada.fanme.modules.shop.usecases.shop.CreateShop
import jp.co.torihada.fanme.modules.shop.usecases.shop.GetShop
import jp.co.torihada.fanme.modules.shop.usecases.shop.RejectShop
import jp.co.torihada.fanme.modules.shop.usecases.shop.UpdateShop
import org.jboss.logging.Logger

@ApplicationScoped
class ShopController {
    @Inject private lateinit var logger: Logger
    @Inject private lateinit var getShop: GetShop
    @Inject private lateinit var createShop: CreateShop
    @Inject private lateinit var updateShop: UpdateShop
    @Inject private lateinit var rejectShop: RejectShop
    @Inject private lateinit var contentBlockController: ContentBlockController
    @Inject private lateinit var userController: UserController
    @Inject private lateinit var config: Config

    fun getShop(
        @NotBlank(message = CREATOR_UID_IS_REQUIRED)
        @Size(max = 50, message = CREATOR_UID_TOO_MANY_LENGTH)
        creatorUid: String
    ): GetShop.ShopForGetShop {
        return getShop.execute(GetShop.Input(creatorUid)).getOrThrow()
    }

    data class CreateShopResponse(val shop: Shop, val isContentBlockCreated: Boolean)

    fun createShop(@Valid request: ShopRequest.CreateShop): CreateShopResponse {
        val shop = createShopRecord(request)
        val isContentBlockCreated = createShopContentBlockRecord(request)
        return CreateShopResponse(shop, isContentBlockCreated)
    }

    @Transactional
    fun createShopRecord(request: ShopRequest.CreateShop): Shop {
        return createShop
            .execute(
                CreateShop.Input(
                    creatorUid = request.creatorUid,
                    name = request.name,
                    description = request.description,
                    headerImageUri = request.headerImageUri,
                    message = request.message,
                    campaignIdentity = request.campaignIdentity,
                )
            )
            .getOrThrow()
    }

    @Transactional
    fun updateShop(@Valid request: ShopRequest.UpdateShop): Shop {
        return updateShop
            .execute(
                UpdateShop.Input(
                    creatorUid = request.creatorUid,
                    name = request.name,
                    description = request.description,
                    headerImageUri = request.headerImageUri,
                    message = request.message,
                )
            )
            .getOrThrow()
    }

    @Transactional(Transactional.TxType.REQUIRES_NEW)
    fun rejectShop(shopId: Long, rejectComment: String): Shop {
        return rejectShop.execute(shopId, rejectComment).getOrThrow()
    }

    private fun createShopContentBlockRecord(request: ShopRequest.CreateShop): Boolean {
        try {
            val user = userController.getUser(request.creatorUid)

            val shopUrl = "${config.shopFrontUrl()}/@${user.accountIdentity}"

            contentBlockController.createContentBlockWithDetail(
                ContentBlockController.CreateContentBlockWithDetailRequest(
                    creatorUid = request.creatorUid,
                    contentBlockType = ContentBlockType.ONE_BLOCK,
                    title = request.name,
                    description = "none",
                    appDescription = "none",
                    url = shopUrl,
                    iconUrl = request.headerImageUri,
                )
            )
            return true
        } catch (e: Exception) {
            logger.error("Failed to create ContentBlock for shop: ${e.message}", e)
            return false
        }
    }
}
