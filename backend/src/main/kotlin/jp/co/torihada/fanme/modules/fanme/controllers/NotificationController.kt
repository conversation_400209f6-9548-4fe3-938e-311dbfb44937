package jp.co.torihada.fanme.modules.fanme.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.modules.fanme.usecases.notification.CreateNotification
import jp.co.torihada.fanme.modules.fanme.usecases.notification.CreateNotification.Input
import jp.co.torihada.fanme.modules.payment.models.Notification

@ApplicationScoped
class NotificationController {
    @Inject private lateinit var createNotification: CreateNotification

    @Transactional(Transactional.TxType.REQUIRES_NEW)
    fun createNotification(input: Input): Notification {
        return createNotification.execute(input).getOrThrow()
    }
}
