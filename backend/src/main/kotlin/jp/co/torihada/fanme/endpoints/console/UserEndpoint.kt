package jp.co.torihada.fanme.endpoints.console

import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.PathParam
import jakarta.ws.rs.Produces
import jakarta.ws.rs.QueryParam
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.dto.BaseResponseBody
import jp.co.torihada.fanme.exception.ErrorObject
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.controllers.MonthlySellerSaleController
import jp.co.torihada.fanme.modules.console.controllers.TransactionController
import jp.co.torihada.fanme.modules.console.controllers.UserController
import jp.co.torihada.fanme.modules.console.usecases.GetUser
import jp.co.torihada.fanme.modules.console.usecases.GetUserMonthlySales
import jp.co.torihada.fanme.modules.console.usecases.GetUserTransactions
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.odata.OData
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/console/users")
@Tag(name = "CONSOLE", description = "CONSOLE APIサーバー")
class UserEndpoint {

    @Inject lateinit var userController: UserController
    @Inject lateinit var monthlySellerSaleController: MonthlySellerSaleController
    @Inject lateinit var transactionController: TransactionController
    @Inject lateinit var requestContext: ContainerRequestContext

    data class UserResponse(val userResponse: GetUser.ConsoleGetUserResponse)

    data class UsersByPartialAccountIdentityResponse(
        val usersByPartialAccountIdentityResponse: List<User>
    )

    @GET
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE)
    @Path("/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    fun getUser(@PathParam("id") id: Long): BaseResponseBody<UserResponse> {

        val userResponse = userController.getUser(id)
        return BaseResponseBody(data = UserResponse(userResponse), errors = emptyList())
    }

    @GET
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE)
    @Produces(MediaType.APPLICATION_JSON)
    fun getUsersByPartialAccountIdentity(
        @QueryParam("partial-account-identity") partialAccountIdentity: String?
    ): BaseResponseBody<UsersByPartialAccountIdentityResponse> {

        val usersByPartialAccountIdentityResponse =
            userController.getUsersByPartialAccountIdentity(partialAccountIdentity)
        return BaseResponseBody(
            data = UsersByPartialAccountIdentityResponse(usersByPartialAccountIdentityResponse),
            errors = emptyList(),
        )
    }

    class GetUserMonthlySalesResponse(
        data: GetUserMonthlySalesData,
        errors: List<ErrorObject> = emptyList(),
        metadata: Map<String, Any>? = null,
    ) :
        BaseResponseBody<GetUserMonthlySalesResponse.GetUserMonthlySalesData>(
            data,
            errors,
            metadata,
        ) {
        data class GetUserMonthlySalesData(
            val userMonthlySalesList: List<GetUserMonthlySales.UserMonthlySales>
        )
    }

    @GET
    @Path("/{account-identity}/sales/monthly")
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    @Produces(MediaType.APPLICATION_JSON)
    fun getUserMonthlySales(
        @PathParam("account-identity") accountIdentity: String,
        @QueryParam("from") from: String?,
        @QueryParam("to") to: String?,
        @QueryParam("\$top") top: Int?,
        @QueryParam("\$skip") skip: Int?,
        @QueryParam("\$count") count: Boolean?,
    ): GetUserMonthlySalesResponse {
        val odata = OData(top = top, skip = skip, count = count)
        val result =
            monthlySellerSaleController.getUserMonthlySales(accountIdentity, from, to, odata)
        val metadata = result.totalCount?.let { mapOf("@odata.count" to it) }
        return GetUserMonthlySalesResponse(
            data =
                GetUserMonthlySalesResponse.GetUserMonthlySalesData(
                    userMonthlySalesList = result.monthlySalesList
                ),
            metadata = metadata,
        )
    }

    class GetUserTransactionsResponse(
        data: GetUserTransactionsData,
        errors: List<ErrorObject> = emptyList(),
        metadata: Map<String, Any>? = null,
    ) :
        BaseResponseBody<GetUserTransactionsResponse.GetUserTransactionsData>(
            data,
            errors,
            metadata,
        ) {
        data class GetUserTransactionsData(
            val purchaseHistories: List<GetUserTransactions.PurchaseHistory>
        )
    }

    @GET
    @Path("/{account-identity}/sales/transactions")
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    @Produces(MediaType.APPLICATION_JSON)
    fun getUserTransaction(
        @PathParam("account-identity") accountIdentity: String,
        @QueryParam("\$top") top: Int?,
        @QueryParam("\$skip") skip: Int?,
        @QueryParam("\$count") count: Boolean?,
    ): GetUserTransactionsResponse {
        val effectiveTop = top ?: 20
        val odata = OData(top = effectiveTop, skip = skip, count = count)
        val result = transactionController.getUserTransactions(accountIdentity, odata)
        val metadata = result.totalCount?.let { mapOf("@odata.count" to it) }
        return GetUserTransactionsResponse(
            data =
                GetUserTransactionsResponse.GetUserTransactionsData(
                    purchaseHistories = result.purchaseHistories
                ),
            metadata = metadata,
        )
    }
}
