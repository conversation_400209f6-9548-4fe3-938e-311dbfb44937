package jp.co.torihada.fanme.endpoints.proxy

import io.smallrye.jwt.auth.principal.DefaultJWTCallerPrincipal
import org.jose4j.jwt.JwtClaims

class CustomJWTPrincipal(userUid: String) : DefaultJWTCallerPrincipal(createJwtClaims(userUid)) {

    companion object {
        private fun createJwtClaims(userUid: String): JwtClaims {
            val claims = JwtClaims()
            claims.subject = userUid
            claims.setIssuedAtToNow()
            claims.setExpirationTimeMinutesInTheFuture(2f) // 2 minutes, proxy token TTL
            return claims
        }
    }
}
