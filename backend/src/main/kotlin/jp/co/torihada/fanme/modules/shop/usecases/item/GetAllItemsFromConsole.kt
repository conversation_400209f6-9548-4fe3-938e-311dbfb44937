package jp.co.torihada.fanme.modules.shop.usecases.item

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.modules.shop.models.Item

@ApplicationScoped
class GetAllItemsFromConsole {

    fun execute(shopId: Long): Result<List<Item>, Exception> {
        val items = Item.findByShopId(shopId, false, null, null)
        return Ok(items)
    }
}
