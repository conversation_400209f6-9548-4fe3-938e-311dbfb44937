package jp.co.torihada.fanme.modules.console.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.endpoints.shop.request.item.digitalBundle.CreateOrUpdateItemRequest
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.console.usecases.UpdateCreatorShopItem
import jp.co.torihada.fanme.modules.shop.models.Item

@ApplicationScoped
class CreatorShopItemsController {

    @Inject lateinit var updateCreatorShopItem: UpdateCreatorShopItem

    fun updateItem(
        accountIdentity: String,
        currentUserUid: String,
        currentUserRole: ConsoleUserRole,
        itemId: Long,
        requestBody: CreateOrUpdateItemRequest,
    ): Item =
        updateCreatorShopItem
            .execute(
                UpdateCreatorShopItem.Input(
                    accountIdentity = accountIdentity,
                    currentUserUid = currentUserUid,
                    currentUserRole = currentUserRole,
                    itemId = itemId,
                    requestBody = requestBody,
                )
            )
            .getOrThrow()
}
