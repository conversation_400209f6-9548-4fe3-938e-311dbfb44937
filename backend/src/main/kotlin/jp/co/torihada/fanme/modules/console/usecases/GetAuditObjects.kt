package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.fanme.controllers.AuditObjectController as FanmeAuditObjectController
import jp.co.torihada.fanme.modules.fanme.models.AuditObject

@ApplicationScoped
class GetAuditObjects {
    @Inject private lateinit var auditObjectController: FanmeAuditObjectController

    fun execute(auditGroupId: Long): Result<List<AuditObject>, FanmeException> {
        val auditObjects = auditObjectController.getAuditObjects(auditGroupId)
        return Ok(auditObjects)
    }
}
