package jp.co.torihada.fanme.modules.fanme.controllers

import BaseController
import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.models.UserConsent
import jp.co.torihada.fanme.modules.fanme.usecases.consent.CreateUserConsentUseCase
import jp.co.torihada.fanme.modules.fanme.usecases.consent.GetConsentsUseCase

@ApplicationScoped
class UserConsentController : BaseController() {

    @Inject private lateinit var getConsentsUseCase: GetConsentsUseCase

    @Inject private lateinit var createUserConsentUseCase: CreateUserConsentUseCase

    fun getConsents(user: User): GetConsentsUseCase.Output {
        val input = GetConsentsUseCase.Input(user)
        return getConsentsUseCase.execute(input).getOrThrow()
    }

    @Transactional
    fun createUserConsent(user: User, consentName: String): UserConsent? {
        val input = CreateUserConsentUseCase.Input(user, consentName)
        return createUserConsentUseCase.execute(input).getOrThrow()
    }
}
