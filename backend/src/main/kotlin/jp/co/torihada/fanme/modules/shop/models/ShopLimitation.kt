package jp.co.torihada.fanme.modules.shop.models

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull

@PersistenceUnit(name = "shop_limitation")
@Entity
@Table(name = "shop_limitations")
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
class ShopLimitation : BaseModel() {
    @NotNull
    @OneToOne
    @JoinColumn(name = "shop_id", nullable = false, updatable = false)
    @JsonIgnore
    var shop: Shop = Shop()
    @NotNull @Column(name = "file_capacity", nullable = false) var fileCapacity: Int = 0
    @NotNull @Column(name = "file_quantity", nullable = false) var fileQuantity: Int = 0
    @NotNull
    @Column(name = "is_cheki_exhibitable", nullable = false)
    var isChekiExhibitable: Boolean = false

    /*
     * 各制限の値はデフォルト(ニュートラル)を0として定義する.
     * その制限を適用しない状態を-1として定義する.
     * 0が全ショップ(母数)に対して最も多くなるように各制限の値を設計すること.
     * 例. 出品可能な商品ファイル数(1以上は在設定されていないこの例のための架空の設定)
     *   -1: バリデーションなしで無制限にファイルをアップロード可能
     *    0: デフォルトのバリデーション(1商品30ファイルまで)
     *    1: 1商品40ファイルまで
     *    2: 1商品50ファイルまで
     */

    companion object : PanacheCompanion<ShopLimitation> {

        fun create(shopId: Long): ShopLimitation {
            val limitation = ShopLimitation().apply { shop = Shop.findById(shopId)!! }
            limitation.persist()
            return limitation
        }

        fun findByShopId(shopId: Long): ShopLimitation? {
            return find("shop.id", shopId).firstResult()
        }

        fun update(
            limitation: ShopLimitation,
            fileCapacity: Int,
            fileQuantity: Int,
            isChekiExhibitable: Boolean,
        ): ShopLimitation {
            limitation.fileCapacity = fileCapacity
            limitation.fileQuantity = fileQuantity
            limitation.isChekiExhibitable = isChekiExhibitable
            limitation.persist()
            return limitation
        }
    }
}
