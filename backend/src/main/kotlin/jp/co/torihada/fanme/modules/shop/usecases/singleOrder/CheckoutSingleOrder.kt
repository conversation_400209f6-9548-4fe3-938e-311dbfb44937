package jp.co.torihada.fanme.modules.shop.usecases.singleOrder

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import java.time.Instant
import jp.co.torihada.fanme.exception.*
import jp.co.torihada.fanme.modules.shop.Const.DIGITAL_GACHA_MULTI_PULL_COUNT
import jp.co.torihada.fanme.modules.shop.Const.DIGITAL_GACHA_SINGLE_PULL_COUNT
import jp.co.torihada.fanme.modules.shop.Const.PRINT_GACHA_MAX_PULL_COUNT
import jp.co.torihada.fanme.modules.shop.Const.PRINT_GACHA_MIN_PULL_COUNT
import jp.co.torihada.fanme.modules.shop.models.GachaReceivedFile
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.models.ItemFile
import jp.co.torihada.fanme.modules.shop.models.ItemType

@ApplicationScoped
class CheckoutSingleOrder {
    data class Input(val userUid: String, val itemId: Long, val quantity: Int)

    fun execute(params: Input): Result<Boolean, FanmeException> {
        val item = Item.findById(params.itemId) ?: return Err(ResourceNotFoundException("item"))

        // ショップが公開中であることを確認
        if (!item.shop.isOpen) return Err(ShopIsClosedException())

        // 商品が全て販売中であることを確認
        if (!item.available) return Err(ContainUnavailableItemException())

        // ファイルが存在するか確認
        val files = ItemFile.findByItemId(item.id!!)
        if (files.isEmpty()) return Err(ResourceNotFoundException("Item file"))

        val option = item.option ?: return Err(ResourceNotFoundException("option"))
        // 販売期間内であることを確認
        val now = Instant.now()
        if (option.forSaleStartAt != null && option.forSaleStartAt!!.isAfter(now))
            return Err(SaleNotStartedYetException())
        if (option.forSaleEndAt != null && option.forSaleEndAt!!.isBefore(now))
            return Err(SaleNotStartedYetException())

        // 購入上限を超えないことを確認
        if (option.qtyTotal != null) {
            val qtyTotal = option.qtyTotal
            if (qtyTotal!! > 0) {
                val purchasedCounts = 0
                if (qtyTotal!! < purchasedCounts + params.quantity)
                    return Err(OutOfStockException())
            }
        }

        return when (item.itemType) {
            ItemType.PRINT_GACHA -> validatePrintGachaPullCount(params.quantity)
            ItemType.DIGITAL_GACHA ->
                validateDigitalGachaPullCount(item, params.quantity, params.userUid, files)
            else -> Ok(true)
        }
    }

    private fun validatePrintGachaPullCount(quantity: Int): Result<Boolean, FanmeException> {
        return if (quantity < PRINT_GACHA_MIN_PULL_COUNT || quantity > PRINT_GACHA_MAX_PULL_COUNT) {
            Err(GachaInvalidPullCountException())
        } else {
            Ok(true)
        }
    }

    private fun validateDigitalGachaPullCount(
        item: Item,
        quantity: Int,
        userUid: String,
        files: List<ItemFile>,
    ): Result<Boolean, FanmeException> {
        val isDuplicated = item.gachaItem?.isDuplicated ?: false

        return if (isDuplicated) {
            if (
                quantity == DIGITAL_GACHA_SINGLE_PULL_COUNT ||
                    quantity == DIGITAL_GACHA_MULTI_PULL_COUNT
            ) {
                Ok(true)
            } else {
                Err(GachaInvalidPullCountException())
            }
        } else {
            // 重複なし: 以下3つすべてを満たす場合のみ許可
            // 1. 1回に買える回数は10回以下
            // 2. 1回・10回・remainingUniqueItemFileCountのいずれか
            // 3. 残りユニーク数（remainingUniqueItemFileCount）を超えない
            val userCollectedUniqueItemFileCount =
                GachaReceivedFile.findByPurchaserUidAndItemId(userUid, item.id!!)
                    .map { it?.itemFile?.id }
                    .distinct()
                    .size
            val totalUniqueItemFileCount = files.size
            val remainingUniqueItemFileCount =
                totalUniqueItemFileCount - userCollectedUniqueItemFileCount
            val isValidQuantity =
                quantity <= DIGITAL_GACHA_MULTI_PULL_COUNT &&
                    (quantity == DIGITAL_GACHA_SINGLE_PULL_COUNT ||
                        quantity == DIGITAL_GACHA_MULTI_PULL_COUNT ||
                        quantity == remainingUniqueItemFileCount) &&
                    quantity <= remainingUniqueItemFileCount

            if (isValidQuantity) {
                Ok(true)
            } else {
                Err(GachaInvalidPullCountException())
            }
        }
    }
}
