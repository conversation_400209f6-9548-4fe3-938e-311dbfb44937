package jp.co.torihada.fanme.modules.fanme.usecases.consent

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.time.Instant
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.models.UserConsent
import jp.co.torihada.fanme.modules.shop.Config

@ApplicationScoped
class GetConsentsUseCase {

    @Inject private lateinit var config: Config

    data class Input(val user: User)

    data class Output(val consents: List<Consent>)

    data class Consent(val name: String, val url: String, val required: Boolean)

    fun execute(params: Input): Result<Output, FanmeException> {
        val requiredConsents = getRequiredConsent(params.user)
        val existingConsents = UserConsent.findByUser(params.user).map { it.consent }.toSet()

        val consents =
            UserConsent.Consent.entries.map { consent ->
                val needsConsent = (consent in requiredConsents) && (consent !in existingConsents)
                Consent(
                    name = consent.name,
                    url = "${config.shopFrontUrl()}${consent.path}",
                    required = needsConsent,
                )
            }

        return Ok(Output(consents = consents))
    }

    private fun getRequiredConsent(user: User): List<UserConsent.Consent> {
        return UserConsent.Consent.entries.filter { consent ->
            when (consent) {
                UserConsent.Consent.TERMS_20250801 -> isTerms20250801Required(user)
            }
        }
    }

    private fun isTerms20250801Required(user: User): Boolean {
        return user.createdAt?.isBefore(Instant.parse("2025-07-31T15:00:00Z")) ?: false
    }
}
