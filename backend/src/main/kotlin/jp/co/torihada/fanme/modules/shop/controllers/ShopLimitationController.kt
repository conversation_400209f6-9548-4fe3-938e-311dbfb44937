package jp.co.torihada.fanme.modules.shop.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.modules.shop.models.ShopLimitation
import jp.co.torihada.fanme.modules.shop.usecases.shopLimitation.UpdateShopLimitation

@ApplicationScoped
class ShopLimitationController
@Inject
constructor(private val updateShopLimitation: UpdateShopLimitation) {

    @Transactional
    fun updateShopLimitation(
        input: UpdateShopLimitation.InputUpdateShopLimitation
    ): ShopLimitation {
        return updateShopLimitation.execute(input).getOrThrow()
    }
}
