package jp.co.torihada.fanme.endpoints.console

import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.PathParam
import jp.co.torihada.fanme.dto.BaseResponseBody
import jp.co.torihada.fanme.exception.ErrorObject
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.controllers.AuditObjectController
import jp.co.torihada.fanme.modules.fanme.models.AuditObject
import org.eclipse.microprofile.openapi.annotations.tags.Tag

// レスポンスDTO
class AuditObjectsData(val auditObjects: List<AuditObject>)

class AuditObjectsResponse(data: AuditObjectsData, errors: List<ErrorObject> = emptyList()) :
    BaseResponseBody<AuditObjectsData>(data, errors)

@Path("/console/audit-groups/{audit-group-id}/audit-objects")
@Tag(name = "CONSOLE", description = "CONSOLE APIサーバー")
class AuditObjectEndpoint {
    @Inject private lateinit var handler: AuditObjectController

    @GET
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE)
    fun getAuditObjects(@PathParam("audit-group-id") auditGroupId: Long): AuditObjectsResponse {
        val auditObjects = handler.getAuditObjects(auditGroupId)
        return AuditObjectsResponse(data = AuditObjectsData(auditObjects = auditObjects))
    }
}
