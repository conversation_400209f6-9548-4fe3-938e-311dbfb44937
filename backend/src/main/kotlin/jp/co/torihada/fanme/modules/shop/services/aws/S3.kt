package jp.co.torihada.fanme.modules.shop.services.aws

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import io.quarkus.logging.Log
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.nio.file.Path
import java.time.Duration
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.shop.Config
import jp.co.torihada.fanme.modules.shop.Util
import org.jboss.logging.Logger
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest

@ApplicationScoped
class S3 {

    @Inject private lateinit var s3Presigner: S3Presigner

    @Inject private lateinit var s3Client: S3Client

    @Inject private lateinit var config: Config

    @Inject private lateinit var logger: Logger

    fun checkFileExists(fileId: Long): String {
        return "file_exists"
    }

    fun getPresignedUrl(key: String, type: Util.PresignedUrlType, name: String?): String {
        val bucket = config.s3BucketName()
        try {
            when (type) {
                Util.PresignedUrlType.DOWNLOAD -> {
                    val encodedFileName = URLEncoder.encode(name, StandardCharsets.UTF_8.toString())
                    val request =
                        GetObjectPresignRequest.builder()
                            .signatureDuration(Duration.ofMinutes(5))
                            .getObjectRequest(
                                if (name == null)
                                    GetObjectRequest.builder().bucket(bucket).key(key).build()
                                else
                                    GetObjectRequest.builder()
                                        .bucket(bucket)
                                        .key(key)
                                        .responseContentDisposition(
                                            "attachment; filename=\"$encodedFileName\"; filename*=UTF-8''$encodedFileName"
                                        )
                                        .build()
                            )
                            .build()
                    return s3Presigner.presignGetObject(request).url().toString()
                }
                Util.PresignedUrlType.UPLOAD -> {
                    val request =
                        PutObjectPresignRequest.builder()
                            .signatureDuration(Duration.ofMinutes(10))
                            .putObjectRequest { it.bucket(bucket).key(key) }
                            .build()
                    return s3Presigner.presignPutObject(request).url().toString()
                }
                Util.PresignedUrlType.GET_OBJECT -> {
                    val request =
                        GetObjectPresignRequest.builder()
                            .signatureDuration(Duration.ofDays(1))
                            .getObjectRequest(
                                GetObjectRequest.builder().bucket(bucket).key(key).build()
                            )
                            .build()
                    return s3Presigner.presignGetObject(request).url().toString()
                }
            }
        } catch (e: Exception) {
            throw FanmeException(1000, e.message ?: "Failed to get presigned url")
        }
    }

    fun getObjectUri(fileUri: String): String {
        val s3BucketPrefix = "${config.s3Endpoint()}/${config.s3BucketName()}"
        if (fileUri.startsWith(s3BucketPrefix)) {
            if (fileUri.startsWith("/public/")) {
                return fileUri
            }
            val fileUriWithoutParams = fileUri.split("?")[0]
            val objectKey = fileUriWithoutParams.replace("$s3BucketPrefix/", "")
            return getPresignedUrl(objectKey, Util.PresignedUrlType.GET_OBJECT, null)
        } else {
            return fileUri
        }
    }

    fun putObject(
        bucketName: String,
        remotePath: String,
        contentType: String,
        fileToUpload: Path,
    ): Result<String, Exception> {
        return try {
            val putRequest =
                PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(remotePath)
                    .metadata(mapOf("Content-Type" to contentType))
                    .build()
            s3Client.putObject(putRequest, fileToUpload)

            Ok(remotePath)
        } catch (e: Exception) {
            Log.error("Failed to save file to S3", e)
            Err(e)
        }
    }

    fun getObject(bucketName: String, key: String): ByteArray? {
        return try {
            val getRequest = GetObjectRequest.builder().bucket(bucketName).key(key).build()

            s3Client.getObject(getRequest).use { response -> response.readAllBytes() }
        } catch (e: Exception) {
            logger.error("Failed to get object from S3: bucket=$bucketName, key=$key", e)
            null
        }
    }
}
