package jp.co.torihada.fanme.modules.shop.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.exception.InvalidParameterException
import jp.co.torihada.fanme.modules.shop.models.ItemType
import jp.co.torihada.fanme.modules.shop.models.ShopItemTypeMarginRate
import jp.co.torihada.fanme.modules.shop.usecases.shop.UpdateShopItemTypeMarginRates

@ApplicationScoped
class MarginRateController {
    @Inject private lateinit var updateShopItemTypeMarginRatesUsecase: UpdateShopItemTypeMarginRates

    @Transactional
    fun updateShopItemTypeMarginRates(
        shopId: Long,
        items: List<Pair<String, Float>>,
    ): List<ShopItemTypeMarginRate> {
        val parsedItems: List<Pair<ItemType, Float>> =
            items.map { (typeStr, rate) ->
                val itemType =
                    try {
                        ItemType.valueOf(typeStr)
                    } catch (e: IllegalArgumentException) {
                        throw InvalidParameterException("Invalid itemType: $typeStr")
                    }
                itemType to rate
            }

        return updateShopItemTypeMarginRatesUsecase
            .execute(UpdateShopItemTypeMarginRates.Input(shopId = shopId, items = parsedItems))
            .getOrThrow()
    }
}
