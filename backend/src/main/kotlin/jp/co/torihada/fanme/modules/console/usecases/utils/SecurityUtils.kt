package jp.co.torihada.fanme.modules.console.usecases.utils

import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ForbiddenAccessException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.fanme.models.User

@ApplicationScoped
class SecurityUtils {

    fun validateAgentAccess(agencyId: Long, currentUserUid: String, currentUserRole: String?) {
        val agency = Agency.findById(agencyId)
        if (agency == null || agency.deletedAt != null) {
            throw ResourceNotFoundException("Agency")
        }

        val currentConsoleUser = ConsoleUser.findByUserUid(currentUserUid)
        if (currentUserRole == UserRole.AGENT_VALUE) {
            if (currentConsoleUser?.agencyId != agencyId) {
                throw ForbiddenAccessException()
            }
        }
    }

    fun validateAgentForUserAccess(
        targetUser: User,
        currentUserRole: String?,
        currentUserUid: String?,
    ) {
        if (currentUserRole == UserRole.AGENT_VALUE && currentUserUid != null) {
            val currentConsoleUser = ConsoleUser.findByUserUid(currentUserUid)
            val targetConsoleUser = ConsoleUser.findByUserId(targetUser.id!!)

            if (currentConsoleUser?.agencyId == null || targetConsoleUser?.agencyId == null) {
                throw ForbiddenAccessException("User not affiliated with any agency")
            }

            if (currentConsoleUser.agencyId != targetConsoleUser.agencyId) {
                throw ForbiddenAccessException("Access denied to users from different agency")
            }
        }
    }

    fun validateCreatorForCreatorAccess(
        targetUser: User,
        currentUserRole: String?,
        currentUserUid: String?,
    ) {
        if (currentUserRole == UserRole.CREATOR_VALUE && currentUserUid != null) {
            if (targetUser.uid != currentUserUid) {
                throw ForbiddenAccessException("Creators can only access their own account")
            }
        }
    }
}
