package jp.co.torihada.fanme.modules.shop.usecases.shopLimitation

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.models.ShopLimitation

@ApplicationScoped
class UpdateShopLimitation {

    data class InputUpdateShopLimitation(
        val shopId: Long,
        val fileCapacity: Int,
        val fileQuantity: Int,
        val isChekiExhibitable: <PERSON>olean,
    )

    fun execute(input: InputUpdateShopLimitation): Result<ShopLimitation, FanmeException> {

        val shopLimitation =
            ShopLimitation.findByShopId(input.shopId)
                ?: throw ResourceNotFoundException("ShopLimitation")

        val updatedShopLimitation =
            ShopLimitation.update(
                limitation = shopLimitation,
                fileCapacity = input.fileCapacity,
                fileQuantity = input.fileQuantity,
                isChekiExhibitable = input.isChekiExhibitable,
            )

        return Ok(updatedShopLimitation)
    }
}
