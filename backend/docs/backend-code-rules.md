# 統合環境バックエンドコーディングルール

## モジュール間の依存関係とInterface

下記の方向のみモジュール間の依存関係を許容する
- fanme -> payment
- shop -> fanme
- shop -> payment
- console -> 他のすべてのモジュール

また、モジュール間の呼び出しは呼出元の`usecase`層から呼出先の`controller`層を呼び出すこと
上記依存関係と逆方向の呼び出しが必要な場合は、エンドポイントを介したAPI通信で呼び出すこと

## DI
各層の依存関係およびロガーはDIで注入する  
注入されるクラスには基本的に`@ApplicationScoped`アノテーションをつける
```kotlin
// endpoint
class CartItemEndpoint : BaseCreatorAccountIdentityEndpoint() {

    @Inject private lateinit var handler: CartItemController
    
    @Inject private lateinit var securityIdentity: SecurityIdentity
    @Inject private lateinit var requestContext: ContainerRequestContext

    @Inject private lateinit var logger: Logger
    @Inject private lateinit var util: Util
    ...
}
```

```kotlin
// controller
class CartItemController {

    @Inject private lateinit var getCartItems: GetCartItems
    @Inject private lateinit var createCartItem: CreateCartItem
    @Inject private lateinit var updateCartItem: UpdateCartItem
    @Inject private lateinit var deleteCartItem: DeleteCartItem
    ...
}
```

## 定数と定数クラス

- 定数および定数クラス(`enum class`)は`Const.kt`に書く
  - 特定のモジュールの`model`で利用する定数クラス → そのモジュールの`Const.kt`
  - 複数のモジュールの`model`で利用する定数クラス → ルートの`Const.kt`
- 許可される依存関係の中で、定数クラスはモジュールを跨いで参照して良い
- モジュールを跨ぐ`usecase→controller`の呼び出し時にはプリミティブ型ではなく定数クラスを使う
- プリミティブ型→定数クラスの型変換は`endpoint`層で行う

## 定義された例外とコードの採番ルール

例外は`Exception.kt`にて、`FanmeException`を継承して作成する

また、エラーコードは下記に従うって採番すること
1. 共通 - 0xxx
2. fanme - 1xxx
3. shop - 2xxx
4. payment - 3xxx
5. console - 4xxx

## エンドポイントの返り値の型
OpenAPIの生成のためにエンドポイントには下記のように`BaseResponseBody`を継承した型を返り値の型として定義する

```kotlin
    data class EndpointResponse(
        override val data: T,
        override val errors: List<E>,
    ) : BaseResponseBody<T>(data = data, errors = errors) {
        data class T(val data: K)
    }

    @GET
    @Path("/current")
    @RolesAllowed("LoginUser")
    @Produces(MediaType.APPLICATION_JSON)
    fun getEndpoint(): EndpointResponse {
        val user = util.getCurrentUser(securityIdentity) ?: throw UnAuthorizedException()
        val data = handler.get(userUuid)
        return EndpointResponse(
            data = result(data = data),
            errors = emptyList(),
        )
    }
```
## User情報の取得

- クリエイター情報(訪問先ページの持ち主)
  - 格納: `RequestFilter`で取得して`RequestContext`に埋め込む
  - 取得: `endpoint`層で`util.getCreator()`を利用して取得する
- ログインユーザー
  - 格納: `SelfRoleAugmentor`で取得し`SecurityIdentity`に埋め込む
  - 取得: `endopoint`層で`util.util.getCurrentUser()`を利用して取得する
- それぞれ取得したユーザー情報はそのまま`controller`層へ渡す

例
```kotlin
@Inject lateinit var securityIdentity: SecurityIdentity
@Inject lateinit var requestContext: ContainerRequestContext

fun getCartItems(): Response {
    val creator = util.getCreator(requestContext)
    val currentUser = util.getCurrentUser(securityIdentity) ?: throw UnAuthorizedException()
    val result = handler.getCartItems(currentUser, creator)
        ...
}
```
## トランザクション

- DBに対するトランザクションの管理は`@Transactional`アノテーションを利用する
- `@Transactional`アノテーションはcontroller層およびservice層でのみ利用してよい
- トランザクションが複数のデータソースにまたがる場合
  - 親トランザクション(依存関係で呼出元となるcontroller)では`@Transactional`を利用する
  - 子トランザクション(依存関係で呼出先となるcontroller)では`@Transactional(Transactional.TxType.REQUIRES_NEW)`を利用する

## usecase層の返り値の型とcontroller層での処理

- `usecase`層は必ず`execute`関数を持ち、それが呼び出されることで`usecase`を実行する
- `execute`関数は必ず`Result<T, FanmeException>`を返り値の型(`T`は任意の型)にとる
- `controller`は`usecase`からの返り値を受け取るとき、`Usecase.execute().getOrThrow()`として発生した例外を`throw`する
  - ただし、例外に対して`controller`で処理を行いたい場合はその限りではない

例

```kotlin
// controller
@Inject private lateinit var getShop: GetShop

fun getShop(creatorUid: String): Shop {
    return getShop.execute(GetShop.Input(creatorUid)).getOrThrow()
}
```

```kotlin
// usecase
fun execute(params: Input): Result<Shop, FanmeException> {
    val shop = Shop.findByCreatorUid(params.creatorUid) ?: return Err(ResourceNotFoundException("Shop"))
    return Ok(shop)
}
```