GRADLE = ./gradlew
MYSQL_FANME = docker exec fanme-api-db mysql -u root -ppassword
MYSQL_PAYMENT = docker exec fanme-payment-db mysql -u root -ppass

.DEFAULT_GOAL := help

.PHONY: help
help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-20s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

.PHONY: test
test: ## Run tests
	$(GRADLE) test

.PHONY: test-console
test-console: ## Run tests for console
	$(GRADLE) test --tests "jp.co.torihada.fanme.modules.console.*"

.PHONY: test-payment
test-payment: ## Run tests for payment module
	$(GRADLE) test --tests "jp.co.torihada.fanme.modules.payment.*"

.PHONY: test-shop
test-shop: ## Run tests for shop module
	$(GRADLE) test --tests "jp.co.torihada.fanme.modules.shop.*"

.PHONY: test-fanme
test-fanme: ## Run tests for fanme module
	$(GRADLE) test --tests "jp.co.torihada.fanme.modules.fanme.*"

.PHONY: test-arch
test-arch: ## Run architecture tests and other non-module tests
	$(GRADLE) test --tests "jp.co.torihada.fanme.*" \
		--tests "!jp.co.torihada.fanme.modules.shop.*" \
		--tests "!jp.co.torihada.fanme.modules.payment.*" \
		--tests "!jp.co.torihada.fanme.modules.console.*" \
		--tests "!jp.co.torihada.fanme.modules.fanme.*"

.PHONY: drop
drop: ## Drop all databases
	@echo "Dropping databases..."
	@$(MYSQL_FANME) -e "DROP DATABASE IF EXISTS fanme_api;" 2>/dev/null || true
	@$(MYSQL_PAYMENT) -e "DROP DATABASE IF EXISTS payment;" 2>/dev/null || true
	@echo "Databases dropped."

.PHONY: create
create: ## Create all databases
	@echo "Creating databases..."
	@$(MYSQL_FANME) -e "CREATE DATABASE IF NOT EXISTS fanme_api;" 2>/dev/null || true
	@$(MYSQL_PAYMENT) -e "CREATE DATABASE IF NOT EXISTS payment;" 2>/dev/null || true
	@echo "Databases created."

.PHONY: seed
seed: ## Load seed data into databases
	@echo "Loading seed data..."
	@cat src/main/resources/seed/import-fanme-dev.sql | docker exec -i fanme-api-db mysql -u root -ppassword --default-character-set=utf8mb4 fanme_api 2>/dev/null || true
	@cat src/main/resources/seed/import-payment-dev.sql | docker exec -i fanme-payment-db mysql -u root -ppass payment 2>/dev/null || true
	@cat src/main/resources/seed/import-shop-dev.sql | docker exec -i fanme-shop-db mysql -u root -ppass --default-character-set=utf8mb4 shop 2>/dev/null || true
	@echo "Seed data loaded."

.PHONY: seed-transaction
seed-transaction: seed ## Load seed data plus massive test data
	@echo "Loading massive test data..."
	@cat src/main/resources/seed/generate-buyer-creators-test-data.sql | docker exec -i fanme-api-db mysql -u root -ppassword --default-character-set=utf8mb4 fanme_api 2>/dev/null || true
	@cat src/main/resources/seed/generate-yearly-transaction-test-data.sql | docker exec -i fanme-payment-db mysql -u root -ppass --default-character-set=utf8mb4 payment 2>/dev/null || true
	@echo "Massive test data loaded."

.PHONY: reset-transaction
reset-transaction: ## Reset massive test data (delete all test buyers and their transactions)
	@echo "Resetting massive test data..."
	@docker exec -i fanme-api-db mysql -u root -ppassword --default-character-set=utf8mb4 fanme_api -e "DELETE FROM creators WHERE uid LIKE 'buyer-%' OR uid LIKE 'test-purchaser-%';" 2>/dev/null || true
	@docker exec -i fanme-payment-db mysql -u root -ppass --default-character-set=utf8mb4 payment -e "DELETE FROM purchasers WHERE user_id LIKE 'buyer-%' OR user_id LIKE 'test-purchaser-%';" 2>/dev/null || true
	@docker exec -i fanme-payment-db mysql -u root -ppass --default-character-set=utf8mb4 payment -e "DELETE FROM transactions WHERE purchaser_user_id LIKE 'buyer-%' OR purchaser_user_id LIKE 'test-purchaser-%';" 2>/dev/null || true
	@echo "Massive test data reset complete."

.PHONY: db-status
db-status: ## Check database status
	@echo "=== Fanme API Database ==="
	@$(MYSQL_FANME) fanme_api -e "SELECT COUNT(*) as count, 'agencies' as table_name FROM agencies UNION SELECT COUNT(*), 'creators' FROM creators UNION SELECT COUNT(*), 'console_users' FROM console_users;" 2>/dev/null || echo "Error: Could not connect to fanme_api database"
	@echo ""
	@echo "=== Payment Database ==="
	@$(MYSQL_PAYMENT) payment -e "SELECT COUNT(*) as count, 'monthly_seller_sales' as table_name FROM monthly_seller_sales UNION SELECT COUNT(*), 'seller_account_balances' FROM seller_account_balances;" 2>/dev/null || echo "Error: Could not connect to payment database"

.PHONY: format
format: ## Format code
	$(GRADLE) ktfmtFormat
	$(GRADLE) detekt

compile: ## Compile the project
	$(GRADLE) compileKotlin

compile-test: ## Compile test sources
	$(GRADLE) compileTestKotlin
